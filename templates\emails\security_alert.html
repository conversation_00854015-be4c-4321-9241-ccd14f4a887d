{% extends "emails/base.html" %}

{% block title %}Security Alert - CVBioLabs{% endblock %}

{% block content %}
<div class="greeting">
    Security Alert Notification
</div>

<div class="content-text">
    This is an automated security alert from the CVBioLabs monitoring system. A security event has been detected that requires immediate attention.
</div>

<div class="warning-box" style="text-align: center; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border: 2px solid #dc3545; border-radius: 12px;">
    <div style="font-size: 20px; color: #721c24; margin-bottom: 15px; font-weight: 600;">
        <i class="fas fa-exclamation-triangle" style="color: #dc3545; font-size: 28px; margin-right: 10px;"></i>
        {{ alert_type }} Detected
    </div>
    <div style="font-size: 16px; color: #721c24; margin-bottom: 10px;">
        <strong>{{ alert_message }}</strong>
    </div>
    <div style="font-size: 14px; color: #721c24;">
        <i class="fas fa-clock"></i> {{ timestamp.strftime('%B %d, %Y at %I:%M %p UTC') }}
    </div>
</div>

<div class="content-text">
    <strong>Alert Details:</strong>
</div>

<table style="width: 100%; border-collapse: collapse; margin: 20px 0; background: #f8f9fa; border-radius: 8px; overflow: hidden;">
    <tr style="background: #e9ecef;">
        <td style="padding: 12px 15px; font-weight: 600; color: #002f6c; border-bottom: 1px solid #dee2e6;">Alert Type</td>
        <td style="padding: 12px 15px; border-bottom: 1px solid #dee2e6;">{{ alert_type }}</td>
    </tr>
    <tr>
        <td style="padding: 12px 15px; font-weight: 600; color: #002f6c; border-bottom: 1px solid #dee2e6;">Timestamp</td>
        <td style="padding: 12px 15px; border-bottom: 1px solid #dee2e6;">{{ timestamp.strftime('%Y-%m-%d %H:%M:%S UTC') }}</td>
    </tr>
    <tr style="background: #e9ecef;">
        <td style="padding: 12px 15px; font-weight: 600; color: #002f6c; border-bottom: 1px solid #dee2e6;">Message</td>
        <td style="padding: 12px 15px; border-bottom: 1px solid #dee2e6;">{{ alert_message }}</td>
    </tr>
    {% if alert_details %}
    {% for key, value in alert_details.items() %}
    <tr{% if loop.index % 2 == 0 %} style="background: #e9ecef;"{% endif %}>
        <td style="padding: 12px 15px; font-weight: 600; color: #002f6c; border-bottom: 1px solid #dee2e6;">{{ key|title }}</td>
        <td style="padding: 12px 15px; border-bottom: 1px solid #dee2e6;">{{ value }}</td>
    </tr>
    {% endfor %}
    {% endif %}
</table>

<div class="warning-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-shield-alt"></i> Immediate Actions Required
    </div>
    <ul style="margin: 0; padding-left: 20px;">
        <li><strong>Investigate:</strong> Review the security logs and system activity immediately</li>
        <li><strong>Assess Impact:</strong> Determine if any data or systems have been compromised</li>
        <li><strong>Secure Systems:</strong> Take appropriate measures to secure affected systems</li>
        <li><strong>Document:</strong> Record all findings and actions taken</li>
        <li><strong>Report:</strong> Escalate to senior management if necessary</li>
    </ul>
</div>

<div class="info-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-tools"></i> Recommended Response Steps
    </div>
    <ol style="margin: 0; padding-left: 20px;">
        <li>Log into the CVBioLabs admin dashboard immediately</li>
        <li>Review security logs and recent system activity</li>
        <li>Check for any unauthorized access or suspicious behavior</li>
        <li>Verify the integrity of critical data and systems</li>
        <li>Implement additional security measures if needed</li>
        <li>Monitor systems closely for the next 24-48 hours</li>
    </ol>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ login_url }}" class="btn" style="background-color: #dc3545; border-color: #dc3545; font-size: 18px; padding: 18px 40px;">
        <i class="fas fa-sign-in-alt"></i> Access Admin Dashboard
    </a>
</div>

<div class="warning-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-phone"></i> Emergency Contacts
    </div>
    <div>
        If this is a critical security incident, contact the following immediately:
        <ul style="margin: 10px 0 0 20px;">
            <li><strong>IT Security Team:</strong> <a href="mailto:<EMAIL>" style="color: #f47c20; text-decoration: none;"><EMAIL></a></li>
            <li><strong>Emergency Hotline:</strong> +91-XXXXXXXXXX (24/7)</li>
            <li><strong>System Administrator:</strong> <a href="mailto:<EMAIL>" style="color: #f47c20; text-decoration: none;"><EMAIL></a></li>
            <li><strong>Management:</strong> <a href="mailto:<EMAIL>" style="color: #f47c20; text-decoration: none;"><EMAIL></a></li>
        </ul>
    </div>
</div>

<div class="info-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-info-circle"></i> About This Alert
    </div>
    <ul style="margin: 0; padding-left: 20px;">
        <li>This alert was generated automatically by the CVBioLabs security monitoring system</li>
        <li>All security events are logged and tracked for compliance purposes</li>
        <li>This email is sent to authorized security personnel only</li>
        <li>Do not forward this email to unauthorized individuals</li>
    </ul>
</div>

<div class="content-text">
    <strong>System Information:</strong>
</div>

<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; font-family: 'Courier New', monospace; font-size: 12px;">
    <div><strong>Alert ID:</strong> {{ alert_details.get('alert_id', 'N/A') }}</div>
    <div><strong>Source System:</strong> CVBioLabs Security Monitor</div>
    <div><strong>Environment:</strong> {{ alert_details.get('environment', 'Production') }}</div>
    <div><strong>Server:</strong> {{ alert_details.get('server', 'N/A') }}</div>
    <div><strong>User Agent:</strong> {{ alert_details.get('user_agent', 'N/A') }}</div>
    <div><strong>IP Address:</strong> {{ alert_details.get('ip_address', 'N/A') }}</div>
</div>

<div class="warning-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-exclamation-triangle"></i> Important Security Notice
    </div>
    <div>
        <strong>This is a critical security alert.</strong> Immediate action is required to investigate and respond to this security event. Failure to respond promptly may result in:
        <ul style="margin: 10px 0 0 20px;">
            <li>Potential data breaches or unauthorized access</li>
            <li>System compromise or service disruption</li>
            <li>Compliance violations and regulatory issues</li>
            <li>Damage to company reputation and customer trust</li>
        </ul>
    </div>
</div>

<div class="content-text" style="margin-top: 30px;">
    This alert requires immediate attention. Please investigate and respond according to your organization's security incident response procedures.
</div>

<div class="content-text" style="color: #002f6c; font-weight: 600;">
    CVBioLabs Security Monitoring System<br>
    Automated Alert - Do Not Reply
</div>
{% endblock %}
