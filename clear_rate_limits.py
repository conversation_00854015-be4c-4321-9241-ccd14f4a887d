#!/usr/bin/env python3
"""
Script to clear rate limits for development/testing purposes
"""

import os
import sys
import redis
from dotenv import load_dotenv

load_dotenv()

def clear_rate_limits():
    """Clear all rate limits from Redis or memory storage"""
    
    # Check if we're in development mode
    if os.getenv('FLASK_ENV') != 'development':
        print("❌ This script can only be run in development mode!")
        print("Set FLASK_ENV=development in your .env file")
        return False
    
    redis_url = os.getenv('REDIS_URL')
    
    if redis_url:
        try:
            # Connect to Redis
            r = redis.from_url(redis_url)
            
            # Get all rate limit keys (Flask-Limiter uses LIMITER prefix by default)
            keys = r.keys('LIMITER*')
            
            if keys:
                # Delete all rate limit keys
                deleted = r.delete(*keys)
                print(f"✅ Cleared {deleted} rate limit entries from Redis")
            else:
                print("ℹ️  No rate limit entries found in Redis")
                
            return True
            
        except Exception as e:
            print(f"❌ Error connecting to Redis: {e}")
            return False
    else:
        print("ℹ️  Using memory storage for rate limits")
        print("ℹ️  Rate limits will be cleared when the Flask app restarts")
        print("💡 To clear immediately, restart your Flask application")
        return True

def main():
    """Main function"""
    print("🔧 CVBioLabs Rate Limit Cleaner")
    print("=" * 40)
    
    if len(sys.argv) > 1 and sys.argv[1] == '--force':
        print("⚠️  Force mode enabled")
    elif os.getenv('FLASK_ENV') == 'production':
        print("❌ Cannot run in production mode!")
        print("This script is for development only")
        sys.exit(1)
    
    success = clear_rate_limits()
    
    if success:
        print("\n✅ Rate limits cleared successfully!")
        print("💡 You can now try logging in again")
    else:
        print("\n❌ Failed to clear rate limits")
        print("💡 Try restarting your Flask application instead")

if __name__ == '__main__':
    main()
