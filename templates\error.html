<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error {{ error_code }} - CVBioLabs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .error-card {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
        .error-code {
            font-size: 4rem;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 2rem;
        }
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: transform 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            color: white;
        }
        .error-id {
            font-size: 0.8rem;
            color: #adb5bd;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <div class="error-code">{{ error_code }}</div>
            <div class="error-message">{{ error_message }}</div>
            
            {% if error_code == 404 %}
                <p class="text-muted">The page you're looking for doesn't exist.</p>
            {% elif error_code == 403 %}
                <p class="text-muted">You don't have permission to access this resource.</p>
            {% elif error_code == 500 %}
                <p class="text-muted">Something went wrong on our end. We're working to fix it.</p>
            {% endif %}
            
            <a href="{{ url_for('home') }}" class="btn-home">
                <i class="fas fa-home"></i> Go Home
            </a>
            
            {% if error_id %}
                <div class="error-id">
                    Error ID: {{ error_id }}
                </div>
            {% endif %}
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
