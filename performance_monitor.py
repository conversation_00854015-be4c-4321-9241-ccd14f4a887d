#!/usr/bin/env python3
"""
Performance monitoring for CVBioLabs application
This module provides tools to monitor and analyze application performance
"""

import time
import logging
from functools import wraps
from datetime import datetime
import json
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/performance.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Performance monitoring class"""
    
    def __init__(self):
        self.metrics = {}
        self.slow_queries = []
        self.slow_query_threshold = 1.0  # seconds
    
    def time_function(self, func_name=None):
        """Decorator to time function execution"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    execution_time = end_time - start_time
                    
                    function_name = func_name or f"{func.__module__}.{func.__name__}"
                    self.record_metric(function_name, execution_time)
                    
                    if execution_time > self.slow_query_threshold:
                        self.record_slow_query(function_name, execution_time, args, kwargs)
                    
                    logger.info(f"Function {function_name} executed in {execution_time:.4f} seconds")
            
            return wrapper
        return decorator
    
    def record_metric(self, name, execution_time):
        """Record performance metric"""
        if name not in self.metrics:
            self.metrics[name] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'avg_time': 0
            }
        
        metric = self.metrics[name]
        metric['count'] += 1
        metric['total_time'] += execution_time
        metric['min_time'] = min(metric['min_time'], execution_time)
        metric['max_time'] = max(metric['max_time'], execution_time)
        metric['avg_time'] = metric['total_time'] / metric['count']
    
    def record_slow_query(self, name, execution_time, args, kwargs):
        """Record slow query for analysis"""
        slow_query = {
            'timestamp': datetime.now().isoformat(),
            'function': name,
            'execution_time': execution_time,
            'args_count': len(args) if args else 0,
            'kwargs_count': len(kwargs) if kwargs else 0
        }
        
        self.slow_queries.append(slow_query)
        logger.warning(f"Slow query detected: {name} took {execution_time:.4f} seconds")
        
        # Keep only last 100 slow queries
        if len(self.slow_queries) > 100:
            self.slow_queries = self.slow_queries[-100:]
    
    def get_performance_report(self):
        """Generate performance report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'metrics': self.metrics,
            'slow_queries_count': len(self.slow_queries),
            'recent_slow_queries': self.slow_queries[-10:] if self.slow_queries else []
        }
        return report
    
    def save_report(self, filename=None):
        """Save performance report to file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'logs/performance_report_{timestamp}.json'
        
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w') as f:
            json.dump(self.get_performance_report(), f, indent=2)
        
        logger.info(f"Performance report saved to {filename}")
    
    def reset_metrics(self):
        """Reset all metrics"""
        self.metrics = {}
        self.slow_queries = []
        logger.info("Performance metrics reset")

# Global performance monitor instance
perf_monitor = PerformanceMonitor()

# Convenience functions
def time_function(func_name=None):
    """Decorator to time function execution"""
    return perf_monitor.time_function(func_name)

def get_performance_report():
    """Get current performance report"""
    return perf_monitor.get_performance_report()

def save_performance_report(filename=None):
    """Save performance report to file"""
    return perf_monitor.save_report(filename)

# Database query timing decorator
def time_db_query(query_name=None):
    """Decorator specifically for database queries"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                execution_time = end_time - start_time
                
                name = query_name or f"db_query_{func.__name__}"
                perf_monitor.record_metric(name, execution_time)
                
                if execution_time > 0.5:  # Lower threshold for DB queries
                    logger.warning(f"Slow database query: {name} took {execution_time:.4f} seconds")
        
        return wrapper
    return decorator

# Example usage:
# 
# @time_function("search_tests_api")
# def search_tests():
#     # Your function code here
#     pass
#
# @time_db_query("get_all_tests")
# def get_tests_from_db():
#     # Your database query code here
#     pass
