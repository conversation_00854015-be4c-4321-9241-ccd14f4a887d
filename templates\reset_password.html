<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - CVBioLabs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --dark-blue: #002f6c;
            --orange: #f47c20;
            --light-blue: #e6f7ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--light-blue) 0%, white 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 2rem;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h2 {
            color: var(--dark-blue);
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
        }

        .toggle-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            background: var(--light-blue);
            padding: 5px;
            border-radius: 25px;
        }

        .toggle-btn {
            padding: 10px 25px;
            border: none;
            background: none;
            color: var(--dark-blue);
            font-weight: 500;
            cursor: pointer;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .toggle-btn.active {
            background: var(--orange);
            color: white;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            color: var(--dark-blue);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--orange);
        }

        /* Password input container for positioning eye icon */
        .password-input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .password-input-container input {
            padding-right: 45px; /* Make space for eye icon */
        }

        /* Eye toggle button styling */
        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: #666;
            font-size: 1.1rem;
            z-index: 10;
        }

        .password-toggle:hover {
            background-color: var(--light-blue);
            color: var(--orange);
            transform: translateY(-50%) scale(1.1);
        }

        .password-toggle:active {
            transform: translateY(-50%) scale(0.95);
        }

        /* Eye icon animations */
        .password-toggle i {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .password-toggle.visible i {
            color: var(--orange);
            transform: scale(1.1);
        }

        .password-toggle.visible:hover i {
            color: var(--dark-blue);
        }

        /* Smooth icon transition animation */
        @keyframes eyeOpen {
            0% { transform: scaleY(0.1); opacity: 0.5; }
            50% { transform: scaleY(0.5); opacity: 0.7; }
            100% { transform: scaleY(1); opacity: 1; }
        }

        @keyframes eyeClose {
            0% { transform: scaleY(1); opacity: 1; }
            50% { transform: scaleY(0.5); opacity: 0.7; }
            100% { transform: scaleY(0.1); opacity: 0.5; }
        }

        .password-toggle.animating-open i {
            animation: eyeOpen 0.3s ease-out;
        }

        .password-toggle.animating-close i {
            animation: eyeClose 0.3s ease-out;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 1rem;
        }

        .checkbox-group label {
            color: var(--dark-blue);
            font-size: 0.9rem;
        }

        .forgot-password {
            display: block;
            text-align: right;
            color: var(--orange);
            text-decoration: none;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: var(--orange);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            background: #e06b15;
            transform: translateY(-2px);
        }

        .social-login {
            text-align: center;
            margin-top: 2rem;
        }

        .social-login p {
            color: #666;
            margin-bottom: 1rem;
        }

        .social-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            margin: 0 10px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .social-btn:hover {
            transform: scale(1.1);
        }

        #passwordStrength {
            margin-top: 5px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Flash Messages */
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .flash-message {
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            font-size: 14px;
            animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            box-shadow: 0 8px 24px -4px rgba(0, 0, 0, 0.2);
        }

        .flash-message.success {
            background: linear-gradient(to right, #22c55e, #4ade80);
        }

        .flash-message.error {
            background: linear-gradient(to right, #ef4444, #f87171);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Focus styles for accessibility */
        .password-toggle:focus {
            outline: 2px solid var(--orange);
            outline-offset: 2px;
            background-color: var(--light-blue);
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .password-toggle {
                border: 1px solid currentColor;
            }

            .password-toggle:hover {
                background-color: ButtonFace;
                color: ButtonText;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .password-toggle,
            .password-toggle i,
            .password-toggle.animating-open i,
            .password-toggle.animating-close i {
                transition: none;
                animation: none;
            }
        }

        /* Password strength indicator */
        .password-strength {
            font-size: 0.85rem;
            margin-top: 5px;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .container {
                padding: 1.5rem;
                max-width: 100%;
            }

            .header h2 {
                font-size: 1.5rem;
            }

            .toggle-btn {
                padding: 8px 20px;
            }

            .social-btn {
                width: 40px;
                height: 40px;
            }

            .password-toggle {
                right: 8px;
                padding: 6px;
                font-size: 1rem;
            }

            .password-input-container input {
                padding-right: 40px;
            }
        }

        /* Touch device optimizations */
        @media (hover: none) and (pointer: coarse) {
            .password-toggle {
                padding: 12px;
                font-size: 1.2rem;
            }

            .password-toggle:hover {
                transform: translateY(-50%);
            }
        }
    </style>
</head>
<body>
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="container">
        <div class="header">
            <h2>Reset Password</h2>
        </div>

        <form method="POST" action="{{ url_for('reset_password') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            <div class="form-group">
                <label>OTP</label>
                <input type="text" name="otp" required>
            </div>
            <div class="form-group">
                <label for="new_password">New Password</label>
                <div class="password-input-container">
                    <input type="password" id="new_password" name="new_password" required
                           aria-describedby="new_password_toggle password-strength">
                    <button type="button" class="password-toggle" id="new_password_toggle"
                            aria-label="Show password"
                            onclick="togglePasswordVisibility('new_password')">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                <div id="password-strength" class="password-strength" aria-live="polite"></div>
            </div>
            <div class="form-group">
                <label for="confirm_password">Confirm New Password</label>
                <div class="password-input-container">
                    <input type="password" id="confirm_password" name="confirm_password" required
                           aria-describedby="confirm_password_toggle">
                    <button type="button" class="password-toggle" id="confirm_password_toggle"
                            aria-label="Show password"
                            onclick="togglePasswordVisibility('confirm_password')">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
            <button type="submit" class="submit-btn">Reset Password</button>
            <p style="text-align: center; margin-top: 1rem;">
                <a href="{{ url_for('login') }}" style="color: var(--orange);">Back to Login</a>
            </p>
        </form>
    </div>

    <script>
        // Auto-hide flash messages after 5 seconds
        setTimeout(function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                message.style.opacity = '0';
                setTimeout(function() {
                    message.remove();
                }, 300);
            });
        }, 5000);

        // Password visibility toggle functionality
        function togglePasswordVisibility(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleButton = document.getElementById(inputId + '_toggle');
            const icon = toggleButton.querySelector('i');

            // Add animation class
            toggleButton.classList.add(passwordInput.type === 'password' ? 'animating-open' : 'animating-close');

            // Toggle password visibility
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                toggleButton.classList.add('visible');
                toggleButton.setAttribute('aria-label', 'Hide password');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                toggleButton.classList.remove('visible');
                toggleButton.setAttribute('aria-label', 'Show password');
            }

            // Remove animation class after animation completes
            setTimeout(() => {
                toggleButton.classList.remove('animating-open', 'animating-close');
            }, 300);

            // Keep focus on the input field
            passwordInput.focus();
        }

        // Enhanced form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const otp = document.querySelector('input[name="otp"]').value.trim();
            const newPassword = document.querySelector('input[name="new_password"]').value;
            const confirmPassword = document.querySelector('input[name="confirm_password"]').value;

            if (!otp) {
                showValidationError('Please enter the OTP');
                e.preventDefault();
                return;
            }

            if (!newPassword) {
                showValidationError('Please enter a new password');
                e.preventDefault();
                return;
            }

            if (newPassword.length < 6) {
                showValidationError('Password must be at least 6 characters long');
                e.preventDefault();
                return;
            }

            if (newPassword !== confirmPassword) {
                showValidationError('Passwords do not match');
                e.preventDefault();
                return;
            }
        });

        // Enhanced validation error display
        function showValidationError(message) {
            // Remove existing error messages
            const existingErrors = document.querySelectorAll('.validation-error');
            existingErrors.forEach(error => error.remove());

            // Create new error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'flash-message error validation-error';
            errorDiv.textContent = message;

            // Add to flash messages container
            const flashContainer = document.querySelector('.flash-messages');
            flashContainer.appendChild(errorDiv);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                errorDiv.style.opacity = '0';
                setTimeout(() => errorDiv.remove(), 300);
            }, 3000);
        }

        // Add keyboard support for password toggle (Enter and Space)
        document.addEventListener('keydown', function(e) {
            if (e.target.classList.contains('password-toggle')) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    e.target.click();
                }
            }
        });

        // Add real-time password strength indicator (optional enhancement)
        document.getElementById('new_password').addEventListener('input', function(e) {
            const password = e.target.value;
            const strengthIndicator = document.getElementById('password-strength');

            if (strengthIndicator) {
                let strength = 0;
                if (password.length >= 6) strength++;
                if (password.match(/[a-z]/)) strength++;
                if (password.match(/[A-Z]/)) strength++;
                if (password.match(/[0-9]/)) strength++;
                if (password.match(/[^a-zA-Z0-9]/)) strength++;

                const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
                const strengthColors = ['#ff4444', '#ff8800', '#ffaa00', '#88cc00', '#00cc44'];

                strengthIndicator.textContent = password ? `Strength: ${strengthLevels[strength] || 'Very Weak'}` : '';
                strengthIndicator.style.color = strengthColors[strength] || '#ff4444';
            }
        });
    </script>
</body>
</html>