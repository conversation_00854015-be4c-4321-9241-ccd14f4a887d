-- Database optimization script for CVBioLabs test performance
-- Run this script to add indexes and optimize queries

USE cvbiolabs;

-- Add indexes for better query performance on testdetails table
-- Check if indexes exist before creating them

-- Index for active tests (most common filter)
CREATE INDEX idx_testdetails_active ON testdetails(active);

-- Composite index for search queries (active + name/department/category)
CREATE INDEX idx_testdetails_search ON testdetails(active, TestName(50), DepartmentName(50));

-- Index for department filtering
CREATE INDEX idx_testdetails_department ON testdetails(DepartmentName(50), active);

-- Index for test code searches
CREATE INDEX idx_testdetails_code ON testdetails(TestCode(50), active);

-- Index for ordering by TestName
CREATE INDEX idx_testdetails_name_order ON testdetails(active, TestName(50));

-- Composite index for pagination queries
CREATE INDEX idx_testdetails_pagination ON testdetails(active, TestName(50), SrNo);

-- Full-text search index for better text searching (if MySQL version supports it)
-- ALTER TABLE testdetails ADD FULLTEXT(TestName, DepartmentName, TestCategory, TestCode);

-- Analyze table to update statistics
ANALYZE TABLE testdetails;

-- Show index usage
SHOW INDEX FROM testdetails;

-- Optimize table
OPTIMIZE TABLE testdetails;

-- Additional performance recommendations:
-- 1. Consider partitioning the testdetails table if it grows very large
-- 2. Implement query caching at application level
-- 3. Use connection pooling
-- 4. Consider read replicas for heavy read workloads

-- Query to check table size and row count
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)',
    ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'Index Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'cvbiolabs' AND TABLE_NAME = 'testdetails';
