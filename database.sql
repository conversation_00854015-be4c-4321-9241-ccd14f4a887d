

CREATE DATABASE IF NOT EXISTS cvbiolabs;
USE cvbiolabs;

-- Admin Users Table
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    professional_id VARCHAR(10) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('Admin', 'Receptionist', 'Doctor') NOT NULL DEFAULT 'Admin',
    phone VARCHAR(20),
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_professional_id (professional_id),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- Users Table (Patients)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    status TINYINT(1) NOT NULL DEFAULT 1,
    otp_verified TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- User Profiles Table
CREATE TABLE user_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE NOT NULL,
    first_name VARCHAR(60),
    last_name VARCHAR(60),
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('Male', 'Female', 'Other'),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_phone (phone),
    INDEX idx_name (first_name, last_name)
);

-- Doctors Table
CREATE TABLE doctors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    professional_id VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    specialization VARCHAR(100),
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    licence_number VARCHAR(50) UNIQUE,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_professional_id (professional_id),
    INDEX idx_email (email),
    INDEX idx_licence (licence_number),
    INDEX idx_specialization (specialization)
);

-- =====================================================
-- TEST AND BOOKING TABLES
-- =====================================================

-- Test Details Table
CREATE TABLE testdetails (
    SrNo INT PRIMARY KEY,
    TestName VARCHAR(255) NOT NULL,
    TestID BIGINT,
    TestCode VARCHAR(255),
    TestAmount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    OutsourceAmount DECIMAL(10,2) DEFAULT 0.00,
    OutsourceCenter VARCHAR(255),
    SampleType VARCHAR(255),
    TestCategory VARCHAR(255),
    DepartmentName VARCHAR(255),
    Accreditation VARCHAR(255),
    IntegrationCode VARCHAR(255),
    ShortText VARCHAR(255),
    CAPTest VARCHAR(5),
    TargetTAT VARCHAR(255),
    VerificationStatus VARCHAR(255),
    TargetTATHHMM VARCHAR(255),
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_test_name (TestName),
    INDEX idx_test_code (TestCode),
    INDEX idx_category (TestCategory),
    INDEX idx_active (active)
);

-- Bookings Table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    test_id INT NOT NULL,
    booking_date DATE NOT NULL,
    appointment_time TIME DEFAULT '00:00:00',
    booking_status ENUM('pending', 'confirmed', 'cancelled', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    barcode VARCHAR(255) UNIQUE NOT NULL,
    address_line1 VARCHAR(255) NOT NULL,
    address_line2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) NOT NULL DEFAULT 'India',
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (test_id) REFERENCES testdetails(SrNo) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_test_id (test_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_status (booking_status),
    INDEX idx_barcode (barcode)
);

-- =====================================================
-- PICKUP AND LOGISTICS TABLES
-- =====================================================

-- Pickup Agents Table
CREATE TABLE pickup_agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    professional_id VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(15) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    status ENUM('Available', 'Busy', 'Inactive') NOT NULL DEFAULT 'Available',
    vehicle_number VARCHAR(20),
    service_area VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_professional_id (professional_id),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_service_area (service_area)
);

-- Sample Collections Table
CREATE TABLE sample_collections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    agent_id INT NOT NULL,
    collection_status ENUM('Pending', 'Collected', 'Delivered') NOT NULL DEFAULT 'Pending',
    collection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES pickup_agents(id) ON DELETE RESTRICT,
    INDEX idx_booking_id (booking_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (collection_status),
    INDEX idx_collection_date (collection_date)
);

-- =====================================================
-- PAYMENT AND COUPON TABLES
-- =====================================================

-- Payments Table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('RazorPay', 'UPI', 'Card', 'Net Banking', 'Cash') NOT NULL,
    transaction_id VARCHAR(100) UNIQUE,
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    refund_amount DECIMAL(10,2) DEFAULT 0.00,
    refund_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_booking_id (booking_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_payment_date (payment_date)
);

-- Coupons Table
CREATE TABLE coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    expiry_date DATE NOT NULL,
    status ENUM('Active', 'Expired', 'Used') NOT NULL DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_expiry (expiry_date)
);

-- Coupon Usage Table
CREATE TABLE coupon_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coupon_id INT NOT NULL,
    user_id INT NOT NULL,
    booking_id INT,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE SET NULL,
    UNIQUE KEY unique_coupon_user (coupon_id, user_id),
    INDEX idx_coupon_id (coupon_id),
    INDEX idx_user_id (user_id),
    INDEX idx_booking_id (booking_id)
);

-- =====================================================
-- REPORTS AND MEDICAL TABLES
-- =====================================================

-- Reports Table
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    patient_id INT NOT NULL,
    test_id INT NOT NULL,
    doctor_id INT,
    assigned_doctor_id INT,
    assign_status ENUM('Not Assigned', 'Assigned') NOT NULL DEFAULT 'Not Assigned',
    report_url VARCHAR(255) NOT NULL,
    report_status ENUM('Pending', 'Verified', 'Completed') NOT NULL DEFAULT 'Pending',
    doctor_review TEXT,
    comments TEXT,
    report_type ENUM('standard', 'detailed', 'summary') NOT NULL DEFAULT 'standard',
    report_format ENUM('pdf', 'excel', 'csv') NOT NULL DEFAULT 'pdf',
    file_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (test_id) REFERENCES testdetails(SrNo) ON DELETE RESTRICT,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_doctor_id) REFERENCES doctors(id) ON DELETE SET NULL,
    INDEX idx_booking_id (booking_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_test_id (test_id),
    INDEX idx_doctor_id (doctor_id),
    INDEX idx_assigned_doctor (assigned_doctor_id),
    INDEX idx_report_status (report_status)
);

-- Patient Report Table
CREATE TABLE patient_report (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    booking_id INT NOT NULL,
    patient_id INT NOT NULL,
    test_name VARCHAR(255) NOT NULL,
    barcode VARCHAR(255) NOT NULL,
    report_url VARCHAR(255) NOT NULL,
    report_status ENUM('Pending', 'Verified', 'Completed') NOT NULL DEFAULT 'Pending',
    comment TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_by_receptionist_id INT,
    verified_by_admin_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sent_by_receptionist_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (verified_by_admin_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_report_id (report_id),
    INDEX idx_booking_id (booking_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_barcode (barcode),
    INDEX idx_status (report_status)
);

-- Report History Table
CREATE TABLE report_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Pending',
    action VARCHAR(50) NOT NULL DEFAULT 'Created',
    changed_by INT,
    doctor_id INT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    comments TEXT,
    details TEXT,
    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE SET NULL,
    INDEX idx_report_id (report_id),
    INDEX idx_changed_by (changed_by),
    INDEX idx_doctor_id (doctor_id),
    INDEX idx_changed_at (changed_at)
);

-- =====================================================
-- UTILITY AND SYSTEM TABLES
-- =====================================================

-- Referrals Table
CREATE TABLE referrals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    referral_code VARCHAR(50) NOT NULL,
    referral_status ENUM('Pending', 'Rewarded') NOT NULL DEFAULT 'Pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred (referred_id),
    INDEX idx_code (referral_code),
    INDEX idx_status (referral_status)
);

-- OTP Verification Table
CREATE TABLE otp_verification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(15) NOT NULL,
    email VARCHAR(100) NOT NULL,
    otp VARCHAR(6) NOT NULL,
    verified TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    INDEX idx_phone (phone),
    INDEX idx_email (email),
    INDEX idx_expires (expires_at)
);

-- Session Logs Table
CREATE TABLE session_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at)
);

-- Audit Logs Table
CREATE TABLE audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    username VARCHAR(255),
    action VARCHAR(255) NOT NULL,
    details TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_timestamp (timestamp)
);

-- =====================================================
-- INITIAL DATA AND SETUP
-- =====================================================


-- Show all tables created
SHOW TABLES;