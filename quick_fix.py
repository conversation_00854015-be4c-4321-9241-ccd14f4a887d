#!/usr/bin/env python3
"""
Quick fix for rate limiting issues
This script provides immediate solutions for rate limit problems
"""

import os
import sys
import time
import shutil
from dotenv import load_dotenv

load_dotenv()

def clear_flask_sessions():
    """Clear Flask session files to reset rate limits"""
    session_dir = './flask_session'
    
    if os.path.exists(session_dir):
        try:
            # Remove all session files
            for filename in os.listdir(session_dir):
                file_path = os.path.join(session_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            print("✅ Cleared Flask session files")
            return True
        except Exception as e:
            print(f"❌ Error clearing session files: {e}")
            return False
    else:
        print("ℹ️  No session directory found")
        return True

def show_current_limits():
    """Show current rate limits"""
    print("📊 Current Rate Limits:")
    print("  • Login: 10 per minute")
    print("  • Signup: 5 per minute") 
    print("  • Forgot Password: 8 per minute")
    print("  • Reset Password: 15 per minute")
    print("  • Default: 200 per hour")

def provide_solutions():
    """Provide immediate solutions"""
    print("\n🔧 Immediate Solutions:")
    print("=" * 30)
    
    print("\n1️⃣  RESTART YOUR FLASK APP (Recommended)")
    print("   • Stop your Flask app (Ctrl+C in terminal)")
    print("   • Start it again: python app.py")
    print("   • This will clear all rate limits immediately")
    
    print("\n2️⃣  WAIT FOR RATE LIMIT TO EXPIRE")
    print("   • Forgot password: Wait 1 minute")
    print("   • Login: Wait 1 minute")
    print("   • Other endpoints: Check specific limits")
    
    print("\n3️⃣  USE DIFFERENT IP/BROWSER")
    print("   • Try incognito/private browsing")
    print("   • Clear browser cache and cookies")
    print("   • Use different device/network")
    
    print("\n4️⃣  CHECK APPLICATION HEALTH")
    print("   • Visit: http://localhost:7000/health")
    print("   • Should show 'healthy' status")

def test_application():
    """Test if application is running"""
    try:
        import requests
        
        print("\n🔍 Testing application...")
        
        # Test health endpoint
        try:
            response = requests.get('http://localhost:7000/health', timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Application is {data.get('status', 'unknown')}")
                print(f"   Database: {data.get('components', {}).get('database', 'unknown')}")
            else:
                print(f"⚠️  Health check returned status {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Application is not running")
            print("💡 Start it with: python app.py")
            return False
        except Exception as e:
            print(f"⚠️  Health check failed: {e}")
        
        # Test if rate limits are working
        try:
            response = requests.get('http://localhost:7000/api/rate-limit-status', timeout=5)
            if response.status_code == 200:
                print("✅ Rate limit system is working")
            else:
                print("ℹ️  Rate limit status endpoint not available (normal in production)")
        except:
            pass
        
        return True
        
    except ImportError:
        print("ℹ️  requests library not available for testing")
        print("💡 Install with: pip install requests")
        return None

def main():
    """Main quick fix function"""
    print("⚡ CVBioLabs Quick Fix Tool")
    print("=" * 35)
    print("🎯 Fixing rate limiting issues...")
    
    # Show current situation
    show_current_limits()
    
    # Clear session files
    print("\n🗑️  Clearing session files...")
    clear_flask_sessions()
    
    # Test application
    app_running = test_application()
    
    # Provide solutions
    provide_solutions()
    
    if app_running is False:
        print("\n🚨 URGENT: Your Flask app is not running!")
        print("   Start it now with: python app.py")
    elif app_running is True:
        print("\n✅ Your Flask app appears to be running")
        print("   Rate limits should be cleared after restart")
    
    print("\n" + "="*50)
    print("🎯 RECOMMENDED ACTION:")
    print("   1. Stop your Flask app (Ctrl+C)")
    print("   2. Run: python app.py")
    print("   3. Try your request again")
    print("="*50)

if __name__ == '__main__':
    main()
