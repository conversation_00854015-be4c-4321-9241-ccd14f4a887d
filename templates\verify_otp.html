<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify OTP - CVBioLabs</title>
    <style>
        :root {
            --dark-blue: #002f6c;
            --orange: #f47c20;
            --light-blue: #e6f7ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--light-blue) 0%, white 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h2 {
            color: var(--dark-blue);
            font-size: 1.8rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            color: var(--dark-blue);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: var(--orange);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
        }

        .submit-btn:hover {
            background: #e06b15;
        }

        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .flash-message {
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            font-size: 14px;
            animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .flash-message.success {
            background: linear-gradient(to right, #22c55e, #4ade80);
        }

        .flash-message.error {
            background: linear-gradient(to right, #ef4444, #f87171);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .otp-container {
            text-align: center;
            max-width: 400px;
            margin: 0 auto;
        }

        .otp-input {
            letter-spacing: 0.5em;
            text-align: center;
            font-size: 1.5em;
        }
    </style>
</head>
<body>
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="container">
        <div class="header">
            <h2>Verify Your Email</h2>
            <p>Please enter the OTP sent to your email</p>
        </div>
        <form method="POST" action="{{ url_for('verify_otp') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            <div class="form-group">
                <label>Enter OTP</label>
                <input type="text" name="otp" required maxlength="6" pattern="[0-9]{6}">
            </div>
            <button type="submit" class="submit-btn">Verify OTP</button>
        </form>
    </div>
</body>
</html>