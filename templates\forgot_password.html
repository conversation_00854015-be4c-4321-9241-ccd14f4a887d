<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - CVBioLabs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --dark-blue: #002f6c;
            --orange: #f47c20;
            --light-blue: #e6f7ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--light-blue) 0%, white 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 2rem;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h2 {
            color: var(--dark-blue);
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
        }

        .toggle-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            background: var(--light-blue);
            padding: 5px;
            border-radius: 25px;
        }

        .toggle-btn {
            padding: 10px 25px;
            border: none;
            background: none;
            color: var(--dark-blue);
            font-weight: 500;
            cursor: pointer;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .toggle-btn.active {
            background: var(--orange);
            color: white;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            color: var(--dark-blue);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--orange);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 1rem;
        }

        .checkbox-group label {
            color: var(--dark-blue);
            font-size: 0.9rem;
        }

        .forgot-password {
            display: block;
            text-align: right;
            color: var(--orange);
            text-decoration: none;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: var(--orange);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            background: #e06b15;
            transform: translateY(-2px);
        }

        .social-login {
            text-align: center;
            margin-top: 2rem;
        }

        .social-login p {
            color: #666;
            margin-bottom: 1rem;
        }

        .social-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            margin: 0 10px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .social-btn:hover {
            transform: scale(1.1);
        }

        #passwordStrength {
            margin-top: 5px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Flash Messages */
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .flash-message {
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            font-size: 14px;
            animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            box-shadow: 0 8px 24px -4px rgba(0, 0, 0, 0.2);
        }

        .flash-message.success {
            background: linear-gradient(to right, #22c55e, #4ade80);
        }

        .flash-message.error {
            background: linear-gradient(to right, #ef4444, #f87171);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .container {
                padding: 1.5rem;
                max-width: 100%;
            }

            .header h2 {
                font-size: 1.5rem;
            }

            .toggle-btn {
                padding: 8px 20px;
            }

            .social-btn {
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="container">
        <div class="header">
            <h2>Forgot Password</h2>
        </div>

        <form method="POST" action="{{ url_for('forgot_password') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            <div class="form-group">
                <label>Email</label>
                <input type="email" name="email" required>
            </div>
            <button type="submit" class="submit-btn">Send OTP</button>
            <p style="text-align: center; margin-top: 1rem;">
                <a href="{{ url_for('login') }}" style="color: var(--orange);">Back to Login</a>
            </p>
        </form>
    </div>
</body>
</html>