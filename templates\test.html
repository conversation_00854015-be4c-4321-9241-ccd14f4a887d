<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Tests - CVBioLabs</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <meta name="csrf-token" content="{{ csrf_token }}">
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');

    :root {
        --primary-orange: #f58220;
        --deep-blue: #003865;
        --bright-blue: #007dc5;
        --light-bg: #f0f7fb;
        --white: #ffffff;
        --text-dark: #1a1a1a;
        --text-light: #6b7280;
        --success: #10b981;
        --gradient-primary: linear-gradient(135deg, var(--deep-blue) 0%, var(--bright-blue) 100%);
        --gradient-accent: linear-gradient(135deg, var(--primary-orange) 0%, #ff6b35 100%);
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }

    body {
        font-family: 'Inter', sans-serif;
        background: var(--white);
        color: var(--text-dark);
        margin: 0;
        padding: 0;
        line-height: 1.6;
    }

    /* Header */
    header {
        position: fixed;
        top: 0;
        width: 100%;
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(20px) saturate(180%);
        border-bottom: 1px solid rgba(255,255,255,0.2);
        z-index: 1000;
        transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
        box-shadow: var(--shadow-md);
    }
    .nav {
        max-width: 1200px;
        margin: 0 auto;
        padding: 1rem 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 800;
        background: var(--gradient-accent);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-decoration: none;
    }
    .logo img {
        width: 45px;
        height: 45px;
        border-radius: 8px;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    }
    .nav-links {
        display: flex;
        gap: 2rem;
        list-style: none;
        align-items: center;
        margin: 0;
        padding: 0;
    }
    .nav-links a {
        color: var(--text-dark);
        text-decoration: none;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        transition: all 0.3s ease;
        position: relative;
    }
    .nav-links a:hover {
        background: rgba(245,130,32,0.1);
        color: var(--primary-orange);
    }
    .cart-icon {
        position: relative;
        font-size: 1.3rem;
        color: var(--deep-blue);
        margin-left: 1rem;
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    .cart-icon:hover {
        color: var(--primary-orange);
    }
    .cart-count {
        position: absolute;
        top: -6px;
        right: -6px;
        background: var(--primary-orange);
        color: white;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 0.7rem;
        font-weight: bold;
        min-width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .auth-buttons {
        display: flex;
        gap: 0.75rem;
        align-items: center;
    }
    .auth-buttons .btn {
        padding: 0.5rem 1.2rem;
        border: none;
        border-radius: 50px;
        cursor: pointer;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 0.95rem;
        position: relative;
        overflow: hidden;
        background: transparent;
        color: var(--text-dark);
        border: 2px solid rgba(0, 0, 0, 0.08);
    }
    .auth-buttons .btn-primary {
        background: var(--gradient-accent);
        color: #fff;
        border: none;
        box-shadow: var(--shadow-md);
    }
    .auth-buttons .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    .auth-buttons .btn-secondary:hover {
        background: var(--light-bg);
        border-color: var(--primary-orange);
        color: var(--primary-orange);
    }
    /* Dropdown for user menu */
    .dropdown {
        position: relative;
        display: inline-block;
    }
    .dropdown-btn {
        background: transparent;
        color: var(--text-dark);
        border: 2px solid rgba(0,0,0,0.08);
        border-radius: 50px;
        padding: 0.5rem 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .dropdown-btn:hover {
        background: var(--light-bg);
        border-color: var(--primary-orange);
        color: var(--primary-orange);
    }
    .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 110%;
        background: #fff;
        min-width: 180px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        border-radius: 12px;
        z-index: 1001;
        padding: 0.5rem 0;
        border: 1px solid #eee;
    }
    .dropdown-content a {
        color: var(--text-dark);
        padding: 0.7rem 1.2rem;
        text-decoration: none;
        display: block;
        font-weight: 500;
        border-radius: 8px;
        transition: background 0.2s, color 0.2s;
    }
    .dropdown-content a:hover {
        background: var(--light-bg);
        color: var(--primary-orange);
    }
    .dropdown:hover .dropdown-content,
    .dropdown:focus-within .dropdown-content {
        display: block;
    }

    /* Main Container */
    .container {
        max-width: 1200px;
        margin: 120px auto 0;
        padding: 0 2rem;
    }

    /* Search Section */
    .search-section {
        background: var(--light-bg);
        padding: 2rem;
        border-radius: 20px;
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
        text-align: center;
    }
    .search-section h1 {
        font-family: 'Poppins', sans-serif;
        font-size: 2rem;
        font-weight: 700;
        color: var(--deep-blue);
        margin-bottom: 0.5rem;
    }
    .search-section p {
        color: var(--text-light);
        margin-bottom: 1.25rem;
        font-size: 1rem;
    }
    .search-container {
        position: relative;
        margin-bottom: 1rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }
    .search-input {
        width: 100%;
        padding: 0.875rem 1rem 0.875rem 2.75rem;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 1rem;
        background-color: white;
        transition: all 0.3s ease;
    }
    .search-input:focus {
        outline: none;
        border-color: var(--primary-orange);
        box-shadow: 0 0 0 3px rgba(245,130,32,0.1);
    }
    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-light);
    }
    .department-select {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 1rem;
        background-color: white;
        color: var(--text-dark);
        cursor: pointer;
        margin-top: 1rem;
    }
    .department-select:focus {
        outline: none;
        border-color: var(--primary-orange);
        box-shadow: 0 0 0 3px rgba(245,130,32,0.1);
    }

    /* Tests Grid */
    .tests-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2rem;
    }
    .test-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: var(--shadow-md);
        transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
        border: 1px solid rgba(0,0,0,0.05);
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .test-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-orange);
    }
    .test-name {
        font-family: 'Poppins', sans-serif;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--deep-blue);
        margin-bottom: 1rem;
    }
    .test-info {
        color: var(--text-light);
        font-size: 1rem;
        margin-bottom: 1rem;
    }
    .test-info strong {
        color: var(--deep-blue);
        font-weight: 600;
    }
    .test-price {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--success);
        margin-bottom: 1rem;
    }
    .book-btn {
        background: var(--gradient-accent);
        color: white;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 50px;
        cursor: pointer;
        font-weight: 600;
        font-size: 1rem;
        width: 100%;
        transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
        box-shadow: var(--shadow-md);
    }
    .book-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        background: linear-gradient(135deg, #ff6b35 0%, var(--primary-orange) 100%);
    }

    /* Professional Cart Modal Styling */
    .cart-modal-content {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        padding: 0;
        font-family: 'Inter', sans-serif;
        max-width: 1100px;
        width: 90%;
        max-height: 85vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Cart Header */
    .cart-header {
        background: linear-gradient(135deg, #f58220 0%, #ff6b35 100%);
        padding: 2.25rem 2rem 2rem 2rem;
        border-radius: 20px 20px 0 0;
        position: relative;
        overflow: hidden;
        min-height: 120px;
    }
    .cart-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }
    .cart-header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        position: relative;
        z-index: 2;
        height: 100%;
    }
    .cart-title {
        display: flex;
        align-items: flex-start;
        gap: 1.25rem;
        flex: 1;
        padding-top: 0.5rem;
    }
    .cart-icon-wrapper {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 14px;
        padding: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-top: 0.25rem;
        flex-shrink: 0;
    }
    .cart-icon-wrapper i {
        font-size: 1.4rem;
        color: white;
    }
    .cart-title-text {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding-top: 0.25rem;
    }
    .cart-title-text h3 {
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        line-height: 1.2;
    }
    .cart-subtitle {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.98);
        margin: 0;
        font-weight: 500;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.3px;
        background: rgba(255, 255, 255, 0.12);
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.25);
        display: inline-block;
        max-width: fit-content;
    }
    .cart-close-btn {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 0.7rem;
        color: white;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 0.5rem;
        flex-shrink: 0;
    }
    .cart-close-btn:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Cart Content */
    .cart-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        background: #fafbfc;
    }
    .cart-main {
        flex: 1;
        display: flex;
        gap: 2.5rem;
        padding: 2rem;
        overflow: hidden;
        min-height: 0;
    }
    .cart-items-container {
        flex: 1.8;
        min-width: 0;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
    }
    .cart-items-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.25rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f3f4f6;
    }
    .cart-items-header h4 {
        font-family: 'Poppins', sans-serif;
        font-size: 1.2rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.6rem;
    }
    .cart-items-header h4 i {
        color: #f58220;
        font-size: 1.1rem;
    }
    .cart-items-count {
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        padding: 0.4rem 0.9rem;
        border-radius: 16px;
        font-size: 0.8rem;
        font-weight: 600;
        color: #6b7280;
        border: 1px solid #d1d5db;
    }
    .cart-items-list {
        flex: 1;
        overflow-y: auto;
        padding-right: 0.5rem;
        min-height: 200px;
    }
    .cart-items-list::-webkit-scrollbar {
        width: 6px;
    }
    .cart-items-list::-webkit-scrollbar-track {
        background: #f3f4f6;
        border-radius: 3px;
    }
    .cart-items-list::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 3px;
    }
    .cart-items-list::-webkit-scrollbar-thumb:hover {
        background: #9ca3af;
    }

    /* Cart Sidebar */
    .cart-sidebar {
        flex: 1;
        min-width: 350px;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        overflow-y: auto;
    }

    /* Billing Card */
    .billing-card {
        background: white;
        border-radius: 16px;
        padding: 1.75rem;
        border: 1px solid #e5e7eb;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        margin-bottom: 1rem;
    }
    .billing-header h4 {
        font-family: 'Poppins', sans-serif;
        font-size: 1.15rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 1.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.6rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f3f4f6;
    }
    .billing-header h4 i {
        color: #f58220;
        font-size: 1.1rem;
    }
    .billing-form .form-row {
        margin-bottom: 1rem;
    }
    .billing-form .form-row-split {
        display: flex;
        gap: 1rem;
    }
    .billing-form .form-row-split .form-group {
        flex: 1;
    }
    .billing-form .form-group {
        margin-bottom: 0;
    }
    .billing-form .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
    }
    .billing-form .form-control {
        width: 100%;
        padding: 0.9rem 1.1rem;
        border: 2px solid #e5e7eb;
        border-radius: 10px;
        font-size: 0.95rem;
        background-color: #fafbfc;
        color: #374151;
        transition: all 0.3s ease;
        box-sizing: border-box;
        font-weight: 400;
    }
    .billing-form .form-control:focus {
        outline: none;
        border-color: #f58220;
        box-shadow: 0 0 0 3px rgba(245, 130, 32, 0.1);
        transform: translateY(-1px);
        background-color: white;
    }
    .billing-form .form-control::placeholder {
        color: #9ca3af;
        font-weight: 400;
    }

    /* Coupon Section */
    .coupon-section {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e2e8f0;
    }
    .coupon-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-light);
    }
    .coupon-input-group {
        display: flex;
        gap: 0.5rem;
        align-items: stretch;
    }
    .coupon-input {
        flex: 1;
    }
    .coupon-btn {
        padding: 0.875rem 1rem;
        border: none;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
    }
    .apply-btn {
        background: var(--gradient-accent);
        color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .apply-btn:hover {
        background: linear-gradient(135deg, #ff6b35 0%, var(--primary-orange) 100%);
        transform: translateY(-1px);
        box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);
    }
    .remove-btn {
        background: #ef4444;
        color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .remove-btn:hover {
        background: #dc2626;
        transform: translateY(-1px);
    }
    .coupon-message {
        margin-top: 0.75rem;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    .coupon-message.success {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #a7f3d0;
    }
    .coupon-message.error {
        background: #fee2e2;
        color: #991b1b;
        border: 1px solid #fca5a5;
    }
    /* Order Summary */
    .order-summary {
        background: white;
        border-radius: 16px;
        padding: 1.75rem;
        border: 1px solid #e5e7eb;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    .summary-header h4 {
        font-family: 'Poppins', sans-serif;
        font-size: 1.15rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 1.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.6rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f3f4f6;
    }
    .summary-header h4 i {
        color: #f58220;
        font-size: 1.1rem;
    }
    .summary-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.95rem;
        padding: 0.25rem 0;
    }
    .summary-row span:first-child {
        color: #6b7280;
        font-weight: 500;
    }
    .summary-row span:last-child {
        color: #374151;
        font-weight: 600;
    }
    .discount-row span:last-child {
        color: #059669;
    }
    .summary-divider {
        height: 1px;
        background: #e5e7eb;
        margin: 0.75rem 0;
    }
    .total-row {
        font-size: 1.2rem;
        font-weight: 700;
        padding: 0.75rem 0;
        background: linear-gradient(135deg, #fef3e2 0%, #fde68a 100%);
        margin: 0.5rem -0.75rem -0.75rem -0.75rem;
        padding: 1rem 0.75rem;
        border-radius: 0 0 16px 16px;
    }
    .total-row span:first-child {
        color: #1f2937;
    }
    .total-row span:last-child {
        color: #f58220;
        font-size: 1.4rem;
    }

    /* Cart Footer */
    .cart-footer {
        background: white;
        padding: 1.75rem 2rem;
        border-top: 1px solid #e5e7eb;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
    }
    .cart-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        align-items: center;
    }
    .btn-secondary-cart {
        background: #f9fafb;
        color: #6b7280;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 0.9rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    .btn-secondary-cart:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
        color: #374151;
        transform: translateY(-1px);
    }
    .btn-primary-cart {
        background: linear-gradient(135deg, #f58220 0%, #ff6b35 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.9rem 2.25rem;
        font-size: 0.95rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 12px rgba(245, 130, 32, 0.3);
        min-width: 220px;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }
    .btn-primary-cart::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    .btn-primary-cart:hover::before {
        left: 100%;
    }
    .btn-primary-cart:hover {
        background: linear-gradient(135deg, #e06b15 0%, #ff5722 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(245, 130, 32, 0.4);
    }
    .btn-primary-cart:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
    /* Cart Item Styling */
    .cart-item {
        background: #fafbfc;
        border-radius: 12px;
        padding: 1.25rem;
        margin-bottom: 0.75rem;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
        position: relative;
    }
    .cart-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border-color: #f58220;
        background: white;
        transform: translateY(-1px);
    }
    .cart-item:last-child {
        margin-bottom: 0;
    }
    .cart-item-info {
        flex: 1;
        min-width: 0;
    }
    .cart-item-name {
        font-size: 1.05rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.3rem;
        line-height: 1.4;
    }
    .cart-item-details {
        font-size: 0.85rem;
        color: #6b7280;
        display: flex;
        align-items: center;
        gap: 0.4rem;
    }
    .cart-item-details span {
        display: flex;
        align-items: center;
    }
    .cart-item-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.4rem;
        background: white;
        border-radius: 10px;
        padding: 0.3rem;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }
    .quantity-btn {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 7px;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.8rem;
        font-weight: 600;
        color: #374151;
    }
    .quantity-btn:hover {
        background: #f58220;
        color: white;
        border-color: #f58220;
        transform: scale(1.05);
    }
    .quantity-value {
        min-width: 28px;
        text-align: center;
        font-weight: 600;
        color: #1f2937;
        font-size: 0.9rem;
    }
    .cart-item-price {
        font-size: 1.1rem;
        font-weight: 700;
        color: #f58220;
        min-width: 85px;
        text-align: right;
    }
    .remove-item-btn {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #ef4444;
        font-size: 0.85rem;
        cursor: pointer;
        padding: 0.4rem;
        border-radius: 8px;
        transition: all 0.2s ease;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .remove-item-btn:hover {
        background: #fee2e2;
        color: #dc2626;
        border-color: #fca5a5;
        transform: scale(1.05);
    }

    /* Empty Cart State */
    .empty-cart {
        background: linear-gradient(135deg, #fafbfc 0%, #f3f4f6 100%);
        border-radius: 16px;
        padding: 3.5rem 2rem;
        text-align: center;
        border: 2px dashed #d1d5db;
        position: relative;
        overflow: hidden;
    }
    .empty-cart::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(245, 130, 32, 0.05) 0%, transparent 70%);
        animation: pulse 3s ease-in-out infinite;
    }
    @keyframes pulse {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
    }
    .empty-cart i {
        font-size: 3.5rem;
        color: #d1d5db;
        margin-bottom: 1.25rem;
        position: relative;
        z-index: 2;
    }
    .empty-cart p {
        font-size: 1.15rem;
        color: #6b7280;
        margin-bottom: 1.75rem;
        font-weight: 500;
        position: relative;
        z-index: 2;
    }
    .continue-shopping-btn {
        background: linear-gradient(135deg, #f58220 0%, #ff6b35 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.9rem 1.75rem;
        font-weight: 600;
        font-size: 0.95rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(245, 130, 32, 0.3);
        position: relative;
        z-index: 2;
    }
    .continue-shopping-btn:hover {
        background: linear-gradient(135deg, #e06b15 0%, #ff5722 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(245, 130, 32, 0.4);
    }

    /* Responsive Design for Cart */
    @media (max-width: 1024px) {
        .cart-modal-content {
            max-width: 95%;
            margin: 1rem;
        }
        .cart-main {
            flex-direction: column;
            gap: 1.5rem;
        }
        .cart-sidebar {
            min-width: auto;
        }
    }
    @media (max-width: 768px) {
        .cart-modal-content {
            margin: 0.5rem;
            border-radius: 16px;
            max-height: 95vh;
        }
        .cart-header {
            padding: 1.5rem;
            border-radius: 16px 16px 0 0;
        }
        .cart-title-text h3 {
            font-size: 1.5rem;
        }
        .cart-main {
            padding: 1.5rem;
        }
        .cart-footer {
            padding: 1rem 1.5rem;
        }
        .cart-actions {
            flex-direction: column;
            gap: 0.75rem;
        }
        .btn-secondary-cart,
        .btn-primary-cart {
            width: 100%;
            justify-content: center;
        }
        .billing-form .form-row-split {
            flex-direction: column;
            gap: 0.75rem;
        }
        .cart-item {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }
        .cart-item-controls {
            justify-content: space-between;
        }
        .cart-item-price {
            text-align: center;
            font-size: 1.25rem;
        }
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .container {
            padding: 0 1rem;
        }
        .tests-grid {
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
    }
    @media (max-width: 768px) {
        .nav {
            flex-direction: column;
            gap: 1rem;
        }
        .tests-grid {
            grid-template-columns: 1fr;
        }
        .search-section {
            padding: 1rem;
        }
        .test-card {
            padding: 1rem;
        }
    }

    /* Modal display fix */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(4px);
        z-index: 2000;
        align-items: center;
        justify-content: center;
        overflow-y: auto;
        padding: 1rem;
    }
    .modal.active {
        display: flex;
        animation: modalFadeIn 0.3s ease-out;
    }
    @keyframes modalFadeIn {
        from {
            opacity: 0;
            backdrop-filter: blur(0px);
        }
        to {
            opacity: 1;
            backdrop-filter: blur(4px);
        }
    }
    .modal .modal-content {
        z-index: 2001;
        position: relative;
        margin: auto;
        background: var(--white);
        animation: modalSlideIn 0.3s ease-out;
    }
    @keyframes modalSlideIn {
        from {
            transform: translateY(-20px) scale(0.95);
            opacity: 0;
        }
        to {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
    }

    /* Auth Modal */
    .auth-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.7);
        z-index: 3000;
        align-items: center;
        justify-content: center;
        overflow-y: auto;
    }
    .auth-modal.active {
        display: flex;
    }
    .auth-modal-content {
        background: var(--white);
        border-radius: 20px;
        box-shadow: var(--shadow-xl);
        padding: 2rem;
        width: 90%;
        max-width: 400px;
        margin: 0 auto;
        position: relative;
        text-align: center;
    }
    .auth-modal-content h2 {
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--deep-blue);
        margin-bottom: 1rem;
        text-align: center;
    }
    .auth-modal-content p {
        color: var(--text-light);
        margin-bottom: 1.5rem;
        font-size: 1rem;
        text-align: center;
    }
    .auth-modal-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .auth-modal-btn {
        padding: 0.75rem;
        border: none;
        border-radius: 50px;
        cursor: pointer;
        font-weight: 600;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 1rem;
        position: relative;
        overflow: hidden;
        background: var(--gradient-accent);
        color: #fff;
        box-shadow: var(--shadow-md);
    }
    .auth-modal-btn:hover {
        background: linear-gradient(135deg, #ff6b35 0%, var(--primary-orange) 100%);
        box-shadow: var(--shadow-lg);
    }
    .auth-modal .close {
        position: absolute;
        top: 1rem;
        right: 1rem;
        color: var(--text-light);
        font-size: 1.5rem;
        cursor: pointer;
        transition: color 0.3s;
    }
    .auth-modal .close:hover {
        color: var(--primary-orange);
    }

    /* Loading Spinner */
    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        z-index: 3000;
        align-items: center;
        justify-content: center;
    }
    .loading-spinner {
        border: 4px solid rgba(245, 130, 32, 0.2);
        border-top: 4px solid var(--primary-orange);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 0.8s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Success Popup Modal */
    .success-popup-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 2000;
        align-items: center;
        justify-content: center;
        overflow-y: auto;
    }
    .success-popup-modal.active {
        display: flex;
    }
    .success-popup-content {
        background: var(--white);
        border-radius: 20px;
        box-shadow: var(--shadow-xl);
        padding: 2rem;
        width: 90%;
        max-width: 400px;
        margin: 0 auto;
        position: relative;
        text-align: center;
    }
    .success-icon {
        font-size: 3rem;
        color: var(--success);
        margin-bottom: 1rem;
    }
    .success-message {
        font-size: 1.1rem;
        color: var(--text-dark);
        margin-bottom: 1.5rem;
    }
    .success-popup-content button {
        background: var(--gradient-accent);
        color: white;
        border: none;
        border-radius: 50px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
        box-shadow: var(--shadow-md);
    }
    .success-popup-content button:hover {
        background: linear-gradient(135deg, #ff6b35 0%, var(--primary-orange) 100%);
        box-shadow: var(--shadow-lg);
    }

    /* Miscellaneous */
    .no-results {
        text-align: center;
        color: var(--text-light);
        padding: 2rem;
        border-radius: 12px;
        background: var(--light-bg);
        box-shadow: var(--shadow-md);
    }
    .error {
        color: #dc3545;
        text-align: center;
        margin: 1rem 0;
    }
    .empty-cart {
        background: #f0f7fb;
        border-radius: 16px;
        box-shadow: var(--shadow-md);
        padding: 2.5rem 1rem;
        text-align: center;
        min-height: 220px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .error-message {
        color: #dc3545;
        text-align: center;
        margin: 1rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    /* Pagination Styles */
    .pagination-container {
        margin: 2rem 0;
        text-align: center;
    }
    .pagination-info {
        margin-bottom: 1rem;
        color: var(--text-light);
        font-size: 0.9rem;
    }
    .pagination {
        display: inline-flex;
        gap: 0.5rem;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
    }
    .pagination-btn {
        background: white;
        border: 2px solid #e2e8f0;
        color: var(--text-dark);
        padding: 0.5rem 1rem;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        min-width: 40px;
        justify-content: center;
    }
    .pagination-btn:hover {
        background: var(--light-bg);
        border-color: var(--primary-orange);
        color: var(--primary-orange);
        transform: translateY(-1px);
    }
    .pagination-btn.active {
        background: var(--gradient-accent);
        border-color: var(--primary-orange);
        color: white;
        box-shadow: var(--shadow-md);
    }
    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .nav-links {
            flex-direction: column;
            gap: 0.5rem;
        }
        .search-section {
            padding: 1.5rem;
        }
        .test-card {
            padding: 1.5rem;
        }
        .cart-modal-content, .billing-section, .auth-modal-content {
            padding: 1.5rem;
        }
        .auth-modal-content {
            width: 90%;
        }
        .success-popup-content {
            width: 90%;
        }
        .pagination {
            gap: 0.25rem;
        }
        .pagination-btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
        }
    }
</style>
</head>
<body>
    <!-- Header -->
<header class="header" id="header">
    <nav class="nav">
        <div class="logo">
            <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBIOLABS Logo">
            <span class="logo-text">CVBIOLABS</span>
        </div>
        <ul class="nav-links">
            <li><a href="{{ url_for('home') }}">Home</a></li>
            {% if not current_user.is_authenticated %}
                <li><a href="{{ url_for('home') }}#services">Services</a></li>
                <li><a href="{{ url_for('home') }}#process">Process</a></li>
                <li><a href="{{ url_for('home') }}#about">About</a></li>
                <li><a href="{{ url_for('home') }}#contact">Contact</a></li>
            {% else %}
                <li><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ url_for('home') }}#services">Services</a></li>
                <li><a href="{{ url_for('home') }}#process">Process</a></li>
                <li><a href="{{ url_for('home') }}#about">About</a></li>
                <li><a href="{{ url_for('home') }}#contact">Contact</a></li>    
            {% endif %}
            <li>
                <div class="cart-icon" id="cartIcon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cart-count">0</span>
                </div>
            </li>
        </ul>
        <div class="auth-buttons">
            {% if current_user.is_authenticated %}
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-btn">
                        <i class="fas fa-user"></i> {{ current_user.username }}
                    </button>
                    <div class="dropdown-content">
                        <a href="{{ url_for('dashboard') }}">Dashboard</a>
                        <a href="{{ url_for('dashboard') }}#bookings">My Bookings</a>
                        <a href="{{ url_for('dashboard') }}#reports">My Reports</a>
                        <a href="{{ url_for('dashboard') }}#profile">Profile</a>
                        <a href="{{ url_for('logout') }}">Logout</a>
                    </div>
                </div>
            {% else %}
                <a class="btn btn-secondary" href="{{ url_for('login') }}">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
                <a class="btn btn-primary" href="{{ url_for('signup') }}">
                    <i class="fas fa-user-plus"></i> Sign Up
                </a>
            {% endif %}
        </div>
    </nav>
</header>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}" id="flashMessage">
                        <i class="fas {% if category == 'success' %}fa-check-circle{% else %}fa-exclamation-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="search-section">
            <h1>Book Your Test</h1>
            <p>Search from our wide range of diagnostic tests</p>
            
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" class="search-input" placeholder="Search for tests...">
            </div>

            <div class="department-filter">
                <select id="departmentFilter" class="department-select">
                    <option value="">All Departments</option>
                    {% for department in departments %}
                        <option value="{{ department }}">{{ department }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div class="tests-container">
            <div id="testsContainer" class="tests-grid">
                {% for test in tests %}
                    <div class="test-card" data-department="{{ test.DepartmentName }}">
                        <h4 class="test-name">{{ test.TestName }}</h4>
                        <div class="test-info">
                            <p><strong>Test Code:</strong> {{ test.TestCode }}</p>
                            <p><strong>Department:</strong> {{ test.DepartmentName }}</p>
                            <p><strong>Sample Type:</strong> {{ test.SampleType }}</p>
                            {% if test.TargetTAT %}
                                <p><strong>TAT:</strong> {{ test.TargetTAT }}</p>
                            {% endif %}
                        </div>
                        <div class="test-price">₹{{ "%.2f"|format(test.TestAmount) }}</div>
                        <button class="book-btn" data-test-id="{{ test.SrNo }}">Add to Cart</button>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Auth Modal -->
    <div id="authModal" class="auth-modal">
        <div class="auth-modal-content">
            <span class="close"><i class="fas fa-times"></i></span>
            <h2>Login Required</h2>
            <p>Please login to book your test</p>
            <div class="auth-modal-buttons">
                <a href="{{ url_for('login') }}" class="auth-modal-btn auth-modal-login">Login</a>
                <a href="{{ url_for('signup') }}" class="auth-modal-btn auth-modal-signup">Sign Up</a>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Cart Modal -->
    <div id="cartModal" class="modal">
        <div class="modal-content cart-modal-content">
            <div class="cart-header">
                <div class="cart-header-content">
                    <div class="cart-title">
                        <div class="cart-icon-wrapper">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="cart-title-text">
                            <h3>Your Cart</h3>
                            <p class="cart-subtitle">Review your selected tests</p>
                        </div>
                    </div>
                    <button id="closeCart" class="cart-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="cart-content">
                <div class="cart-main">
                    <div class="cart-items-container">
                        <div class="cart-items-header">
                            <h4><i class="fas fa-list-ul"></i> Selected Tests</h4>
                            <div class="cart-items-count">
                                <span id="cartItemsCount">0 items</span>
                            </div>
                        </div>
                        <div id="cartItems" class="cart-items-list"></div>
                    </div>

                    <div class="cart-sidebar">
                        <div class="billing-card">
                            <div class="billing-header">
                                <h4><i class="fas fa-user-circle"></i> Billing Information</h4>
                            </div>
                            <form id="billingForm" class="billing-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Full Name</label>
                                        <input type="text" id="fullName" class="form-control" placeholder="Enter your full name" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Email Address</label>
                                        <input type="email" id="email" class="form-control" placeholder="Enter your email" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Phone Number</label>
                                        <input type="tel" id="phone" class="form-control" placeholder="Enter your phone number" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Address</label>
                                        <input type="text" id="address" class="form-control" placeholder="Enter your address" required>
                                    </div>
                                </div>
                                <div class="form-row form-row-split">
                                    <div class="form-group">
                                        <label class="form-label">City</label>
                                        <input type="text" id="city" class="form-control" placeholder="City" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">State</label>
                                        <input type="text" id="state" class="form-control" placeholder="State" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Pincode</label>
                                        <input type="text" id="pincode" class="form-control" placeholder="Enter pincode" required>
                                    </div>
                                </div>

                                <div class="coupon-section">
                                    <div class="coupon-header">
                                        <i class="fas fa-tag"></i>
                                        <span>Have a coupon code?</span>
                                    </div>
                                    <div class="coupon-input-group">
                                        <input type="text" id="couponCode" class="form-control coupon-input" placeholder="Enter coupon code">
                                        <button type="button" id="applyCoupon" class="coupon-btn apply-btn">
                                            <i class="fas fa-check"></i> Apply
                                        </button>
                                        <button type="button" id="removeCoupon" class="coupon-btn remove-btn" style="display: none;">
                                            <i class="fas fa-times"></i> Remove
                                        </button>
                                    </div>
                                    <div id="couponMessage" class="coupon-message" style="display: none;">
                                        <i class="fas"></i> <span></span>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div class="order-summary">
                            <div class="summary-header">
                                <h4><i class="fas fa-receipt"></i> Order Summary</h4>
                            </div>
                            <div class="summary-content">
                                <div class="summary-row">
                                    <span>Subtotal</span>
                                    <span id="subtotal">₹0.00</span>
                                </div>
                                <div class="summary-row discount-row">
                                    <span>Discount</span>
                                    <span id="discount">-₹0.00</span>
                                </div>
                                <div class="summary-divider"></div>
                                <div class="summary-row total-row">
                                    <span>Total Amount</span>
                                    <span id="cartTotal">₹0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cart-footer">
                    <div class="cart-actions">
                        <button id="continueShopping" class="btn-secondary-cart">
                            <i class="fas fa-arrow-left"></i> Continue Shopping
                        </button>
                        <button id="bookTests" class="btn-primary-cart">
                            <i class="fas fa-credit-card"></i> Proceed to Payment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Popup Modal -->
    <div id="successPopup" class="success-popup-modal">
        <div class="success-popup-content">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="success-message" id="successMessage">Payment successful! Your tests have been booked.</div>
        </div>
    </div>

    <script>
        const isAuthenticated = "{{ 'true' if current_user.is_authenticated else 'false' }}" === 'true';
        const searchInput = document.getElementById('searchInput');
        const testsContainer = document.getElementById('testsContainer');
        let currentPage = 1;
        let currentQuery = '';
        let currentDepartment = '';
        let isLoading = false;
        let hasMorePages = true;

        // Function to show loading overlay
        function showLoading() {
            $('#loadingOverlay').css('display', 'flex');
        }

        // Function to hide loading overlay
        function hideLoading() {
            $('#loadingOverlay').css('display', 'none');
        }

        // Function to format price
        function formatPrice(price) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR'
            }).format(price);
        }

        // Function to render tests
        function renderTests(tests, append = false) {
            if (!tests || tests.length === 0) {
                if (!append) {
                    testsContainer.innerHTML = '<div class="no-results">No tests found matching your search.</div>';
                }
                return;
            }

            const testsHTML = tests.map(test => `
                <div class="test-card" data-department="${test.DepartmentName || 'N/A'}">
                    <h4 class="test-name">${test.TestName || 'N/A'}</h4>
                    <div class="test-info">
                        <p><strong>Test Code:</strong> ${test.TestCode || 'N/A'}</p>
                        <p><strong>Department:</strong> ${test.DepartmentName || 'N/A'}</p>
                        <p><strong>Sample Type:</strong> ${test.SampleType || 'N/A'}</p>
                        ${test.TargetTAT ? `<p><strong>TAT:</strong> ${test.TargetTAT}</p>` : ''}
                    </div>
                    <div class="test-price">₹${(test.TestAmount || 0).toFixed(2)}</div>
                    <button class="book-btn" data-test-id="${test.SrNo || ''}">Add to Cart</button>
                </div>
            `).join('');

            if (append) {
                testsContainer.innerHTML += testsHTML;
            } else {
                testsContainer.innerHTML = testsHTML;
            }
        }

        // Function to render pagination
        function renderPagination(pagination) {
            const existingPagination = document.querySelector('.pagination-container');
            if (existingPagination) {
                existingPagination.remove();
            }

            if (pagination.pages <= 1) return;

            const paginationHTML = `
                <div class="pagination-container" style="text-align: center; margin: 2rem 0; padding: 1rem;">
                    <div class="pagination-info" style="margin-bottom: 1rem; color: #6b7280;">
                        Showing ${((pagination.page - 1) * pagination.per_page) + 1} to ${Math.min(pagination.page * pagination.per_page, pagination.total)} of ${pagination.total} tests
                    </div>
                    <div class="pagination" style="display: inline-flex; gap: 0.5rem; align-items: center;">
                        ${pagination.has_prev ? `<button class="pagination-btn" data-page="${pagination.page - 1}">← Previous</button>` : ''}
                        ${Array.from({length: Math.min(5, pagination.pages)}, (_, i) => {
                            const page = i + 1;
                            const isActive = page === pagination.page;
                            return `<button class="pagination-btn ${isActive ? 'active' : ''}" data-page="${page}">${page}</button>`;
                        }).join('')}
                        ${pagination.pages > 5 ? '<span>...</span>' : ''}
                        ${pagination.has_next ? `<button class="pagination-btn" data-page="${pagination.page + 1}">Next →</button>` : ''}
                    </div>
                </div>
            `;

            testsContainer.insertAdjacentHTML('afterend', paginationHTML);

            // Add pagination click handlers
            document.querySelectorAll('.pagination-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const page = parseInt(e.target.dataset.page);
                    if (page && page !== currentPage) {
                        currentPage = page;
                        searchTests(currentQuery, false);
                    }
                });
            });
        }

        // Function to search tests with pagination
        async function searchTests(query = '', append = false) {
            if (isLoading) return;

            try {
                isLoading = true;
                if (!append) showLoading();

                const params = new URLSearchParams({
                    query: query,
                    page: append ? currentPage + 1 : currentPage,
                    per_page: 20,
                    department: currentDepartment
                });

                const response = await fetch(`/search_tests?${params}`);

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const data = await response.json();
                console.log('Received data:', data);

                if (!data.tests || !Array.isArray(data.tests)) {
                    console.error('Expected tests array, got:', typeof data.tests);
                    throw new Error('Invalid response format');
                }

                if (append) {
                    currentPage++;
                }

                renderTests(data.tests, append);
                if (!append) {
                    renderPagination(data.pagination);
                }

                hasMorePages = data.pagination.has_next;

            } catch (error) {
                console.error('Error fetching tests:', error);
                if (!append) {
                    testsContainer.innerHTML = '<div class="error">Error loading tests. Please try again later.</div>';
                }
            } finally {
                isLoading = false;
                if (!append) hideLoading();
            }
        }

        // Load initial tests
        searchTests();

        $(document).ready(function() {
            // Set up CSRF token for all AJAX requests
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", $('meta[name="csrf-token"]').attr('content'));
                    }
                }
            });

            // Function to show auth modal
            function showAuthModal() {
                console.log('Showing auth modal');
                $('#authModal').addClass('active');
            }

            // Function to hide auth modal
            function hideAuthModal() {
                console.log('Hiding auth modal');
                $('#authModal').removeClass('active');
            }

            // Add to Cart
            $(document).on('click', '.book-btn', function() {
                if (!isAuthenticated) {
                    showAuthModal();
                    return;
                }

                const testId = $(this).data('test-id');
                const button = $(this);
                button.prop('disabled', true);

                $.ajax({
                    url: '/cart/add',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ test_id: testId }),
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#cart-count').text(response.count);
                            const originalText = button.text();
                            button.text('Added!');
                            setTimeout(() => button.text(originalText), 2000);
                        } else {
                            button.text('Error');
                            setTimeout(() => button.text('Add to Cart'), 2000);
                        }
                    },
                    error: function(xhr, status, error) {
                        if (xhr.status === 401) {
                            showAuthModal();
                            return;
                        }
                        console.error('Cart error:', {xhr, status, error});
                        button.text('Error');
                        setTimeout(() => button.text('Add to Cart'), 2000);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            });

            // Close auth modal when clicking outside
            $(window).on('click', function(e) {
                if ($(e.target).is('#authModal')) {
                    hideAuthModal();
                }
            });

            // Close auth modal when clicking the close button
            $('#authModal .close').on('click', function() {
                hideAuthModal();
            });

            // Department Filter
            $('#departmentFilter').change(function() {
                currentDepartment = $(this).val();
                currentPage = 1;
                searchTests(currentQuery, false);
            });

            // Search Functionality with debouncing
            let searchTimeout;
            $('#searchInput').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentQuery = $(this).val().trim();
                    currentPage = 1;
                    searchTests(currentQuery, false);
                }, 500); // Increased debounce time for better performance
            });

            // Load Cart
            function loadCart() {
                $('#cartItems').html('<div class="loading-spinner"></div>');
                $.ajax({
                    url: '/cart',
                    method: 'GET',
                    success: function(response) {
                        if (response.status === 'success') {
                            // Update cart items count
                            $('#cartItemsCount').text(`${response.count} ${response.count === 1 ? 'item' : 'items'}`);

                            if (response.cart.length === 0) {
                                $('#cartItems').html(`
                                    <div class="empty-cart">
                                        <i class="fas fa-shopping-cart"></i>
                                        <p>Your cart is empty</p>
                                        <button class="continue-shopping-btn" onclick="$('#cartModal').removeClass('active')">
                                            <i class="fas fa-arrow-left"></i> Continue Shopping
                                        </button>
                                    </div>
                                `);
                                $('#bookTests').prop('disabled', true);
                                $('#subtotal').text('₹0.00');
                                $('#discount').text('-₹0.00');
                                $('#cartTotal').text('₹0.00');
                                $('.cart-footer').css('opacity', '0.5');
                            } else {
                                $('.cart-footer').css('opacity', '1');
                                $('#cartItems').empty();
                                let subtotal = 0;
                                let discount = response.discount || 0;
                                let total = response.total || 0;

                                response.cart.forEach(item => {
                                    const itemTotal = item.price * item.quantity;
                                    subtotal += itemTotal;
                                    $('#cartItems').append(`
                                        <div class="cart-item" data-test-id="${item.test_id}">
                                            <div class="cart-item-info">
                                                <div class="cart-item-name">${item.name}</div>
                                                <div class="cart-item-details">
                                                    <span>₹${item.price.toFixed(2)} each</span>
                                                    <span>•</span>
                                                    <span>Qty: ${item.quantity}</span>
                                                </div>
                                            </div>
                                            <div class="cart-item-controls">
                                                <div class="quantity-controls">
                                                    <button class="quantity-btn minus-btn" data-test-id="${item.test_id}">-</button>
                                                    <span class="quantity-value">${item.quantity}</span>
                                                    <button class="quantity-btn plus-btn" data-test-id="${item.test_id}">+</button>
                                                </div>
                                                <button class="remove-item-btn" data-test-id="${item.test_id}" title="Remove item">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <div class="cart-item-price">₹${itemTotal.toFixed(2)}</div>
                                        </div>
                                    `);
                                });

                                if (typeof response.discount === 'undefined') {
                                    discount = 0;
                                }
                                if (typeof response.total === 'undefined') {
                                    total = subtotal - discount;
                                }
                                $('#subtotal').text(`₹${subtotal.toFixed(2)}`);
                                $('#discount').text(`-₹${discount.toFixed(2)}`);
                                $('#cartTotal').text(`₹${total.toFixed(2)}`);
                                $('#bookTests').prop('disabled', false);
                            }
                            $('#cart-count').text(response.count);
                        } else {
                            $('#cartItems').html(`
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <p>Error loading cart</p>
                                </div>
                            `);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Load cart error:', {xhr, status, error});
                        $('#cartItems').html(`
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                <p>Error loading cart</p>
                            </div>
                        `);
                    }
                });
            }

            // Open Cart Modal
            $('#cartIcon').click(function() {
                console.log('Cart icon clicked');
                $('#cartModal').addClass('active');
                loadCart();
            });

            // Close Cart Modal
            $('#closeCart').click(function() {
                $('#cartModal').removeClass('active');
            });

            // Continue Shopping Button
            $(document).on('click', '#continueShopping', function() {
                $('#cartModal').removeClass('active');
            });

            // Update quantity in cart
            $(document).on('click', '.quantity-btn', function() {
                const testId = $(this).data('test-id');
                const action = $(this).hasClass('plus-btn') ? 'increment' : 'decrement';

                $.ajax({
                    url: `/cart/update/${testId}`,
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ action: action }),
                    success: function(response) {
                        if (response.status === 'success') {
                            loadCart();
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Update cart error:', {xhr, status, error});
                        alert('Error updating cart');
                    }
                });
            });

            // Remove item from cart
            $(document).on('click', '.remove-item-btn', function() {
                const testId = $(this).data('test-id');

                if (confirm('Are you sure you want to remove this item from your cart?')) {
                    $.ajax({
                        url: `/cart/update/${testId}`,
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ action: 'remove' }),
                        success: function(response) {
                            if (response.status === 'success') {
                                loadCart();
                            } else {
                                alert(response.message);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Remove cart item error:', {xhr, status, error});
                            alert('Error removing item from cart');
                        }
                    });
                }
            });

            // Add coupon validation
            $('#applyCoupon').click(function() {
                const couponCode = $('#couponCode').val().trim();
                const couponMessage = $('#couponMessage');
                const applyButton = $('#applyCoupon');
                const removeButton = $('#removeCoupon');
                
                couponMessage.show();
                
                if (!couponCode) {
                    couponMessage.removeClass('success').addClass('error');
                    couponMessage.find('i').removeClass('fa-check-circle').addClass('fa-exclamation-circle');
                    couponMessage.find('span').text('Please enter a coupon code');
                    return;
                }

                const originalButtonText = applyButton.html();
                applyButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Applying...');

                $.ajax({
                    url: '/validate_coupon',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ coupon_code: couponCode }),
                    success: function(response) {
                        if (response.status === 'success') {
                            const discount = response.discount;
                            const subtotal = parseFloat($('#subtotal').text().replace('₹', ''));
                            const newTotal = subtotal - discount;
                            
                            $('#discount').text(`-₹${discount.toFixed(2)}`);
                            $('#cartTotal').text(`₹${newTotal.toFixed(2)}`);
                            
                            couponMessage.removeClass('error').addClass('success');
                            couponMessage.find('i').removeClass('fa-exclamation-circle').addClass('fa-check-circle');
                            couponMessage.find('span').text(`Coupon applied successfully! ₹${discount.toFixed(2)} discount applied.`);
                            
                            applyButton.hide();
                            removeButton.show();
                            $('#couponCode').prop('disabled', true);
                        } else {
                            couponMessage.removeClass('success').addClass('error');
                            couponMessage.find('i').removeClass('fa-check-circle').addClass('fa-exclamation-circle');
                            couponMessage.find('span').text(response.message || 'Invalid coupon code');
                            applyButton.prop('disabled', false).html(originalButtonText);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Coupon validation error:', {xhr, status, error});
                        couponMessage.removeClass('success').addClass('error');
                        couponMessage.find('i').removeClass('fa-check-circle').addClass('fa-exclamation-circle');
                        let errorMessage = 'Error validating coupon code. Please try again.';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            console.error('Error parsing response:', e);
                        }
                        couponMessage.find('span').text(errorMessage);
                        applyButton.prop('disabled', false).html(originalButtonText);
                    }
                });
            });

            // Remove coupon
            $('#removeCoupon').click(function() {
                const couponMessage = $('#couponMessage');
                const applyButton = $('#applyCoupon');
                const removeButton = $('#removeCoupon');
                
                removeButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Removing...');
                
                $.ajax({
                    url: '/remove_coupon',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ action: 'remove' }),
                    success: function(response) {
                        $('#couponCode').val('').prop('disabled', false);
                        $('#discount').text('-₹0.00');
                        const subtotal = parseFloat($('#subtotal').text().replace('₹', ''));
                        $('#cartTotal').text(`₹${subtotal.toFixed(2)}`);
                        couponMessage.removeClass('error').addClass('success');
                        couponMessage.find('i').removeClass('fa-exclamation-circle').addClass('fa-check-circle');
                        couponMessage.find('span').text(response.message || 'Coupon removed successfully');
                        couponMessage.show();
                        applyButton.show().prop('disabled', false).html('<i class="fas fa-check"></i> Apply');
                        removeButton.hide();
                        setTimeout(() => {
                            couponMessage.fadeOut();
                        }, 3000);
                    },
                    error: function(xhr, status, error) {
                        console.error('Remove coupon error:', {xhr, status, error});
                        couponMessage.removeClass('success').addClass('error');
                        couponMessage.find('i').removeClass('fa-check-circle').addClass('fa-exclamation-circle');
                        let errorMessage = 'Error removing coupon. Please try again.';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            console.error('Error parsing response:', e);
                        }
                        couponMessage.find('span').text(errorMessage);
                        couponMessage.show();
                        removeButton.prop('disabled', false).html('<i class="fas fa-times"></i> Remove');
                    }
                });
            });

            // Update coupon input
            $('#couponCode').on('input', function() {
                const couponMessage = $('#couponMessage');
                const applyButton = $('#applyCoupon');
                const removeButton = $('#removeCoupon');
                
                if ($(this).prop('disabled') && $(this).val().trim() !== '') {
                    removeButton.show();
                } else {
                    couponMessage.removeClass('success error').hide();
                    couponMessage.find('span').text('');
                    if (applyButton.prop('disabled')) {
                        applyButton.prop('disabled', false).html('<i class="fas fa-check"></i> Apply');
                    }
                }
            });

            // Book Tests
            $('#bookTests').click(function() {
                if (!validateBillingForm()) {
                    return;
                }

                const billingData = {
                    fullName: $('#fullName').val(),
                    email: $('#email').val(),
                    phone: $('#phone').val(),
                    address: $('#address').val(),
                    city: $('#city').val(),
                    state: $('#state').val(),
                    pincode: $('#pincode').val(),
                    couponCode: $('#couponCode').val()
                };

                $.ajax({
                    url: '/store_billing_info',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(billingData),
                    success: function(response) {
                        if (response.status === 'success') {
                            $.get('/cart', function(response) {
                                if (response.status === 'success' && response.cart.length > 0) {
                                    $.ajax({
                                        url: '/book_tests',
                                        method: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify({
                                            tests: response.cart,
                                            total_amount: parseFloat($('#cartTotal').text().replace('₹', '')),
                                            billing_info: billingData
                                        }),
                                        success: function(orderData) {
                                            if (orderData.status === 'error') {
                                                alert(orderData.message);
                                                return;
                                            }

                                            $.ajax({
                                                url: '/store_billing_info',
                                                method: 'POST',
                                                contentType: 'application/json',
                                                data: JSON.stringify(billingData),
                                                success: function() {
                                                    var options = {
                                                        key: orderData.key,
                                                        amount: orderData.amount,
                                                        currency: "INR",
                                                        name: "CVBioLabs",
                                                        description: "Test Booking Payment",
                                                        order_id: orderData.order_id,
                                                        handler: function(response) {
                                                            verifyPayment(response);
                                                        },
                                                        prefill: {
                                                            name: billingData.fullName,
                                                            email: billingData.email,
                                                            contact: billingData.phone
                                                        },
                                                        theme: {
                                                            color: "#f47c20"
                                                        }
                                                    };
                                                    var rzp = new Razorpay(options);
                                                    rzp.open();
                                                },
                                                error: function() {
                                                    alert('Error storing billing information. Please try again.');
                                                }
                                            });
                                        },
                                        error: function() {
                                            alert('Error creating payment order. Please try again.');
                                        }
                                    });
                                } else {
                                    alert('Your cart is empty');
                                }
                            });
                        } else {
                            alert('Error storing billing information. Please try again.');
                        }
                    },
                    error: function() {
                        alert('Error storing billing information. Please try again.');
                    }
                });
            });

            // Flash message handling
            const flashMessage = $('#flashMessage');
            if (flashMessage.length) {
                $('#cartModal').removeClass('active');
                setTimeout(() => {
                    flashMessage.addClass('show');
                }, 100);
                setTimeout(() => {
                    flashMessage.removeClass('show');
                    setTimeout(() => {
                        flashMessage.remove();
                    }, 500);
                }, 3000);
            }

            // Clear cart on page load
            if (window.location.pathname === '/test') {
                sessionStorage.removeItem('cart');
                $('#cart-count').text('0');
            }
        });

        function validateBillingForm() {
            const requiredFields = ['fullName', 'email', 'phone', 'address', 'city', 'state', 'pincode'];
            let isValid = true;

            requiredFields.forEach(field => {
                const value = $(`#${field}`).val().trim();
                if (!value) {
                    $(`#${field}`).addClass('error');
                    isValid = false;
                } else {
                    $(`#${field}`).removeClass('error');
                }
            });

            if (!isValid) {
                alert('Please fill in all required fields');
            }

            return isValid;
        }

        function verifyPayment(response) {
            console.log('Razorpay response:', response);
            if (!response.razorpay_payment_id || !response.razorpay_order_id || !response.razorpay_signature) {
                console.error('Missing required Razorpay fields:', response);
                alert('Payment verification failed: Missing required fields');
                return;
            }
            showLoading();
            $('#cartModal').removeClass('active');
            $.get('/cart', function(cartResponse) {
                if (cartResponse.status === 'success') {
                    const cartData = cartResponse.cart;
                    $.ajax({
                        url: '/verify_payment',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            payment_id: response.razorpay_payment_id,
                            order_id: response.razorpay_order_id,
                            signature: response.razorpay_signature
                        }),
                        success: function(result) {
                            console.log('Verification result:', result);
                            if (result.status === 'success') {
                                sessionStorage.removeItem('cart');
                                sendConfirmationEmail(cartData);
                            } else {
                                hideLoading();
                                window.location.href = '/payment_error';
                            }
                        },
                        error: function(xhr, status, error) {
                            hideLoading();
                            console.error('Payment verification error:', {status, error, response: xhr.responseText});
                            window.location.href = '/payment_error';
                        }
                    });
                } else {
                    hideLoading();
                    console.error('Failed to get cart data');
                    window.location.href = '/payment_error';
                }
            });
        }

        function sendConfirmationEmail(cartData) {
            const billingData = {
                fullName: $('#fullName').val(),
                email: $('#email').val(),
                phone: $('#phone').val(),
                address: $('#address').val(),
                city: $('#city').val(),
                state: $('#state').val(),
                pincode: $('#pincode').val()
            };
            sessionStorage.removeItem('cart');
            $.ajax({
                url: '/send_confirmation_email',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    email: billingData.email,
                    name: billingData.fullName,
                    amount: parseFloat($('#cartTotal').text().replace('₹', '')),
                    tests: cartData,
                    billing_info: billingData
                }),
                success: function(response) {
                    $('#cart-count').text('0');
                    hideLoading();
                    if (response.status === 'success') {
                        window.location.replace('/payment_success');
                    } else {
                        window.location.replace('/payment_error');
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    console.error('Email confirmation error:', {status, error, response: xhr.responseText});
                    window.location.replace('/payment_error');
                }
            });
        }
    </script>
</body>
</html>