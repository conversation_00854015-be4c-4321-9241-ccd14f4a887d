<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - CVBioLabs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        :root {
            --dark-blue: #002f6c;
            --orange: #f47c20;
            --light-blue: #e6f7ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--light-blue) 0%, white 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 2rem;
            text-align: center;
        }

        .logo {
            color: var(--dark-blue);
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .payment-details {
            background: var(--light-blue);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        .amount {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--dark-blue);
            margin-bottom: 0.5rem;
        }

        .email {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .expiry {
            color: #888;
            font-size: 0.9rem;
        }

        .pay-button {
            width: 100%;
            padding: 15px;
            background: var(--orange);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1.5rem;
        }

        .pay-button:hover {
            background: #e06b15;
            transform: translateY(-2px);
        }

        .pay-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .security-info {
            margin-top: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .loading {
            display: none;
            margin-top: 1rem;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--orange);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Flash Messages */
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .flash-message {
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            font-size: 14px;
            animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            box-shadow: 0 8px 24px -4px rgba(0, 0, 0, 0.2);
        }

        .flash-message.success {
            background: linear-gradient(to right, #22c55e, #4ade80);
        }

        .flash-message.error {
            background: linear-gradient(to right, #ef4444, #f87171);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 1.5rem;
                max-width: 100%;
            }

            .amount {
                font-size: 2rem;
            }

            .logo {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="container">
        <div class="logo">
            <i class="fas fa-flask"></i> CVBioLabs
        </div>
        
        <h2>Payment Request</h2>
        
        <div class="payment-details">
            <div class="amount">₹{{ "%.2f"|format(payment_link.amount) }}</div>
            <div class="email">{{ payment_link.email }}</div>
            <div class="expiry">
                <i class="fas fa-clock"></i> 
                Expires: {{ payment_link.expiry_date.strftime('%Y-%m-%d at %H:%M') }}
            </div>
        </div>

        <button class="pay-button" onclick="initiatePayment()" id="payButton">
            <i class="fas fa-credit-card"></i> Pay Now
        </button>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Processing payment...</p>
        </div>

        <div class="security-info">
            <i class="fas fa-shield-alt"></i>
            Your payment is secured by Razorpay with 256-bit SSL encryption
        </div>
    </div>

    <script>
        function showLoading() {
            document.getElementById('payButton').style.display = 'none';
            document.getElementById('loading').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('payButton').style.display = 'block';
            document.getElementById('loading').style.display = 'none';
        }

        function initiatePayment() {
            showLoading();
            
            fetch(`/payment/link/{{ payment_link.link_id }}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                
                if (data.status === 'success') {
                    var options = {
                        key: data.key,
                        amount: data.amount,
                        currency: data.currency,
                        name: "CVBioLabs",
                        description: "Payment via Link",
                        order_id: data.order_id,
                        handler: function(response) {
                            // Verify payment
                            fetch('/verify_payment_link', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    payment_id: response.razorpay_payment_id,
                                    order_id: response.razorpay_order_id,
                                    signature: response.razorpay_signature,
                                    link_id: '{{ payment_link.link_id }}',
                                    amount: {{ payment_link.amount }}
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.status === 'success') {
                                    alert('Payment successful! Thank you for your payment.');
                                    window.location.href = '/payment_success';
                                } else {
                                    alert('Payment verification failed: ' + (data.message || 'Unknown error'));
                                }
                            })
                            .catch(error => {
                                console.error('Payment verification error:', error);
                                alert('Payment verification failed. Please contact support.');
                            });
                        },
                        prefill: {
                            email: data.email
                        },
                        theme: {
                            color: "#f47c20"
                        },
                        modal: {
                            ondismiss: function() {
                                alert('Payment cancelled');
                            }
                        }
                    };

                    var rzp = new Razorpay(options);
                    rzp.on('payment.failed', function(response) {
                        alert('Payment failed: ' + response.error.description);
                    });
                    rzp.open();
                } else {
                    alert('Error: ' + (data.error || 'Failed to initiate payment'));
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                alert('Error initiating payment. Please try again.');
            });
        }

        // Auto-hide flash messages after 5 seconds
        setTimeout(function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                message.style.opacity = '0';
                setTimeout(function() {
                    message.remove();
                }, 300);
            });
        }, 5000);
    </script>
</body>
</html>
