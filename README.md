# CVBioLabs - Laboratory Management System

A comprehensive web-based laboratory management system built with Flask, featuring patient management, test booking, sample collection, report generation, and multi-role access control.

## 🚀 Features

- **Patient Portal**: Test booking, report viewing, payment integration
- **Doctor Dashboard**: Report verification, patient management
- **Staff Interface**: Receptionist tools, sample tracking, agent management
- **Admin Panel**: User management, system configuration, analytics
- **Pickup Agent App**: Sample collection tracking, status updates
- **Payment Integration**: Razorpay payment gateway
- **Security**: reCAPTCHA, CSRF protection, role-based access control

## 📋 Prerequisites

- Python 3.8 or higher
- MySQL 5.7 or higher
- Redis (optional, for session storage)
- Modern web browser

## 🛠️ Installation

### Option 1: Automatic Installation (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cvbiolabs
   ```

2. **Run the installation script**
   ```bash
   python install_dependencies.py
   ```

3. **Follow the prompts** to create a virtual environment and install dependencies

### Option 2: Manual Installation

1. **Create a virtual environment** (recommended)
   ```bash
   python -m venv cvbiolabs_env
   
   # Activate on Windows
   cvbiolabs_env\Scripts\activate
   
   # Activate on Unix/Linux/Mac
   source cvbiolabs_env/bin/activate
   ```

2. **Install dependencies**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

## ⚙️ Configuration

1. **Copy environment file**
   ```bash
   cp .env.example .env  # If .env.example exists
   # OR create .env manually
   ```

2. **Configure .env file**
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=your_mysql_password
   DB_NAME=cvbiolabs
   DB_CHARSET=utf8mb4

   # Flask Configuration
   SECRET_KEY=your-very-strong-secret-key-here
   FLASK_ENV=production
   FLASK_DEBUG=False
   PORT=7000

   # Email Configuration
   MAIL_SERVER=smtp.gmail.com
   MAIL_PORT=587
   MAIL_USE_TLS=True
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your_app_password
   MAIL_DEFAULT_SENDER=<EMAIL>

   # Payment Gateway (Razorpay)
   RAZORPAY_KEY_ID=your_razorpay_key_id
   RAZORPAY_KEY_SECRET=your_razorpay_key_secret

   # reCAPTCHA (optional)
   RECAPTCHA_SITE_KEY=your_recaptcha_site_key
   RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key

   # Admin Credentials
   ADMIN_USERNAME=<EMAIL>
   ADMIN_PASSWORD=admin123

   # CORS Configuration (comma-separated origins)
   CORS_ORIGINS=http://localhost:7000,http://127.0.0.1:7000

   # File Upload Configuration
   UPLOAD_FOLDER=uploads
   ```

## 🗄️ Database Setup

1. **Create MySQL database**
   ```sql
   CREATE DATABASE cvbiolabs CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **Initialize database tables**
   ```bash
   python init_database.py
   ```

   OR run the SQL script manually:
   ```bash
   mysql -u root -p cvbiolabs < create_tables.sql
   ```

3. **Create admin users**
   ```bash
   python setup_staff_user.py
   ```

## 🚀 Running the Application

1. **Start the Flask application**
   ```bash
   python app.py
   ```

2. **Access the application**
   - Main site: http://localhost:7000 (or your configured PORT)
   - Admin panel: http://localhost:7000/admin
   - Doctor portal: http://localhost:7000/doctor
   - Staff interface: http://localhost:7000/staff
   - Pickup agent: http://localhost:7000/pickup/login

## 👥 Default Login Credentials

### Admin
- Email: <EMAIL>
- Password: admin123

### Test Pickup Agent
- Email: <EMAIL>
- Password: (check database or create new)

## 📁 Project Structure

```
cvbiolabs/
├── app.py                 # Main Flask application
├── admin.py              # Admin panel blueprint
├── doctor.py             # Doctor dashboard blueprint
├── staff.py              # Staff interface blueprint
├── requirements.txt      # Python dependencies
├── .env.example          # Environment configuration template
├── create_tables.sql     # Database schema
├── init_database.py      # Database initialization script
├── setup_staff_user.py   # Create default admin users
├── templates/            # HTML templates
│   ├── home.html
│   ├── login_signup.html
│   ├── pickup.html
│   └── ...
├── static/               # Static files (CSS, JS, images)
│   └── images/
└── uploads/              # File uploads directory
```

## 🚀 Production Deployment

### Environment Configuration
For production deployment, ensure your `.env` file has:
```env
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-very-strong-production-secret-key
CORS_ORIGINS=https://yourdomain.com
PORT=80
```

### Security Considerations
- Use strong, unique SECRET_KEY
- Configure proper CORS origins
- Set up SSL/HTTPS
- Use environment variables for all sensitive data
- Regularly update dependencies

### Server Deployment
The application is configured to run on any machine with:
- Python 3.8+
- MySQL database
- Required environment variables

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check MySQL service is running
   - Verify credentials in .env file
   - Ensure database exists

2. **reCAPTCHA Errors**
   - Check browser console for extension conflicts
   - Verify reCAPTCHA keys in .env
   - Use development mode for testing

3. **Import Errors**
   - Ensure virtual environment is activated
   - Run: `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

4. **Pickup Button Not Working**
   - Check browser console for JavaScript errors
   - Verify agent is logged in
   - Check Flask logs for detailed error messages

### Debug Mode

Enable detailed logging by setting in .env:
```env
FLASK_DEBUG=1
FLASK_ENV=development
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Phone: +91 **********
- WhatsApp: Available in the application

## 🔄 Updates

Check for updates regularly and run:
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```
