#!/usr/bin/env python3
"""
Convert CVBioLabs logo to base64 for email embedding
"""

import base64
import os

def convert_logo_to_base64():
    """Convert the logo image to base64 string"""
    logo_path = "static/images/CV.png"
    
    if not os.path.exists(logo_path):
        print(f"❌ Logo file not found: {logo_path}")
        return None
    
    try:
        with open(logo_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        
        print(f"✅ Logo converted to base64")
        print(f"📏 Base64 length: {len(encoded_string):,} characters")
        
        # Create the data URL
        data_url = f"data:image/png;base64,{encoded_string}"
        
        # Save to file for use in templates
        with open("logo_base64.txt", "w") as f:
            f.write(data_url)
        
        print(f"✅ Base64 data URL saved to: logo_base64.txt")
        
        # Show first 100 characters as preview
        print(f"📋 Preview: {data_url[:100]}...")
        
        return data_url
        
    except Exception as e:
        print(f"❌ Error converting logo: {e}")
        return None

def create_email_template_with_logo():
    """Create an enhanced email template with embedded logo"""
    base64_logo = convert_logo_to_base64()
    
    if not base64_logo:
        print("⚠️  Using text logo fallback")
        return
    
    # Create template snippet with embedded logo
    template_snippet = f'''
<!-- Gmail-Compatible Logo Header -->
<div class="email-header">
    <div class="logo-container" style="text-align: center; margin-bottom: 15px;">
        <!-- Base64 Embedded Logo (Gmail Compatible) -->
        <img src="{base64_logo}" 
             alt="CVBioLabs Logo" 
             style="height: 60px; width: auto; max-width: 200px; display: inline-block; margin-bottom: 10px;"
             onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
        
        <!-- Professional Text Logo Fallback -->
        <div class="text-logo-fallback" style="display: none;">
            <div style="display: inline-block; background: rgba(255,255,255,0.15); padding: 15px 25px; border-radius: 50px; margin-bottom: 10px;">
                <div style="color: #ffffff; font-size: 32px; font-weight: bold; letter-spacing: 2px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                    <span style="color: #ffffff;">CV</span><span style="color: #e6f7ff;">BioLabs</span>
                </div>
            </div>
        </div>
        
        <!-- Decorative elements -->
        <div style="display: flex; justify-content: center; align-items: center; margin: 10px 0;">
            <div style="width: 30px; height: 2px; background: #ffffff; margin: 0 10px;"></div>
            <div style="color: #e6f7ff; font-size: 20px;">
                <i class="fas fa-microscope"></i>
            </div>
            <div style="width: 30px; height: 2px; background: #ffffff; margin: 0 10px;"></div>
        </div>
    </div>
    <div class="tagline" style="color: #e6f7ff; font-size: 16px; font-weight: 400; margin-top: 5px;">
        For a Healthy Life - Advanced Medical Diagnostics
    </div>
</div>
'''
    
    # Save template snippet
    with open("email_header_with_logo.html", "w") as f:
        f.write(template_snippet)
    
    print(f"✅ Email template snippet saved to: email_header_with_logo.html")

def main():
    """Main function"""
    print("🖼️  CVBioLabs Logo Base64 Converter")
    print("=" * 40)
    
    # Convert logo
    base64_logo = convert_logo_to_base64()
    
    if base64_logo:
        # Create template
        create_email_template_with_logo()
        
        print(f"\n🎯 Next Steps:")
        print(f"   1. The base64 logo is ready for Gmail")
        print(f"   2. Update your email template with the embedded logo")
        print(f"   3. Test the email to see the logo in Gmail")
        
        # Show size warning if too large
        if len(base64_logo) > 100000:  # 100KB
            print(f"\n⚠️  Warning: Base64 logo is large ({len(base64_logo):,} chars)")
            print(f"   Consider optimizing the image for better email performance")
    else:
        print(f"\n❌ Logo conversion failed")
        print(f"   Using text logo fallback instead")

if __name__ == '__main__':
    main()
