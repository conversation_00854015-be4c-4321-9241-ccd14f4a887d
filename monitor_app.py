#!/usr/bin/env python3
"""
Application monitoring script for CVBioLabs Flask app
Monitors logs, rate limits, and security events
"""

import os
import re
import time
import json
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dotenv import load_dotenv

load_dotenv()

class AppMonitor:
    """Monitor Flask application logs and metrics"""
    
    def __init__(self):
        self.rate_limit_events = deque(maxlen=100)
        self.ssl_errors = deque(maxlen=50)
        self.failed_logins = deque(maxlen=100)
        self.error_patterns = {
            'rate_limit': re.compile(r'flask-limiter.*exceeded'),
            'ssl_handshake': re.compile(r'Bad request version.*h2|SSL'),
            'failed_login': re.compile(r'Invalid email or password'),
            'malformed_request': re.compile(r'Bad request version')
        }
    
    def parse_log_line(self, line):
        """Parse a single log line and extract relevant information"""
        timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})', line)
        if not timestamp_match:
            return None
        
        timestamp = timestamp_match.group(1)
        
        # Check for different types of events
        if 'flask-limiter' in line and 'exceeded' in line:
            ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', line)
            endpoint_match = re.search(r'endpoint: (\w+)', line)
            return {
                'type': 'rate_limit',
                'timestamp': timestamp,
                'ip': ip_match.group(1) if ip_match else 'unknown',
                'endpoint': endpoint_match.group(1) if endpoint_match else 'unknown',
                'raw': line.strip()
            }
        
        elif 'Bad request version' in line:
            ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', line)
            return {
                'type': 'malformed_request',
                'timestamp': timestamp,
                'ip': ip_match.group(1) if ip_match else 'unknown',
                'raw': line.strip()
            }
        
        elif 'Invalid email or password' in line:
            return {
                'type': 'failed_login',
                'timestamp': timestamp,
                'raw': line.strip()
            }
        
        return None
    
    def monitor_logs(self, log_file=None, tail_lines=100):
        """Monitor application logs"""
        if not log_file:
            # Try to find log files
            possible_logs = ['app.log', 'flask.log', 'cvbiolabs.log']
            for log in possible_logs:
                if os.path.exists(log):
                    log_file = log
                    break
        
        if not log_file or not os.path.exists(log_file):
            print("⚠️  No log file found. Monitoring console output...")
            return self.monitor_console()
        
        print(f"📊 Monitoring log file: {log_file}")
        
        # Read last N lines
        try:
            with open(log_file, 'r') as f:
                lines = deque(f, maxlen=tail_lines)
            
            events = []
            for line in lines:
                event = self.parse_log_line(line)
                if event:
                    events.append(event)
            
            return self.analyze_events(events)
            
        except Exception as e:
            print(f"❌ Error reading log file: {e}")
            return None
    
    def monitor_console(self):
        """Monitor console output when no log file is available"""
        print("💡 To monitor logs effectively, consider:")
        print("   1. Redirecting Flask output to a log file")
        print("   2. Using the logging configuration in your app")
        print("   3. Running: python app.py > app.log 2>&1")
        return None
    
    def analyze_events(self, events):
        """Analyze collected events and provide insights"""
        if not events:
            print("✅ No significant events found in recent logs")
            return
        
        # Group events by type
        by_type = defaultdict(list)
        for event in events:
            by_type[event['type']].append(event)
        
        print(f"\n📈 Analysis of {len(events)} events:")
        print("=" * 50)
        
        # Rate limit analysis
        if by_type['rate_limit']:
            print(f"\n🚫 Rate Limit Events: {len(by_type['rate_limit'])}")
            ip_counts = defaultdict(int)
            endpoint_counts = defaultdict(int)
            
            for event in by_type['rate_limit']:
                ip_counts[event['ip']] += 1
                endpoint_counts[event['endpoint']] += 1
            
            print("   Top IPs:")
            for ip, count in sorted(ip_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"     {ip}: {count} violations")
            
            print("   Affected endpoints:")
            for endpoint, count in endpoint_counts.items():
                print(f"     {endpoint}: {count} violations")
        
        # Malformed request analysis
        if by_type['malformed_request']:
            print(f"\n🔧 Malformed Requests: {len(by_type['malformed_request'])}")
            ip_counts = defaultdict(int)
            
            for event in by_type['malformed_request']:
                ip_counts[event['ip']] += 1
            
            print("   Source IPs:")
            for ip, count in sorted(ip_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"     {ip}: {count} malformed requests")
            
            print("   💡 These are likely SSL handshake attempts on HTTP port")
        
        # Failed login analysis
        if by_type['failed_login']:
            print(f"\n🔐 Failed Logins: {len(by_type['failed_login'])}")
            print("   💡 Monitor for potential brute force attacks")
        
        return by_type
    
    def get_recommendations(self, events_by_type):
        """Provide recommendations based on analysis"""
        print(f"\n💡 Recommendations:")
        print("=" * 30)
        
        if events_by_type and events_by_type.get('rate_limit'):
            print("🚫 Rate Limiting Issues:")
            print("   • Consider increasing rate limits for legitimate users")
            print("   • Implement IP whitelisting for trusted sources")
            print("   • Use Redis for distributed rate limiting")
            print("   • Run: python clear_rate_limits.py (in development)")
        
        if events_by_type and events_by_type.get('malformed_request'):
            print("\n🔧 Malformed Requests:")
            print("   • These are likely SSL handshake attempts")
            print("   • Consider setting up proper HTTPS with SSL termination")
            print("   • Use a reverse proxy (nginx) to handle SSL")
            print("   • Filter these requests at the network level")
        
        if events_by_type and events_by_type.get('failed_login'):
            print("\n🔐 Security:")
            print("   • Monitor for brute force patterns")
            print("   • Consider implementing CAPTCHA after failures")
            print("   • Use account lockout policies")
            print("   • Enable 2FA for sensitive accounts")
        
        print("\n🛠️  General Recommendations:")
        print("   • Use HTTPS in production")
        print("   • Implement proper logging and monitoring")
        print("   • Set up health checks")
        print("   • Use environment-specific configurations")

def main():
    """Main monitoring function"""
    print("🔍 CVBioLabs Application Monitor")
    print("=" * 40)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    monitor = AppMonitor()
    events_by_type = monitor.monitor_logs()
    
    if events_by_type:
        monitor.get_recommendations(events_by_type)
    
    print(f"\n✅ Monitoring complete")
    print("💡 Run this script regularly to track application health")

if __name__ == '__main__':
    main()
