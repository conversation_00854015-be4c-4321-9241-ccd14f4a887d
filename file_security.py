"""
File security utilities for CVBioLabs application
Provides secure file upload, validation, and storage
"""

import os
import uuid
import hashlib
import mimetypes
import time
from pathlib import Path
from werkzeug.utils import secure_filename
import logging

logger = logging.getLogger(__name__)

# Try to import python-magic for content-based file type detection
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
    logger.warning("python-magic not available. Install with: pip install python-magic")

class SecureFileHandler:
    """Secure file upload and validation utilities"""
    
    # Allowed file types with their MIME types and extensions
    ALLOWED_FILE_TYPES = {
        'pdf': {
            'mime_types': ['application/pdf'],
            'extensions': ['.pdf'],
            'max_size': 10 * 1024 * 1024,  # 10MB
            'description': 'PDF Document'
        },
        'image': {
            'mime_types': ['image/jpeg', 'image/png', 'image/gif'],
            'extensions': ['.jpg', '.jpeg', '.png', '.gif'],
            'max_size': 5 * 1024 * 1024,  # 5MB
            'description': 'Image File'
        },
        'document': {
            'mime_types': [
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain'
            ],
            'extensions': ['.doc', '.docx', '.txt'],
            'max_size': 10 * 1024 * 1024,  # 10MB
            'description': 'Document File'
        }
    }
    
    # Dangerous file extensions that should never be allowed
    DANGEROUS_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
        '.php', '.asp', '.aspx', '.jsp', '.py', '.pl', '.sh', '.ps1'
    }
    
    def __init__(self, upload_folder='uploads'):
        self.upload_folder = Path(upload_folder)
        self.upload_folder.mkdir(exist_ok=True)
    
    def validate_file_extension(self, filename):
        """Validate file extension against allowed types"""
        if not filename:
            return False, "No filename provided"
        
        file_ext = Path(filename).suffix.lower()
        
        # Check for dangerous extensions
        if file_ext in self.DANGEROUS_EXTENSIONS:
            return False, f"File type {file_ext} is not allowed for security reasons"
        
        # Check if extension is in allowed types
        for file_type, config in self.ALLOWED_FILE_TYPES.items():
            if file_ext in config['extensions']:
                return True, file_type
        
        return False, f"File extension {file_ext} is not allowed"
    
    def validate_file_content(self, file_path):
        """Validate file content using MIME type detection"""
        if not HAS_MAGIC:
            logger.warning("Content validation skipped - python-magic not available")
            return True, "Content validation skipped"
        
        try:
            # Detect MIME type from file content
            mime_type = magic.from_file(str(file_path), mime=True)
            
            # Check if MIME type is allowed
            for file_type, config in self.ALLOWED_FILE_TYPES.items():
                if mime_type in config['mime_types']:
                    return True, file_type
            
            return False, f"File content type {mime_type} is not allowed"
        
        except Exception as e:
            logger.error(f"Content validation failed: {e}")
            return False, "Content validation failed"
    
    def validate_file_size(self, file_size, file_type):
        """Validate file size against limits"""
        if file_type not in self.ALLOWED_FILE_TYPES:
            return False, "Unknown file type"
        
        max_size = self.ALLOWED_FILE_TYPES[file_type]['max_size']
        if file_size > max_size:
            max_size_mb = max_size / (1024 * 1024)
            return False, f"File size exceeds {max_size_mb:.1f}MB limit"
        
        return True, "File size OK"
    
    def generate_secure_filename(self, original_filename):
        """Generate a secure, unique filename"""
        # Get file extension
        file_ext = Path(original_filename).suffix.lower()
        
        # Generate unique filename using UUID and timestamp
        unique_id = uuid.uuid4().hex
        timestamp = str(int(time.time()))
        
        # Create secure filename
        secure_name = f"{unique_id}_{timestamp}{file_ext}"
        
        return secure_name
    
    def calculate_file_hash(self, file_path):
        """Calculate SHA-256 hash of file for integrity checking"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            logger.error(f"Hash calculation failed: {e}")
            return None
    
    def scan_for_malware(self, file_path):
        """
        Basic malware scanning (placeholder for real antivirus integration)
        In production, integrate with ClamAV or similar
        """
        try:
            # Basic checks for suspicious content
            with open(file_path, 'rb') as f:
                content = f.read(1024)  # Read first 1KB
                
                # Check for suspicious patterns
                suspicious_patterns = [
                    b'<script',
                    b'javascript:',
                    b'vbscript:',
                    b'<?php',
                    b'<%',
                    b'eval(',
                    b'exec(',
                    b'system(',
                ]
                
                for pattern in suspicious_patterns:
                    if pattern in content.lower():
                        return False, f"Suspicious content detected: {pattern.decode('utf-8', errors='ignore')}"
            
            return True, "No malware detected"
        
        except Exception as e:
            logger.error(f"Malware scan failed: {e}")
            return False, "Malware scan failed"
    
    def secure_upload(self, file, subfolder='general'):
        """
        Perform secure file upload with comprehensive validation
        Returns: (success, message, file_info)
        """
        try:
            if not file or not file.filename:
                return False, "No file provided", None
            
            # Validate file extension
            ext_valid, ext_result = self.validate_file_extension(file.filename)
            if not ext_valid:
                return False, ext_result, None
            
            file_type = ext_result
            
            # Validate file size
            file.seek(0, 2)  # Seek to end
            file_size = file.tell()
            file.seek(0)  # Reset to beginning
            
            size_valid, size_result = self.validate_file_size(file_size, file_type)
            if not size_valid:
                return False, size_result, None
            
            # Generate secure filename
            secure_name = self.generate_secure_filename(file.filename)
            
            # Create subfolder
            upload_path = self.upload_folder / subfolder
            upload_path.mkdir(exist_ok=True)
            
            # Save file temporarily for validation
            temp_path = upload_path / f"temp_{secure_name}"
            file.save(str(temp_path))
            
            try:
                # Validate file content
                content_valid, content_result = self.validate_file_content(temp_path)
                if not content_valid:
                    temp_path.unlink()  # Delete temp file
                    return False, content_result, None
                
                # Scan for malware
                malware_valid, malware_result = self.scan_for_malware(temp_path)
                if not malware_valid:
                    temp_path.unlink()  # Delete temp file
                    return False, malware_result, None
                
                # Calculate file hash
                file_hash = self.calculate_file_hash(temp_path)
                
                # Move to final location
                final_path = upload_path / secure_name
                temp_path.rename(final_path)
                
                # Return file information
                file_info = {
                    'original_filename': file.filename,
                    'secure_filename': secure_name,
                    'file_path': str(final_path),
                    'relative_path': f"{subfolder}/{secure_name}",
                    'file_type': file_type,
                    'file_size': file_size,
                    'file_hash': file_hash,
                    'mime_type': content_result if content_valid else None
                }
                
                return True, "File uploaded successfully", file_info
            
            except Exception as e:
                # Clean up temp file on error
                if temp_path.exists():
                    temp_path.unlink()
                raise e
        
        except Exception as e:
            logger.error(f"File upload failed: {e}")
            return False, f"Upload failed: {str(e)}", None
    
    def delete_file(self, file_path):
        """Securely delete a file"""
        try:
            file_path = Path(file_path)
            if file_path.exists() and file_path.is_file():
                # Verify file is within upload directory
                if self.upload_folder in file_path.parents:
                    file_path.unlink()
                    return True, "File deleted successfully"
                else:
                    return False, "File not in allowed directory"
            else:
                return False, "File not found"
        except Exception as e:
            logger.error(f"File deletion failed: {e}")
            return False, f"Deletion failed: {str(e)}"

# Global instance
secure_file_handler = SecureFileHandler()
