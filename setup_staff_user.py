#!/usr/bin/env python3
"""
Setup script to create initial staff users for CVBioLabs
Run this script to create default admin and staff accounts
"""

import mysql.connector
import bcrypt
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'cvbiolabs'),
    'charset': 'utf8mb4'
}

def get_db_connection():
    """Get database connection"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except mysql.connector.Error as e:
        print(f"Database connection error: {e}")
        return None

def create_staff_users():
    """Create initial staff users"""
    conn = get_db_connection()
    if not conn:
        print("❌ Failed to connect to database")
        return False
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        # Check if admin_users table exists
        cursor.execute("SHOW TABLES LIKE 'admin_users'")
        if not cursor.fetchone():
            print("❌ admin_users table doesn't exist. Please run init_database.py first")
            return False
        
        # Default staff users to create
        staff_users = [
            {
                'professional_id': 'ADM001',
                'name': 'Admin User',
                'email': '<EMAIL>',
                'password': 'admin123',
                'role': 'Admin',
                'phone': '9876543210'
            },
            {
                'professional_id': 'REC001', 
                'name': 'Receptionist',
                'email': '<EMAIL>',
                'password': 'receptionist123',
                'role': 'Receptionist',
                'phone': '9876543211'
            },
            {
                'professional_id': 'DOC001',
                'name': 'Dr. Smith',
                'email': '<EMAIL>', 
                'password': 'doctor123',
                'role': 'Doctor',
                'phone': '9876543212'
            }
        ]
        
        created_users = []
        
        for user in staff_users:
            # Check if user already exists
            cursor.execute("SELECT id FROM admin_users WHERE email = %s", (user['email'],))
            existing_user = cursor.fetchone()
            
            if existing_user:
                print(f"⚠️  User {user['email']} already exists, skipping...")
                continue
            
            # Hash password using bcrypt
            password_hash = bcrypt.hashpw(user['password'].encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # Insert user
            cursor.execute("""
                INSERT INTO admin_users (professional_id, name, email, password_hash, role, phone, status)
                VALUES (%s, %s, %s, %s, %s, %s, 'active')
            """, (
                user['professional_id'],
                user['name'], 
                user['email'],
                password_hash,
                user['role'],
                user['phone']
            ))
            
            created_users.append(user)
            print(f"✅ Created {user['role']}: {user['email']} (password: {user['password']})")
        
        # If creating a doctor, also add to doctors table
        for user in created_users:
            if user['role'] == 'Doctor':
                cursor.execute("""
                    INSERT INTO doctors (professional_id, name, email, password_hash, phone, status)
                    VALUES (%s, %s, %s, %s, %s, 'active')
                """, (
                    user['professional_id'],
                    user['name'],
                    user['email'], 
                    bcrypt.hashpw(user['password'].encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
                    user['phone']
                ))
                print(f"✅ Added {user['name']} to doctors table")
        
        conn.commit()
        
        if created_users:
            print(f"\n🎉 Successfully created {len(created_users)} staff users!")
            print("\n📋 Login Credentials:")
            print("=" * 50)
            for user in created_users:
                print(f"Role: {user['role']}")
                print(f"Email: {user['email']}")
                print(f"Password: {user['password']}")
                print(f"Professional ID: {user['professional_id']}")
                print("-" * 30)
        else:
            print("ℹ️  All users already exist in the database")
            
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
        conn.rollback()
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def verify_staff_users():
    """Verify that staff users can be found"""
    conn = get_db_connection()
    if not conn:
        return False
        
    try:
        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT professional_id, name, email, role, status FROM admin_users ORDER BY role")
        users = cursor.fetchall()
        
        if users:
            print("\n📋 Current Staff Users in Database:")
            print("=" * 60)
            for user in users:
                print(f"ID: {user['professional_id']} | {user['name']} | {user['email']} | {user['role']} | {user['status']}")
        else:
            print("❌ No staff users found in database")
            
        return len(users) > 0
        
    except Exception as e:
        print(f"❌ Error verifying users: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("🚀 CVBioLabs Staff User Setup")
    print("=" * 40)
    
    # Verify database connection
    print("🔍 Checking database connection...")
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database. Please check your .env file and database settings.")
        exit(1)
    conn.close()
    print("✅ Database connection successful")
    
    # Create staff users
    print("\n👥 Creating staff users...")
    if create_staff_users():
        print("\n🔍 Verifying created users...")
        verify_staff_users()
        
        print("\n🎯 Next Steps:")
        print("1. Start your Flask application: python app.py")
        print("2. Go to: http://localhost:7000/login")
        print("3. Use the credentials shown above to login")
        print("4. Staff users will be redirected to: http://localhost:7000/staff")
        
    else:
        print("❌ Failed to create staff users")
        exit(1)
