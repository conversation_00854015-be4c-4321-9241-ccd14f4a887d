{% extends "ADMIN/adminbase.html" %}

{% block title %}Test Management - Admin{% endblock %}
{% block page_title %}Test Management{% endblock %}

{% block extra_css %}
<style>
    .test-management-container {
        padding: 2rem;
        background: #f6f8fa;
        min-height: calc(100vh - 120px);
    }

    .test-header {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
    }

    .test-header h2 {
        font-family: 'Poppins', sans-serif;
        font-size: 1.75rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .test-header h2 i {
        color: #f58220;
        font-size: 1.5rem;
    }

    .test-header p {
        color: #6b7280;
        margin-bottom: 0;
        font-size: 1rem;
    }

    .test-controls {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
    }

    .controls-row {
        display: flex;
        gap: 1rem;
        align-items: end;
        flex-wrap: wrap;
    }

    .control-group {
        flex: 1;
        min-width: 200px;
    }

    .control-group.search {
        flex: 2;
        min-width: 300px;
    }

    .control-group.actions {
        flex: 0 0 auto;
    }

    .control-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .search-container {
        position: relative;
    }

    .search-input {
        width: 100%;
        padding: 0.875rem 1rem 0.875rem 2.75rem;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 0.95rem;
        background-color: #fafbfc;
        color: #374151;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: #f58220;
        box-shadow: 0 0 0 3px rgba(245, 130, 32, 0.1);
        background-color: white;
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
        font-size: 1rem;
    }

    .filter-select {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 0.95rem;
        background-color: #fafbfc;
        color: #374151;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .filter-select:focus {
        outline: none;
        border-color: #f58220;
        box-shadow: 0 0 0 3px rgba(245, 130, 32, 0.1);
        background-color: white;
    }

    .btn-add-test {
        background: linear-gradient(135deg, #f58220 0%, #ff6b35 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.875rem 1.5rem;
        font-weight: 600;
        font-size: 0.95rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 12px rgba(245, 130, 32, 0.3);
    }

    .btn-add-test:hover {
        background: linear-gradient(135deg, #e06b15 0%, #ff5722 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(245, 130, 32, 0.4);
    }

    .tests-table-container {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
    }

    .table-header {
        background: #f8fafc;
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .table-header h3 {
        font-family: 'Poppins', sans-serif;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .table-header h3 i {
        color: #f58220;
    }

    .test-count {
        background: #e5e7eb;
        padding: 0.4rem 0.8rem;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 600;
        color: #6b7280;
    }

    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }

    .tests-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
    }

    .tests-table th {
        background: #f8fafc;
        padding: 1rem 0.75rem;
        text-align: left;
        font-weight: 600;
        color: #374151;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e5e7eb;
        position: sticky;
        top: 0;
        z-index: 10;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .tests-table th:nth-child(1) { width: 25%; } /* Test Name */
    .tests-table th:nth-child(2) { width: 15%; } /* Test Code */
    .tests-table th:nth-child(3) { width: 12%; } /* Department */
    .tests-table th:nth-child(4) { width: 10%; } /* Category */
    .tests-table th:nth-child(5) { width: 10%; } /* Sample Type */
    .tests-table th:nth-child(6) { width: 8%; }  /* Amount */
    .tests-table th:nth-child(7) { width: 8%; }  /* TAT */
    .tests-table th:nth-child(8) { width: 7%; }  /* Status */
    .tests-table th:nth-child(9) { width: 5%; }  /* Actions */

    .tests-table td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        vertical-align: middle;
        font-size: 0.9rem;
        color: #374151;
        word-wrap: break-word;
        overflow: hidden;
    }

    .tests-table tbody tr:hover {
        background: rgba(245, 130, 32, 0.02);
    }

    .test-name {
        font-weight: 600;
        color: #1f2937;
        line-height: 1.4;
        max-height: 2.8em;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .test-code {
        font-family: 'Monaco', 'Menlo', monospace;
        background: #f3f4f6;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.8rem;
        color: #6b7280;
        word-break: break-all;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
    }

    .test-code:hover {
        white-space: normal;
        word-wrap: break-word;
        background: #e5e7eb;
        cursor: help;
    }

    .test-amount {
        font-weight: 600;
        color: #059669;
        font-size: 1rem;
    }

    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-badge.active {
        background: #d1fae5;
        color: #065f46;
    }

    .status-badge.inactive {
        background: #fee2e2;
        color: #991b1b;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .btn-action {
        padding: 0.5rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

    .btn-edit {
        background: #dbeafe;
        color: #1d4ed8;
    }

    .btn-edit:hover {
        background: #bfdbfe;
        transform: scale(1.05);
    }

    .btn-delete {
        background: #fee2e2;
        color: #dc2626;
    }

    .btn-delete:hover {
        background: #fecaca;
        transform: scale(1.05);
    }

    .pagination-container {
        background: white;
        padding: 1.5rem;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: between;
        align-items: center;
        gap: 1rem;
    }

    .pagination-info {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .pagination {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        margin-left: auto;
    }

    .pagination a,
    .pagination span {
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .pagination a {
        color: #6b7280;
        background: #f9fafb;
        border: 1px solid #e5e7eb;
    }

    .pagination a:hover {
        background: #f3f4f6;
        color: #374151;
    }

    .pagination .current {
        background: #f58220;
        color: white;
        border: 1px solid #f58220;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 3rem;
        color: #d1d5db;
        margin-bottom: 1rem;
    }

    .empty-state h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .tests-table th:nth-child(1) { width: 30%; }
        .tests-table th:nth-child(2) { width: 20%; }
        .tests-table th:nth-child(3) { width: 15%; }
        .tests-table th:nth-child(4) { width: 10%; }
        .tests-table th:nth-child(5) { width: 10%; }
        .tests-table th:nth-child(6) { width: 8%; }
        .tests-table th:nth-child(7) { width: 7%; }
    }

    @media (max-width: 992px) {
        .test-management-container {
            padding: 1.5rem;
        }

        .controls-row {
            flex-direction: column;
            gap: 1rem;
        }

        .control-group {
            min-width: auto;
        }

        .control-group.search {
            min-width: auto;
        }

        .tests-table {
            font-size: 0.85rem;
        }

        .tests-table th,
        .tests-table td {
            padding: 0.75rem 0.5rem;
        }

        /* Hide less important columns on medium screens */
        .tests-table th:nth-child(4),
        .tests-table td:nth-child(4),
        .tests-table th:nth-child(7),
        .tests-table td:nth-child(7) {
            display: none;
        }

        .tests-table th:nth-child(1) { width: 35%; }
        .tests-table th:nth-child(2) { width: 25%; }
        .tests-table th:nth-child(3) { width: 15%; }
        .tests-table th:nth-child(5) { width: 15%; }
        .tests-table th:nth-child(6) { width: 10%; }
    }

    @media (max-width: 768px) {
        .test-management-container {
            padding: 1rem;
        }

        .test-header {
            padding: 1.5rem;
        }

        .test-header h2 {
            font-size: 1.5rem;
        }

        .test-controls {
            padding: 1rem;
        }

        .tests-table {
            font-size: 0.8rem;
        }

        .tests-table th,
        .tests-table td {
            padding: 0.5rem 0.25rem;
        }

        /* Hide more columns on mobile */
        .tests-table th:nth-child(3),
        .tests-table td:nth-child(3),
        .tests-table th:nth-child(5),
        .tests-table td:nth-child(5) {
            display: none;
        }

        .tests-table th:nth-child(1) { width: 45%; }
        .tests-table th:nth-child(2) { width: 30%; }
        .tests-table th:nth-child(6) { width: 15%; }
        .tests-table th:nth-child(8) { width: 10%; }

        .action-buttons {
            flex-direction: column;
            gap: 0.25rem;
        }

        .btn-action {
            width: 28px;
            height: 28px;
            font-size: 0.75rem;
        }

        .pagination {
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.25rem;
        }

        .pagination a,
        .pagination span {
            padding: 0.4rem 0.6rem;
            font-size: 0.8rem;
        }

        .pagination-info {
            font-size: 0.8rem;
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .pagination-container {
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }
    }

    @media (max-width: 480px) {
        .test-management-container {
            padding: 0.5rem;
        }

        .test-header {
            padding: 1rem;
        }

        .test-header h2 {
            font-size: 1.25rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .test-controls {
            padding: 0.75rem;
        }

        .control-label {
            font-size: 0.8rem;
        }

        .search-input,
        .filter-select {
            padding: 0.75rem;
            font-size: 0.9rem;
        }

        .btn-add-test {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
        }

        .tests-table-container {
            overflow-x: auto;
        }

        .tests-table {
            min-width: 600px;
        }

        .modal-dialog {
            margin: 0.5rem;
        }

        .modal-body {
            padding: 1rem;
        }

        .modal-body .row .col-md-6 {
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="test-management-container">
    <!-- Header Section -->
    <div class="test-header">
        <h2>
            <i class="fas fa-flask"></i>
            Test Management
        </h2>
        <p>Manage diagnostic tests, pricing, and categories</p>
    </div>

    <!-- Controls Section -->
    <div class="test-controls">
        <div class="controls-row">
            <div class="control-group search">
                <label class="control-label">Search Tests</label>
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="searchInput" class="search-input" 
                           placeholder="Search by test name or code..." 
                           value="{{ search }}">
                </div>
            </div>
            
            <div class="control-group">
                <label class="control-label">Department</label>
                <select id="departmentFilter" class="filter-select">
                    <option value="">All Departments</option>
                    {% for dept in departments %}
                        <option value="{{ dept }}" {% if dept == selected_department %}selected{% endif %}>
                            {{ dept }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="control-group">
                <label class="control-label">Category</label>
                <select id="categoryFilter" class="filter-select">
                    <option value="">All Categories</option>
                    {% for cat in categories %}
                        <option value="{{ cat }}" {% if cat == selected_category %}selected{% endif %}>
                            {{ cat }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="control-group">
                <label class="control-label">Status</label>
                <select id="statusFilter" class="filter-select">
                    <option value="">All Status</option>
                    <option value="active" {% if selected_status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            
            <div class="control-group actions">
                <button class="btn-add-test" onclick="openAddTestModal()">
                    <i class="fas fa-plus"></i>
                    Add New Test
                </button>
            </div>
        </div>
    </div>

    <!-- Tests Table Section -->
    <div class="tests-table-container">
        <div class="table-header">
            <h3>
                <i class="fas fa-list"></i>
                Tests List
            </h3>
            <div class="test-count">{{ total_tests }} tests</div>
        </div>

        {% if tests %}
        <div class="table-responsive">
            <table class="tests-table">
                <thead>
                    <tr>
                        <th>Test Name</th>
                        <th>Test Code</th>
                        <th>Department</th>
                        <th>Category</th>
                        <th>Sample Type</th>
                        <th>Amount</th>
                        <th>TAT</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for test in tests %}
                    <tr>
                        <td>
                            <div class="test-name" title="{{ test.TestName }}">{{ test.TestName }}</div>
                        </td>
                        <td>
                            <span class="test-code" title="{{ test.TestCode or 'No code assigned' }}">
                                {{ test.TestCode or 'N/A' }}
                            </span>
                        </td>
                        <td>
                            <span title="{{ test.DepartmentName or 'No department' }}">
                                {{ test.DepartmentName or 'N/A' }}
                            </span>
                        </td>
                        <td>
                            <span title="{{ test.TestCategory or 'No category' }}">
                                {{ test.TestCategory or 'N/A' }}
                            </span>
                        </td>
                        <td>
                            <span title="{{ test.SampleType or 'No sample type' }}">
                                {{ test.SampleType or 'N/A' }}
                            </span>
                        </td>
                        <td>
                            <span class="test-amount">₹{{ "%.2f"|format(test.TestAmount) }}</span>
                        </td>
                        <td>
                            <span title="{{ test.TargetTAT or 'No TAT specified' }}">
                                {{ test.TargetTAT or 'N/A' }}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge {{ 'active' if test.active else 'inactive' }}">
                                {{ 'Active' if test.active else 'Inactive' }}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-edit"
                                        onclick="editTest({{ test.SrNo }})"
                                        title="Edit Test">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-delete"
                                        onclick="deleteTest({{ test.SrNo }}, '{{ test.TestName|replace("'", "\\'") }}')"
                                        title="Delete Test">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if total_pages > 1 %}
        <div class="pagination-container">
            <div class="pagination-info">
                Showing {{ ((current_page - 1) * 20) + 1 }} to {{ [current_page * 20, total_tests]|min }} of {{ total_tests }} tests
            </div>
            <div class="pagination">
                {% if current_page > 1 %}
                    <a href="?page={{ current_page - 1 }}&search={{ search }}&department={{ selected_department }}&category={{ selected_category }}&status={{ selected_status }}">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                {% endif %}

                {% for page_num in range(1, total_pages + 1) %}
                    {% if page_num == current_page %}
                        <span class="current">{{ page_num }}</span>
                    {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                        <a href="?page={{ page_num }}&search={{ search }}&department={{ selected_department }}&category={{ selected_category }}&status={{ selected_status }}">
                            {{ page_num }}
                        </a>
                    {% elif page_num == 4 and current_page > 6 %}
                        <span>...</span>
                    {% elif page_num == total_pages - 3 and current_page < total_pages - 5 %}
                        <span>...</span>
                    {% endif %}
                {% endfor %}

                {% if current_page < total_pages %}
                    <a href="?page={{ current_page + 1 }}&search={{ search }}&department={{ selected_department }}&category={{ selected_category }}&status={{ selected_status }}">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                {% endif %}
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="empty-state">
            <i class="fas fa-flask"></i>
            <h3>No Tests Found</h3>
            <p>No tests match your current filters. Try adjusting your search criteria or add a new test.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add/Edit Test Modal -->
<div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModalLabel">Add New Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="testForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="testName" class="form-label">Test Name *</label>
                            <input type="text" class="form-control" id="testName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="testCode" class="form-label">Test Code *</label>
                            <input type="text" class="form-control" id="testCode" required
                                   placeholder="e.g., CBC, LFT, KFT">
                            <div class="form-text">Short code for the test (recommended: 3-10 characters)</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="testAmount" class="form-label">Test Amount *</label>
                            <input type="number" class="form-control" id="testAmount" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="outsourceAmount" class="form-label">Outsource Amount</label>
                            <input type="number" class="form-control" id="outsourceAmount" step="0.01" min="0">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="departmentName" class="form-label">Department *</label>
                            <select class="form-control" id="departmentName" required>
                                <option value="">Select Department</option>
                                {% for dept in departments %}
                                    <option value="{{ dept }}">{{ dept }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="testCategory" class="form-label">Category</label>
                            <select class="form-control" id="testCategory">
                                <option value="">Select Category</option>
                                {% for cat in categories %}
                                    <option value="{{ cat }}">{{ cat }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sampleType" class="form-label">Sample Type *</label>
                            <input type="text" class="form-control" id="sampleType" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="targetTAT" class="form-label">Target TAT</label>
                            <input type="text" class="form-control" id="targetTAT" placeholder="e.g., 24 hours">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="outsourceCenter" class="form-label">Outsource Center</label>
                            <input type="text" class="form-control" id="outsourceCenter">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="accreditation" class="form-label">Accreditation</label>
                            <input type="text" class="form-control" id="accreditation">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="integrationCode" class="form-label">Integration Code</label>
                            <input type="text" class="form-control" id="integrationCode">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="capTest" class="form-label">CAP Test</label>
                            <input type="text" class="form-control" id="capTest" maxlength="5">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="shortText" class="form-label">Short Description</label>
                            <input type="text" class="form-control" id="shortText">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="activeStatus" class="form-label">Status</label>
                            <select class="form-control" id="activeStatus">
                                <option value="true">Active</option>
                                <option value="false">Inactive</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveTest()">Save Test</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the test "<span id="deleteTestName"></span>"?</p>
                <p class="text-muted">This action cannot be undone. If the test has existing bookings, it will be deactivated instead of deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentTestId = null;
    let isEditMode = false;

    // Search and filter functionality
    document.getElementById('searchInput').addEventListener('input', debounce(applyFilters, 300));
    document.getElementById('departmentFilter').addEventListener('change', applyFilters);
    document.getElementById('categoryFilter').addEventListener('change', applyFilters);
    document.getElementById('statusFilter').addEventListener('change', applyFilters);

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function applyFilters() {
        const search = document.getElementById('searchInput').value;
        const department = document.getElementById('departmentFilter').value;
        const category = document.getElementById('categoryFilter').value;
        const status = document.getElementById('statusFilter').value;

        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (department) params.append('department', department);
        if (category) params.append('category', category);
        if (status) params.append('status', status);

        window.location.href = '/admin/tests?' + params.toString();
    }

    function openAddTestModal() {
        isEditMode = false;
        currentTestId = null;
        document.getElementById('testModalLabel').textContent = 'Add New Test';
        document.getElementById('testForm').reset();

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('testModal'));
        modal.show();
    }

    function editTest(testId) {
        isEditMode = true;
        currentTestId = testId;
        document.getElementById('testModalLabel').textContent = 'Edit Test';

        // Fetch test details
        fetch(`/admin/api/tests/${testId}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showAlert('Error loading test details: ' + data.error, 'danger');
                    return;
                }

                // Populate form
                document.getElementById('testName').value = data.TestName || '';
                document.getElementById('testCode').value = data.TestCode || '';
                document.getElementById('testAmount').value = data.TestAmount || '';
                document.getElementById('outsourceAmount').value = data.OutsourceAmount || '';
                document.getElementById('departmentName').value = data.DepartmentName || '';
                document.getElementById('testCategory').value = data.TestCategory || '';
                document.getElementById('sampleType').value = data.SampleType || '';
                document.getElementById('targetTAT').value = data.TargetTAT || '';
                document.getElementById('outsourceCenter').value = data.OutsourceCenter || '';
                document.getElementById('accreditation').value = data.Accreditation || '';
                document.getElementById('integrationCode').value = data.IntegrationCode || '';
                document.getElementById('capTest').value = data.CAPTest || '';
                document.getElementById('shortText').value = data.ShortText || '';
                document.getElementById('activeStatus').value = data.active ? 'true' : 'false';

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('testModal'));
                modal.show();
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error loading test details', 'danger');
            });
    }

    function saveTest() {
        const form = document.getElementById('testForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const formData = {
            TestName: document.getElementById('testName').value,
            TestCode: document.getElementById('testCode').value,
            TestAmount: parseFloat(document.getElementById('testAmount').value),
            OutsourceAmount: parseFloat(document.getElementById('outsourceAmount').value) || 0,
            DepartmentName: document.getElementById('departmentName').value,
            TestCategory: document.getElementById('testCategory').value,
            SampleType: document.getElementById('sampleType').value,
            TargetTAT: document.getElementById('targetTAT').value,
            OutsourceCenter: document.getElementById('outsourceCenter').value,
            Accreditation: document.getElementById('accreditation').value,
            IntegrationCode: document.getElementById('integrationCode').value,
            CAPTest: document.getElementById('capTest').value,
            ShortText: document.getElementById('shortText').value,
            active: document.getElementById('activeStatus').value === 'true'
        };

        const url = isEditMode ? `/admin/tests/update/${currentTestId}` : '/admin/tests/create';
        const method = isEditMode ? 'PUT' : 'POST';

        // Get CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                         document.querySelector('input[name=csrf_token]')?.value;

        const headers = {
            'Content-Type': 'application/json'
        };

        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken;
        }

        fetch(url, {
            method: method,
            headers: headers,
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert(data.error, 'danger');
                return;
            }

            showAlert(data.message, 'success');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('testModal'));
            modal.hide();

            // Reload page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error saving test', 'danger');
        });
    }

    function deleteTest(testId, testName) {
        currentTestId = testId;
        document.getElementById('deleteTestName').textContent = testName;

        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }

    function confirmDelete() {
        // Get CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                         document.querySelector('input[name=csrf_token]')?.value;

        const headers = {};
        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken;
        }

        fetch(`/admin/tests/delete/${currentTestId}`, {
            method: 'DELETE',
            headers: headers
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert(data.error, 'danger');
                return;
            }

            showAlert(data.message, 'success');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();

            // Reload page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error deleting test', 'danger');
        });
    }

    function showAlert(message, type) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.style.position = 'fixed';
        alertDiv.style.top = '20px';
        alertDiv.style.right = '20px';
        alertDiv.style.zIndex = '9999';
        alertDiv.style.minWidth = '300px';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Add to page
        document.body.appendChild(alertDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
</script>
{% endblock %}
