#!/usr/bin/env python3
"""
Safe application startup script with error handling
"""

import os
import sys
import time
from dotenv import load_dotenv

def check_environment():
    """Check if environment is properly configured"""
    print("🔧 Checking Environment Configuration...")
    
    # Load environment variables
    load_dotenv()
    
    required_vars = [
        'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
        'MAIL_USERNAME', 'MAIL_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("💡 Please check your .env file")
        return False
    
    print("✅ Environment configuration looks good")
    return True

def clear_session_files():
    """Clear any existing session files"""
    session_dir = './flask_session'
    if os.path.exists(session_dir):
        try:
            import shutil
            shutil.rmtree(session_dir)
            print("🗑️  Cleared old session files")
        except Exception as e:
            print(f"⚠️  Could not clear session files: {e}")

def start_application():
    """Start the Flask application with error handling"""
    print("🚀 Starting CVBioLabs Application...")
    print("-" * 40)
    
    try:
        # Clear session files first
        clear_session_files()
        
        # Import and start the app
        from app import app
        
        print("✅ Application imported successfully")
        print("🌐 Starting server on http://localhost:7000")
        print("🏥 Health check will be available at: http://localhost:7000/health")
        print("📊 Rate limits have been reset")
        print("\n💡 Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start the Flask development server
        app.run(
            host='0.0.0.0',
            port=7000,
            debug=os.getenv('FLASK_ENV') == 'development',
            use_reloader=False  # Disable reloader to prevent double startup
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("   1. Check your .env file configuration")
        print("   2. Ensure MySQL is running")
        print("   3. Verify all dependencies are installed")
        print("   4. Run: python test_fixes.py")
        sys.exit(1)

def main():
    """Main startup function"""
    print("🏥 CVBioLabs Application Starter")
    print("=" * 40)
    
    # Check environment first
    if not check_environment():
        print("\n❌ Environment check failed")
        print("💡 Please fix the configuration issues above")
        sys.exit(1)
    
    # Start the application
    start_application()

if __name__ == '__main__':
    main()
