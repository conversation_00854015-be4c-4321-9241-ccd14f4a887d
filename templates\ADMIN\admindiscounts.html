{% extends "ADMIN/adminbase.html" %}

{% block title %}Discounts & Promo Codes - Admin Dashboard{% endblock %}

{% block page_title %}Discounts & Promo Codes{% endblock %}

{% block extra_css %}
<meta name="csrf-token" content="{{ csrf_token }}">
<style>
    .discount-card {
        transition: transform 0.3s;
    }

    .discount-card:hover {
        transform: translateY(-5px);
    }

    .required::after {
        content: " *";
        color: var(--danger);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDiscountModal">
            <i class="fas fa-plus me-2"></i>Create New Discount
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card discount-card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Active Discounts</h5>
                    <h2 class="mb-0">{{ active_discounts }}</h2>
                    <small class="text-white-50">Currently Running</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card discount-card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Uses</h5>
                    <h2 class="mb-0">{{ total_uses }}</h2>
                    <small class="text-white-50">This Month</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card discount-card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Savings</h5>
                    <h2 class="mb-0">₹{{ total_savings|round(2) }}</h2>
                    <small class="text-white-50">This Month</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card discount-card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">Expiring Soon</h5>
                    <h2 class="mb-0">{{ expiring_soon }}</h2>
                    <small class="text-white-50">Next 7 Days</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select class="form-select" id="statusFilter">
                        <option value="all">All Status</option>
                        <option value="Active">Active</option>
                        <option value="Expired">Expired</option>
                        <option value="Used">Used</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control" placeholder="Search discounts..." id="searchInput">
                </div>
                <div class="col-md-2">
                    <label class="form-label"> </label>
                    <button class="btn btn-primary w-100" onclick="filterDiscounts()">
                        <i class="fas fa-filter me-2"></i>Apply Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Discounts Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Code</th>
                            <th>Discount Amount</th>
                            <th>Valid Until</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="discountsTableBody">
                        {% if discounts %}
                            {% for discount in discounts %}
                            <tr>
                                <td>{{ discount.id }}</td>
                                <td>{{ discount.code }}</td>
                                <td>₹{{ discount.discount_amount|round(2) }}</td>
                                <td>{{ discount.valid_until }}</td>
                                <td><span class="badge bg-{{ discount.status|status_badge }}">{{ discount.status }}</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="editDiscount('{{ discount.code }}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteDiscount('{{ discount.code }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center">No discounts found</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Discount Modal -->
<div class="modal fade" id="addDiscountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Discount</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="discountForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label required">Discount Code</label>
                            <input type="text" class="form-control" name="code" required id="discountCode">
                            <small class="text-muted">Must be unique, no spaces allowed</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Discount Amount</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="discount_amount" required id="discountAmount">
                                <span class="input-group-text">₹</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Valid Until</label>
                            <input type="date" class="form-control" name="valid_until" required id="validUntil">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Status</label>
                            <select class="form-select" name="status" required id="discountStatus">
                                <option value="Active">Active</option>
                                <option value="Expired">Expired</option>
                                <option value="Used">Used</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveDiscount()">Save Discount</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this discount code? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize discounts array
    let discounts = [];
    try {
        const discountsData = {{ discounts|tojson|safe }};
        if (Array.isArray(discountsData)) {
            discounts = discountsData.map(function(d) {
                return {
                    id: d && d.id !== undefined && d.id !== null ? d.id : '',
                    code: d && d.code !== undefined && d.code !== null ? d.code : '',
                    discount_amount: d && d.discount_amount !== undefined && d.discount_amount !== null ? d.discount_amount : 0,
                    valid_until: d && d.valid_until !== undefined && d.valid_until !== null ? d.valid_until : '',
                    status: d && d.status !== undefined && d.status !== null ? d.status : ''
                };
            });
        }
    } catch (e) {
        console.error('Error initializing discounts:', e);
        showAlert('Error loading discount data', 'danger');
    }

    // CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Show alert message
    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3`;
        alertDiv.style.zIndex = '9999';
        alertDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                <div>${message}</div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 150);
        }, 5000);
    }

    function filterDiscounts() {
        const statusFilter = document.getElementById('statusFilter').value;
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();

        const filteredDiscounts = discounts.filter(discount => {
            const matchesStatus = statusFilter === 'all' || discount.status === statusFilter;
            const matchesSearch = discount.code.toLowerCase().includes(searchTerm);
            return matchesStatus && matchesSearch;
        });

        updateDiscountTable(filteredDiscounts);
    }

    function updateDiscountTable(filteredDiscounts) {
        const tbody = document.getElementById('discountsTableBody');
        tbody.innerHTML = '';

        if (filteredDiscounts.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">No discounts found</td></tr>';
            return;
        }

        filteredDiscounts.forEach(discount => {
            const row = `
                <tr>
                    <td>${discount.id}</td>
                    <td>${discount.code}</td>
                    <td>₹${parseFloat(discount.discount_amount).toFixed(2)}</td>
                    <td>${discount.valid_until}</td>
                    <td><span class="badge bg-${discount.status === 'Active' ? 'success' : (discount.status === 'Expired' ? 'secondary' : 'warning')}">${discount.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editDiscount('${discount.code}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteDiscount('${discount.code}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    }

    function editDiscount(code) {
        const discount = discounts.find(d => d.code === code);
        if (!discount) return;

        // Store the discount ID for update
        document.getElementById('discountForm').dataset.id = discount.id;
        document.getElementById('discountCode').value = discount.code;
        document.getElementById('discountAmount').value = discount.discount_amount;
        document.getElementById('validUntil').value = discount.valid_until;
        document.getElementById('discountStatus').value = discount.status;

        const modal = new bootstrap.Modal(document.getElementById('addDiscountModal'));
        modal.show();
    }

    let discountToDelete = null;
    function deleteDiscount(code) {
        discountToDelete = code;
        const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        document.getElementById('confirmDeleteBtn').onclick = confirmDelete;
        modal.show();
    }

    function confirmDelete() {
        if (!discountToDelete) return;

        fetch(`/admin/discounts/delete/${discountToDelete}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-Token': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Discount deleted successfully', 'success');
                // Remove the deleted discount from the array
                discounts = discounts.filter(d => d.code !== discountToDelete);
                // Update the table
                updateDiscountTable(discounts);
                // Update the counts
                updateDiscountCounts();
            } else {
                throw new Error(data.error || 'Failed to delete discount');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert(error.message, 'danger');
        })
        .finally(() => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            modal.hide();
            discountToDelete = null;
        });
    }

function saveDiscount() {
    const form = document.getElementById('discountForm');
    const formData = new FormData(form);
    const discountId = form.dataset.id;

    // Convert FormData to JSON object
    const data = {
        code: formData.get('code'),
        discount_amount: formData.get('discount_amount'),
        valid_until: formData.get('valid_until'),
        status: formData.get('status'),
        csrf_token: formData.get('csrf_token')
    };

    // Add ID if editing existing discount
    if (discountId) {
        data.id = discountId;
    }

    fetch('/admin/discounts/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');

            // Defensive: handle both dict and tuple/array from backend
            let d = data.discount;
            if (Array.isArray(d)) {
                d = {
                    id: d[0] || '',
                    code: d[1] || '',
                    discount_amount: parseFloat(d[2]) || 0,
                    valid_until: d[3] || '',
                    status: d[4] || ''
                };
            }

            const formattedDiscount = {
                id: d.id || '',
                code: d.code || '',
                discount_amount: parseFloat(d.discount_amount) || 0,
                valid_until: d.valid_until || '',
                status: d.status || ''
            };

            if (discountId) {
                // Update existing discount
                const index = discounts.findIndex(dis => dis.id == discountId);
                if (index !== -1) {
                    discounts[index] = formattedDiscount;
                }
            } else {
                // Add new discount
                discounts.unshift(formattedDiscount);
            }

            // Update the table and counts
            updateDiscountTable(discounts);
            updateDiscountCounts();

            // Close the modal
            bootstrap.Modal.getInstance(document.getElementById('addDiscountModal')).hide();
            // Reset the form
            form.reset();
            delete form.dataset.id;
        } else {
            throw new Error(data.error || 'Failed to save discount');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert(error.message, 'danger');
    });
}

    function updateDiscountCounts() {
        // Update the counts in the cards
        const activeCount = discounts.filter(d => d.status === 'Active').length;
        const expiringCount = discounts.filter(d => {
            const expiryDate = new Date(d.valid_until);
            const today = new Date();
            const sevenDaysFromNow = new Date();
            sevenDaysFromNow.setDate(today.getDate() + 7);
            return d.status === 'Active' && expiryDate >= today && expiryDate <= sevenDaysFromNow;
        }).length;

        // Update the cards
        document.querySelector('.bg-primary h2').textContent = activeCount;
        document.querySelector('.bg-warning h2').textContent = expiringCount;
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Initial table update
        updateDiscountTable(discounts);
        updateDiscountCounts();

        // Add event listeners for filters
        document.getElementById('searchInput').addEventListener('input', filterDiscounts);
        document.getElementById('statusFilter').addEventListener('change', filterDiscounts);

        // Reset form when modal is closed
        const addDiscountModal = document.getElementById('addDiscountModal');
        addDiscountModal.addEventListener('hidden.bs.modal', function() {
            const form = document.getElementById('discountForm');
            form.reset();
            delete form.dataset.id;
        });
    });
</script>
{% endblock %}