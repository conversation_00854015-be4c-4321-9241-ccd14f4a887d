@echo off
echo ========================================
echo CVBioLabs Flask App Restart Script
echo ========================================
echo.

echo 🔄 Stopping any running Flask processes...
taskkill /f /im python.exe /fi "WINDOWTITLE eq *app.py*" 2>nul
timeout /t 2 /nobreak >nul

echo 🗑️  Clearing session files...
if exist "flask_session" (
    rmdir /s /q "flask_session" 2>nul
    echo ✅ Session files cleared
) else (
    echo ℹ️  No session files to clear
)

echo.
echo 🚀 Starting Flask application...
echo ⏳ Please wait...
echo.

start "CVBioLabs Flask App" python app.py

timeout /t 3 /nobreak >nul

echo ✅ Flask application started!
echo 🔗 Application URL: http://localhost:7000
echo 🏥 Health check: http://localhost:7000/health
echo.
echo 📊 Rate limits have been reset!
echo 💡 You can now try your requests again.
echo.
pause
