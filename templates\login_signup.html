<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Authentication - CVBioLabs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    {% if recaptcha_site_key and recaptcha_site_key != '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI' %}
    <script src="https://www.google.com/recaptcha/api.js?render={{ recaptcha_site_key }}" async defer></script>
    <script>
        // Initialize reCAPTCHA
        window.recaptchaReady = false;
        window.addEventListener('load', function() {
            if (typeof grecaptcha !== 'undefined') {
                grecaptcha.ready(function() {
                    window.recaptchaReady = true;
                    console.log('reCAPTCHA is ready');
                });
            } else {
                console.warn('reCAPTCHA failed to load, using development mode');
                window.recaptchaReady = true;
                window.grecaptcha = {
                    ready: function(callback) { callback(); },
                    execute: function(siteKey, options) {
                        return Promise.resolve('development-token');
                    }
                };
            }
        });
    </script>
    {% else %}
    <script>
        // Development mode - Mock reCAPTCHA
        window.recaptchaReady = true;
        window.grecaptcha = {
            ready: function(callback) {
                setTimeout(callback, 100);
            },
            execute: function(siteKey, options) {
                console.log('Using development reCAPTCHA token');
                return Promise.resolve('development-token-' + Date.now());
            }
        };
        console.log('reCAPTCHA disabled for development');
    </script>
    {% endif %}
    <style>
        :root {
            --dark-blue: #002f6c;
            --orange: #f47c20;
            --light-blue: #e6f7ff;
            --spacing-base: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--light-blue) 0%, white 100%);
            background-image: 
                linear-gradient(135deg, rgba(230, 247, 255, 0.9) 0%, rgba(255, 255, 255, 0.9) 100%),
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23002f6c' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            display: flex;
            justify-content: center;
            align-items: center;
            padding: calc(var(--spacing-base) * 2);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 0 100px rgba(0, 47, 108, 0.1);
            width: 100%;
            max-width: 550px;
            padding: calc(var(--spacing-base) * 3);
            margin: var(--spacing-base);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h2 {
            color: var(--dark-blue);
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
        }

        .toggle-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            background: var(--light-blue);
            padding: 5px;
            border-radius: 25px;
        }

        .toggle-btn {
            padding: 10px 25px;
            border: none;
            background: none;
            color: var(--dark-blue);
            font-weight: 500;
            cursor: pointer;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .toggle-btn.active {
            background: var(--orange);
            color: white;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            color: var(--dark-blue);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--orange);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 1rem;
        }

        .checkbox-group label {
            color: var(--dark-blue);
            font-size: 0.9rem;
        }

        .forgot-password {
            display: block;
            text-align: right;
            color: var(--orange);
            text-decoration: none;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: var(--orange);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            background: #e06b15;
            transform: translateY(-2px);
        }

        #passwordStrength {
            margin-top: 5px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Flash Messages */
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .flash-message {
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            font-size: 14px;
            animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            box-shadow: 0 8px 24px -4px rgba(0, 0, 0, 0.2);
        }

        .flash-message.success {
            background: linear-gradient(to right, #22c55e, #4ade80);
        }

        .flash-message.error {
            background: linear-gradient(to right, #ef4444, #f87171);
        }

        /* Loading Overlay Styles */
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            z-index: 9999;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--light-blue);
            border-top: 4px solid var(--orange);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        .loading-text {
            color: var(--dark-blue);
            font-size: 1.1rem;
            font-weight: 500;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            body {
                padding: 15px;
            }
            
            .container {
                padding: 1.5rem;
                max-width: 100%;
                margin: 10px;
            }

            .header h2 {
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }

            .toggle-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-group input {
                padding: 10px;
            }

            .name-fields {
                flex-direction: column;
                gap: 1rem;
            }

            .name-field {
                width: 100%;
            }
        }

        .name-fields {
            display: flex;
            gap: calc(var(--spacing-base) * 1.5);
            margin-bottom: calc(var(--spacing-base) * 1.5);
        }

        .name-fields .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing your request...</div>
    </div>

    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="container">
        <div class="header">
            <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBiolabs Logo" style="max-width:50px; height:auto; display:block; margin:0 auto 1rem auto;">
            <h2>Welcome to CVBioLabs</h2>
            <div class="toggle-buttons">
                <button class="toggle-btn active" data-form="login">Login</button>
                <button class="toggle-btn" data-form="register">Register</button>
            </div>
        </div>

        <div class="form-container">
            <!-- Login Form -->
            <form id="loginForm" method="POST" action="{{ url_for('login', next=request.args.get('next')) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="recaptcha_token" id="loginRecaptchaToken">
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" id="loginEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label>Password</label>
                    <div style="position:relative;">
                        <input type="password" id="loginPassword" name="password" required style="padding-right:40px;">
                        <span class="toggle-password" data-target="loginPassword" style="position:absolute;top:50%;right:12px;transform:translateY(-50%);cursor:pointer;">
                            <i class="fa fa-eye-slash"></i>
                        </span>
                    </div>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="rememberMe">
                    <label for="rememberMe">Remember me</label>
                </div>
                <a href="{{ url_for('forgot_password') }}" class="forgot-password">Forgot password?</a>
                <button type="submit" class="submit-btn">Login</button>
            </form>

            <!-- Register Form -->
            <form id="registerForm" style="display: none;" method="POST" action="{{ url_for('signup') }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="recaptcha_token" id="registerRecaptchaToken">
                <div class="name-fields">
                    <div class="form-group">
                        <label>First Name</label>
                        <input type="text" id="signupFirstName" name="firstname" required>
                    </div>
                    <div class="form-group">
                        <label>Last Name</label>
                        <input type="text" id="signupLastName" name="lastname" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" id="signupEmail" name="email" required>
                </div>
                 <div class="form-group">
                    <label>Phone Number</label>
                    <input type="tel" id="phone" name="phone" 
                           required 
                           pattern="^[6-9]\d{9}$" 
                           title="Phone number must start with 6-9 and be exactly 10 digits long">
                    <small class="phone-validation-error" style="color: red; display: none;">Please enter a valid 10-digit phone number starting with 6-9</small>
                </div>
                <div class="form-group">
                    <label>Password</label>
                    <div style="position:relative;">
                        <input type="password" id="signupPassword" name="password" required onkeyup="checkPasswordStrength()" style="padding-right:40px;">
                        <span class="toggle-password" data-target="signupPassword" style="position:absolute;top:50%;right:12px;transform:translateY(-50%);cursor:pointer;">
                            <i class="fa fa-eye-slash"></i>
                        </span>
                    </div>
                    <div id="passwordStrength"></div>
                </div>
                <div class="form-group">
                    <label>Confirm Password</label>
                    <div style="position:relative;">
                        <input type="password" id="confirmPassword" name="confirmPassword" required style="padding-right:40px;">
                        <span class="toggle-password" data-target="confirmPassword" style="position:absolute;top:50%;right:12px;transform:translateY(-50%);cursor:pointer;">
                            <i class="fa fa-eye-slash"></i>
                        </span>
                    </div>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="terms" required>
                    <label for="terms">I accept the Terms and Conditions</label>
                </div>
                <button type="submit" class="submit-btn">Register</button>
            </form>
        </div>
    </div>

    <script>

        console.log = function() {};
        // Toggle between login and register forms
        document.querySelectorAll('.toggle-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault(); // Prevent any default button behavior
                document.querySelectorAll('.toggle-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const form = this.getAttribute('data-form');
                document.getElementById('loginForm').style.display = form === 'login' ? 'block' : 'none';
                document.getElementById('registerForm').style.display = form === 'register' ? 'block' : 'none';
            });
        });

        // Password strength checker
        function checkPasswordStrength() {
            const password = document.getElementById('signupPassword').value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            const hasUpperCase = /[A-Z]/.test(password);
            const hasLowerCase = /[a-z]/.test(password);
            const hasNumbers = /\d/.test(password);
            const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
            const isLongEnough = password.length >= 8;

            let strength = 0;
            strength += hasUpperCase ? 1 : 0;
            strength += hasLowerCase ? 1 : 0;
            strength += hasNumbers ? 1 : 0;
            strength += hasSpecialChar ? 1 : 0;
            strength += isLongEnough ? 1 : 0;

            let strengthText = '';
            let color = '';

            if (password.length === 0) {
                strengthText = '';
                color = '';
            } else if (strength < 2) {
                strengthText = 'Weak';
                color = '#d93025';
            } else if (strength < 4) {
                strengthText = 'Medium';
                color = '#f4b400';
            } else {
                strengthText = 'Strong';
                color = '#188038';
            }

            strengthDiv.textContent = strengthText ? `Password Strength: ${strengthText}` : '';
            strengthDiv.style.color = color;
        }

        // Clear password strength on page load
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('passwordStrength').textContent = '';
        });

        async function handleFormSubmission(event, endpoint) {
            event.preventDefault();
            const form = event.target;

            // Show loading overlay
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }

            try {
                // Wait for reCAPTCHA to be ready
                let retries = 0;
                while (!window.recaptchaReady && retries < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    retries++;
                }

                if (!window.recaptchaReady) {
                    console.warn('reCAPTCHA not ready, using fallback');
                }

                // Get reCAPTCHA token with error handling
                let token;
                try {
                    {% if recaptcha_site_key and recaptcha_site_key != '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI' %}
                    if (typeof grecaptcha !== 'undefined' && grecaptcha.execute) {
                        token = await grecaptcha.execute('{{ recaptcha_site_key }}', {action: 'submit'});
                    } else {
                        token = 'development-fallback-' + Date.now();
                    }
                    {% else %}
                    token = await grecaptcha.execute('development', {action: 'submit'});
                    {% endif %}
                } catch (recaptchaError) {
                    console.warn('reCAPTCHA execution failed:', recaptchaError);
                    token = 'development-fallback-' + Date.now();
                }

                console.log('reCAPTCHA token received:', token);

                // Create form data
                const formData = new FormData(form);

                // Add reCAPTCHA token to form data
                formData.set('recaptcha_token', token);

                // Add next parameter if it exists in URL
                const urlParams = new URLSearchParams(window.location.search);
                const nextParam = urlParams.get('next');
                if (nextParam) {
                    formData.append('next', nextParam);
                }

                // Get CSRF token from the form
                const csrfToken = form.querySelector('input[name="csrf_token"]').value;

                // Send the form data
                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': csrfToken
                    }
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);

                if (data.status === 'success') {
                    showFlashMessage(data.message, 'success');
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    }
                } else {
                    showFlashMessage(data.message, 'error');
                }
            } catch (error) {
                console.error('Error details:', error);
                showFlashMessage(error.message || 'An error occurred during submission', 'error');
            } finally {
                // Hide loading overlay
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Log environment info for debugging
            console.log('Page loaded, checking reCAPTCHA status...');
            console.log('reCAPTCHA ready:', window.recaptchaReady);
            console.log('grecaptcha object:', typeof window.grecaptcha);

            // Check for browser extension conflicts
            if (window.chrome && window.chrome.runtime) {
                console.log('Chrome extension environment detected');
            }

            // Add form submission handlers
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');

            // Log form elements for debugging
            console.log('Login form:', loginForm);
            console.log('Register form:', registerForm);

            if (loginForm) {
                loginForm.addEventListener('submit', function(event) {
                    console.log('Login form submitted');
                    handleFormSubmission(event, loginForm.action);
                });
            }

            if (registerForm) {
                registerForm.addEventListener('submit', function(event) {
                    console.log('Register form submitted');
                    handleFormSubmission(event, registerForm.action);
                });
            }

            // Auto-hide flash messages
            setTimeout(function() {
                const flashMessages = document.querySelectorAll('.flash-message');
                flashMessages.forEach(msg => {
                    msg.style.opacity = '0';
                    setTimeout(() => msg.remove(), 300);
                });
            }, 5000);
        });

        // Password show/hide toggle
        document.querySelectorAll('.toggle-password').forEach(function(toggle) {
            toggle.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const input = document.getElementById(targetId);
                const icon = this.querySelector('i');
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                } else {
                    input.type = 'password';
                    icon.classList.add('fa-eye-slash');
                    icon.classList.remove('fa-eye');
                }
            });
        });

        function showFlashMessage(message, type) {
            const flashMessages = document.querySelector('.flash-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `flash-message ${type}`;
            messageDiv.textContent = message;
            flashMessages.appendChild(messageDiv);

            // Auto-hide flash message after 5 seconds
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                setTimeout(() => messageDiv.remove(), 300);
            }, 5000);
        }
    </script>
</body>
</html>