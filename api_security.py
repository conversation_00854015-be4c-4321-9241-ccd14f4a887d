"""
API Security utilities for CVBioLabs application
Provides JWT authentication, rate limiting, and API validation
"""

import jwt
import secrets
import time
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app, session
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import logging

logger = logging.getLogger(__name__)

class APIAuth:
    """API Authentication utilities"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize API authentication with Flask app"""
        # Set JWT secret key
        app.config.setdefault('JWT_SECRET_KEY', secrets.token_urlsafe(32))
        app.config.setdefault('JWT_ACCESS_TOKEN_EXPIRES', timedelta(hours=1))
        app.config.setdefault('JWT_REFRESH_TOKEN_EXPIRES', timedelta(days=30))
    
    @staticmethod
    def generate_tokens(user_id, user_role):
        """Generate JWT access and refresh tokens"""
        try:
            secret_key = current_app.config['JWT_SECRET_KEY']
            
            # Access token payload
            access_payload = {
                'user_id': user_id,
                'user_role': user_role,
                'type': 'access',
                'iat': datetime.utcnow(),
                'exp': datetime.utcnow() + current_app.config['JWT_ACCESS_TOKEN_EXPIRES']
            }
            
            # Refresh token payload
            refresh_payload = {
                'user_id': user_id,
                'type': 'refresh',
                'iat': datetime.utcnow(),
                'exp': datetime.utcnow() + current_app.config['JWT_REFRESH_TOKEN_EXPIRES']
            }
            
            access_token = jwt.encode(access_payload, secret_key, algorithm='HS256')
            refresh_token = jwt.encode(refresh_payload, secret_key, algorithm='HS256')
            
            return access_token, refresh_token
        except Exception as e:
            logger.error(f"Token generation failed: {e}")
            return None, None
    
    @staticmethod
    def verify_token(token, token_type='access'):
        """Verify JWT token"""
        try:
            secret_key = current_app.config['JWT_SECRET_KEY']
            payload = jwt.decode(token, secret_key, algorithms=['HS256'])
            
            # Check token type
            if payload.get('type') != token_type:
                return None
            
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
    
    @staticmethod
    def refresh_access_token(refresh_token):
        """Generate new access token from refresh token"""
        payload = APIAuth.verify_token(refresh_token, 'refresh')
        if not payload:
            return None
        
        user_id = payload.get('user_id')
        # Get user role from database or session
        user_role = session.get('user_role', 'patient')
        
        access_token, _ = APIAuth.generate_tokens(user_id, user_role)
        return access_token

def jwt_required(f):
    """Decorator to require JWT authentication for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        
        # Check for token in Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
        
        # Check for token in request args (for GET requests)
        if not token:
            token = request.args.get('token')
        
        # Check for token in form data (for POST requests)
        if not token:
            token = request.form.get('token')
        
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
        
        payload = APIAuth.verify_token(token)
        if not payload:
            return jsonify({'error': 'Token is invalid or expired'}), 401
        
        # Add user info to request context
        request.current_user_id = payload.get('user_id')
        request.current_user_role = payload.get('user_role')
        
        return f(*args, **kwargs)
    
    return decorated_function

def role_required(allowed_roles):
    """Decorator to require specific roles for API endpoints"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_role = getattr(request, 'current_user_role', None)
            
            if not user_role:
                return jsonify({'error': 'User role not found'}), 403
            
            if user_role not in allowed_roles:
                return jsonify({'error': 'Insufficient permissions'}), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

class APIRateLimiter:
    """Enhanced rate limiting for API endpoints"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize rate limiter with Flask app"""
        self.limiter = Limiter(
            get_remote_address,
            app=app,
            default_limits=["100 per hour", "20 per minute"],
            storage_uri="memory://"  # Use Redis in production
        )
    
    def limit_by_user(self, rate):
        """Rate limit by authenticated user"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                user_id = getattr(request, 'current_user_id', None)
                if user_id:
                    # Use user-specific rate limiting
                    key = f"user:{user_id}"
                else:
                    # Fall back to IP-based rate limiting
                    key = get_remote_address()
                
                # Apply rate limiting logic here
                # This is a simplified version - use Redis for production
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator

def validate_json_request(required_fields=None):
    """Decorator to validate JSON request data"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({'error': 'Request must be JSON'}), 400
            
            data = request.get_json()
            if not data:
                return jsonify({'error': 'Invalid JSON data'}), 400
            
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    return jsonify({
                        'error': f'Missing required fields: {", ".join(missing_fields)}'
                    }), 400
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def api_error_handler(f):
    """Decorator to handle API errors consistently"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"API validation error: {e}")
            return jsonify({'error': 'Invalid input data'}), 400
        except PermissionError as e:
            logger.warning(f"API permission error: {e}")
            return jsonify({'error': 'Access denied'}), 403
        except Exception as e:
            logger.error(f"API error: {e}", exc_info=True)
            return jsonify({'error': 'Internal server error'}), 500
    
    return decorated_function

# Initialize global instances
api_auth = APIAuth()
api_rate_limiter = APIRateLimiter()
