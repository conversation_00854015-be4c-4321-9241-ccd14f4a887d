# Reset Password Troubleshooting Guide

## Common Issues and Solutions

### 1. 429 (Too Many Requests) Error

**Problem**: Rate limiting is blocking password reset attempts.

**Solutions**:
- Wait for the rate limit to reset (now 10 attempts per hour instead of 5)
- Check if multiple users are trying to reset passwords from the same IP
- Consider implementing user-specific rate limiting instead of IP-based

**Rate Limits Applied**:
- Staff reset: 10 per hour
- Doctor reset: 10 per hour  
- Agent reset: 10 per hour
- Main reset: 10 per minute

### 2. 400 (Bad Request) Error

**Problem**: Missing or invalid form data.

**Common Causes**:
- Missing required fields (agent_id, phone, new_password for agents)
- Empty or whitespace-only inputs
- Password too short (less than 6 characters)
- Invalid CSRF token

**Solutions**:
- Ensure all required fields are filled
- Check password meets minimum requirements
- Verify CSRF token is properly included

### 3. Session Issues

**Problem**: "Please initiate password reset first" error.

**Solutions**:
- Ensure user goes through forgot_password flow first
- Check session is maintained between requests
- Verify session storage is working properly

### 4. OTP Issues

**Problem**: "Invalid or expired OTP" error.

**Solutions**:
- Check OTP was sent successfully to email
- Verify <PERSON><PERSON> is entered correctly (no extra spaces)
- Ensure OTP is used within the valid time window (5 minutes)

## Testing Steps

### For Regular User Password Reset:
1. Go to `/forgot_password`
2. Enter email address
3. Check email for OTP
4. Go to `/reset_password`
5. Enter OTP and new password
6. Submit form

### For Admin Password Reset:
1. Access admin panel
2. Go to password reset section
3. Fill required fields:
   - **Staff**: staff_id, email, new_password
   - **Doctor**: doctor_id, license_number, new_password
   - **Agent**: agent_id, phone, new_password
4. Submit form

### Debug Information:
- Visit `/debug/session` (development only) to check session state
- Check browser console for JavaScript errors
- Check server logs for detailed error messages

## Validation Rules

### Password Requirements:
- Minimum 6 characters
- Must not be empty or whitespace only

### Field Requirements:
- **Staff Reset**: staff_id, email, new_password (all required)
- **Doctor Reset**: doctor_id, license_number, new_password (all required)
- **Agent Reset**: agent_id, phone, new_password (all required)
- **User Reset**: otp, new_password, confirm_password (all required)

## Error Messages

### Enhanced Error Messages:
- "Agent ID is required" (instead of generic "All fields required")
- "Phone number is required"
- "New password is required"
- "Password must be at least 6 characters long"
- "Passwords do not match"

## Security Features

### Rate Limiting:
- Prevents brute force attacks
- IP-based limiting
- Different limits for different endpoints

### CSRF Protection:
- All forms include CSRF tokens
- Tokens validated on submission
- Prevents cross-site request forgery

### Input Validation:
- Server-side validation for all inputs
- Client-side validation for immediate feedback
- Proper trimming and sanitization

## Next Steps

If issues persist:
1. Check server logs for detailed error messages
2. Verify database connectivity
3. Test email sending functionality
4. Check session storage configuration
5. Verify all required environment variables are set
