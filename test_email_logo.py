#!/usr/bin/env python3
"""
Test script to verify email logo functionality
"""

import os
import sys
from flask import Flask
from flask_mail import Mail
from email_service import CVBioLabsEmailService

def test_logo_loading():
    """Test if the logo can be loaded properly"""
    print("🧪 Testing CVBioLabs Email Logo Functionality")
    print("=" * 50)
    
    # Create a minimal Flask app for testing
    app = Flask(__name__)
    app.config.update({
        'MAIL_SERVER': 'smtp.gmail.com',
        'MAIL_PORT': 587,
        'MAIL_USE_TLS': True,
        'MAIL_USERNAME': '<EMAIL>',
        'MAIL_PASSWORD': 'test',
        'MAIL_DEFAULT_SENDER': '<EMAIL>',
        'MAIL_SUPPRESS_SEND': True  # Don't actually send emails
    })
    
    with app.app_context():
        # Initialize mail and email service
        mail = Mail(app)
        email_service = CVBioLabsEmailService(mail)
        
        # Test logo loading
        print("1. Testing logo base64 loading...")
        logo_base64 = email_service.logo_base64
        
        if logo_base64:
            print(f"   ✅ Logo loaded successfully!")
            print(f"   📏 Logo size: {len(logo_base64):,} characters")
            print(f"   🔍 Preview: {logo_base64[:50]}...")
            
            # Check if it's a valid data URL
            if logo_base64.startswith('data:image/'):
                print("   ✅ Valid data URL format")
            else:
                print("   ❌ Invalid data URL format")
                
        else:
            print("   ❌ Logo failed to load")
            
        # Test base context
        print("\n2. Testing email context...")
        context = email_service._get_base_context("<EMAIL>")
        
        if 'logo_base64' in context:
            print("   ✅ Logo included in email context")
            if context['logo_base64'] == logo_base64:
                print("   ✅ Logo context matches loaded logo")
            else:
                print("   ❌ Logo context mismatch")
        else:
            print("   ❌ Logo not included in email context")
            
        # Test file existence
        print("\n3. Testing file availability...")
        
        files_to_check = [
            'logo_base64.txt',
            'static/images/CV.png',
            'static/images/logo.jpg'
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"   ✅ {file_path} exists ({size:,} bytes)")
            else:
                print(f"   ❌ {file_path} not found")
                
        print("\n🎯 Summary:")
        if logo_base64:
            print("   ✅ Your CV.png logo will now appear in emails!")
            print("   ✅ No more placeholder images will show up")
            print("   ✅ Fallback text logo available if image fails")
        else:
            print("   ⚠️  Logo loading failed - emails will use text fallback")
            
        print("\n📧 Next steps:")
        print("   1. Restart your Flask application")
        print("   2. Send a test email to verify the logo appears")
        print("   3. Check that only your CV.png logo shows up")

if __name__ == '__main__':
    test_logo_loading()
