"""
Security monitoring and alerting for CVBioLabs application
Provides audit logging, threat detection, and security alerts
"""

import logging
import json
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque
from threading import Lock
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from flask import request, session
import os

# Configure security logger
security_logger = logging.getLogger('security')
security_handler = logging.FileHandler('logs/security.log')
security_formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s'
)
security_handler.setFormatter(security_formatter)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

class SecurityMonitor:
    """Real-time security monitoring and threat detection"""
    
    def __init__(self):
        self.failed_logins = defaultdict(deque)  # IP -> deque of timestamps
        self.suspicious_activities = defaultdict(list)  # IP -> list of activities
        self.rate_limit_violations = defaultdict(deque)  # IP -> deque of timestamps
        self.lock = Lock()
        
        # Thresholds
        self.FAILED_LOGIN_THRESHOLD = 5  # Max failed logins per IP
        self.FAILED_LOGIN_WINDOW = 300  # 5 minutes
        self.RATE_LIMIT_THRESHOLD = 10  # Max rate limit violations
        self.RATE_LIMIT_WINDOW = 600  # 10 minutes
        
    def log_failed_login(self, ip_address, email, reason):
        """Log and monitor failed login attempts"""
        with self.lock:
            current_time = time.time()
            
            # Add to failed logins
            self.failed_logins[ip_address].append(current_time)
            
            # Clean old entries
            while (self.failed_logins[ip_address] and 
                   current_time - self.failed_logins[ip_address][0] > self.FAILED_LOGIN_WINDOW):
                self.failed_logins[ip_address].popleft()
            
            # Check if threshold exceeded
            if len(self.failed_logins[ip_address]) >= self.FAILED_LOGIN_THRESHOLD:
                self._trigger_security_alert(
                    'MULTIPLE_FAILED_LOGINS',
                    f'IP {ip_address} has {len(self.failed_logins[ip_address])} failed login attempts',
                    {
                        'ip_address': ip_address,
                        'email': email,
                        'reason': reason,
                        'count': len(self.failed_logins[ip_address])
                    }
                )
            
            # Log the event
            security_logger.warning(f"Failed login attempt - IP: {ip_address}, Email: {email}, Reason: {reason}")
    
    def log_suspicious_activity(self, ip_address, activity_type, details):
        """Log suspicious activities"""
        with self.lock:
            activity = {
                'timestamp': time.time(),
                'type': activity_type,
                'details': details
            }
            
            self.suspicious_activities[ip_address].append(activity)
            
            # Check for patterns
            if self._detect_suspicious_patterns(ip_address):
                self._trigger_security_alert(
                    'SUSPICIOUS_ACTIVITY_PATTERN',
                    f'Suspicious activity pattern detected from IP {ip_address}',
                    {
                        'ip_address': ip_address,
                        'activities': self.suspicious_activities[ip_address][-5:]  # Last 5 activities
                    }
                )
            
            security_logger.warning(f"Suspicious activity - IP: {ip_address}, Type: {activity_type}, Details: {details}")
    
    def log_rate_limit_violation(self, ip_address, endpoint, limit):
        """Log rate limit violations"""
        with self.lock:
            current_time = time.time()
            
            self.rate_limit_violations[ip_address].append(current_time)
            
            # Clean old entries
            while (self.rate_limit_violations[ip_address] and 
                   current_time - self.rate_limit_violations[ip_address][0] > self.RATE_LIMIT_WINDOW):
                self.rate_limit_violations[ip_address].popleft()
            
            # Check if threshold exceeded
            if len(self.rate_limit_violations[ip_address]) >= self.RATE_LIMIT_THRESHOLD:
                self._trigger_security_alert(
                    'EXCESSIVE_RATE_LIMITING',
                    f'IP {ip_address} has excessive rate limit violations',
                    {
                        'ip_address': ip_address,
                        'endpoint': endpoint,
                        'limit': limit,
                        'violations': len(self.rate_limit_violations[ip_address])
                    }
                )
            
            security_logger.warning(f"Rate limit violation - IP: {ip_address}, Endpoint: {endpoint}, Limit: {limit}")
    
    def _detect_suspicious_patterns(self, ip_address):
        """Detect suspicious activity patterns"""
        activities = self.suspicious_activities[ip_address]
        
        if len(activities) < 3:
            return False
        
        # Check for rapid successive activities
        recent_activities = [a for a in activities if time.time() - a['timestamp'] < 60]  # Last minute
        if len(recent_activities) >= 3:
            return True
        
        # Check for diverse attack types
        activity_types = set(a['type'] for a in activities[-10:])  # Last 10 activities
        if len(activity_types) >= 3:
            return True
        
        return False
    
    def _trigger_security_alert(self, alert_type, message, details):
        """Trigger security alert"""
        alert = {
            'timestamp': datetime.utcnow().isoformat(),
            'type': alert_type,
            'message': message,
            'details': details
        }
        
        # Log critical alert
        security_logger.critical(f"SECURITY ALERT: {json.dumps(alert)}")
        
        # Send email alert if configured
        self._send_email_alert(alert)
        
        # Could also integrate with external monitoring systems here
        # e.g., Slack, PagerDuty, etc.
    
    def _send_email_alert(self, alert):
        """Send email security alert"""
        try:
            alert_email = os.getenv('SECURITY_ALERT_EMAIL')
            if not alert_email:
                return
            
            smtp_server = os.environ.get('MAIL_SERVER', 'smtpout.secureserver.net')
            smtp_port = int(os.getenv('MAIL_PORT', '465'))
            smtp_username = os.getenv('SMTP_USERNAME')
            smtp_password = os.getenv('SMTP_PASSWORD')
            
            if not all([smtp_username, smtp_password]):
                return
            
            msg = MIMEMultipart()
            msg['From'] = smtp_username
            msg['To'] = alert_email
            msg['Subject'] = f"CVBioLabs Security Alert: {alert['type']}"
            
            body = f"""
Security Alert Detected

Type: {alert['type']}
Time: {alert['timestamp']}
Message: {alert['message']}

Details:
{json.dumps(alert['details'], indent=2)}

Please investigate immediately.

CVBioLabs Security System
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Use SMTP_SSL for port 465 (GoDaddy requires SSL)
            server = smtplib.SMTP_SSL(smtp_server, smtp_port)
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()
            
        except Exception as e:
            security_logger.error(f"Failed to send security alert email: {e}")

class AuditLogger:
    """Comprehensive audit logging for compliance and forensics"""
    
    def __init__(self):
        # Configure audit logger
        self.audit_logger = logging.getLogger('audit')
        audit_handler = logging.FileHandler('logs/audit.log')
        audit_formatter = logging.Formatter(
            '%(asctime)s - AUDIT - %(message)s'
        )
        audit_handler.setFormatter(audit_formatter)
        self.audit_logger.addHandler(audit_handler)
        self.audit_logger.setLevel(logging.INFO)
    
    def log_user_action(self, action, user_id=None, details=None):
        """Log user actions for audit trail"""
        audit_entry = {
            'action': action,
            'user_id': user_id or session.get('user_id'),
            'user_role': session.get('user_role'),
            'ip_address': request.remote_addr if request else None,
            'user_agent': request.headers.get('User-Agent') if request else None,
            'endpoint': request.endpoint if request else None,
            'timestamp': datetime.utcnow().isoformat(),
            'details': details or {}
        }
        
        self.audit_logger.info(json.dumps(audit_entry))
    
    def log_data_access(self, resource_type, resource_id, action='READ'):
        """Log data access for sensitive resources"""
        self.log_user_action(
            f'DATA_ACCESS_{action}',
            details={
                'resource_type': resource_type,
                'resource_id': resource_id
            }
        )
    
    def log_admin_action(self, action, target_user_id=None, details=None):
        """Log administrative actions"""
        self.log_user_action(
            f'ADMIN_{action}',
            details={
                'target_user_id': target_user_id,
                'admin_details': details
            }
        )
    
    def log_authentication_event(self, event_type, email, success=True):
        """Log authentication events"""
        self.log_user_action(
            f'AUTH_{event_type}',
            details={
                'email': email,
                'success': success
            }
        )
    
    def log_file_operation(self, operation, file_path, file_type=None):
        """Log file operations"""
        self.log_user_action(
            f'FILE_{operation}',
            details={
                'file_path': file_path,
                'file_type': file_type
            }
        )

class SecurityMetrics:
    """Security metrics collection and reporting"""
    
    def __init__(self):
        self.metrics = defaultdict(int)
        self.daily_metrics = defaultdict(lambda: defaultdict(int))
        self.lock = Lock()
    
    def increment_metric(self, metric_name, value=1):
        """Increment a security metric"""
        with self.lock:
            self.metrics[metric_name] += value
            
            # Daily metrics
            today = datetime.now().strftime('%Y-%m-%d')
            self.daily_metrics[today][metric_name] += value
    
    def get_metrics(self):
        """Get current security metrics"""
        with self.lock:
            return dict(self.metrics)
    
    def get_daily_metrics(self, date=None):
        """Get metrics for a specific date"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')
        
        with self.lock:
            return dict(self.daily_metrics.get(date, {}))
    
    def generate_security_report(self):
        """Generate security summary report"""
        today = datetime.now().strftime('%Y-%m-%d')
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        report = {
            'date': today,
            'total_metrics': self.get_metrics(),
            'today_metrics': self.get_daily_metrics(today),
            'yesterday_metrics': self.get_daily_metrics(yesterday)
        }
        
        return report

# Global instances
security_monitor = SecurityMonitor()
audit_logger = AuditLogger()
security_metrics = SecurityMetrics()

# Ensure logs directory exists
os.makedirs('logs', exist_ok=True)
