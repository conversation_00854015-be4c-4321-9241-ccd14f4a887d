"""
Rate limiting configuration for CVBioLabs Flask application
"""

import os

class RateLimitConfig:
    """Centralized rate limiting configuration"""
    
    def __init__(self):
        self.env = os.getenv('FLASK_ENV', 'development')
        self.is_production = self.env == 'production'
        self.is_development = self.env == 'development'
    
    @property
    def default_limits(self):
        """Default rate limits for all endpoints"""
        if self.is_production:
            return ["500 per day", "100 per hour"]
        else:
            return ["1000 per day", "200 per hour"]
    
    @property
    def login_limits(self):
        """Rate limits for login endpoint"""
        if self.is_production:
            return "8 per minute"  # Stricter in production
        else:
            return "15 per minute"  # More lenient in development
    
    @property
    def signup_limits(self):
        """Rate limits for signup endpoint"""
        if self.is_production:
            return "3 per minute"
        else:
            return "8 per minute"
    
    @property
    def password_reset_limits(self):
        """Rate limits for password reset"""
        if self.is_production:
            return "5 per minute"
        else:
            return "10 per minute"
    
    @property
    def api_limits(self):
        """Rate limits for API endpoints"""
        if self.is_production:
            return "60 per minute"
        else:
            return "120 per minute"
    
    @property
    def dashboard_limits(self):
        """Rate limits for dashboard access"""
        if self.is_production:
            return "30 per minute"
        else:
            return "60 per minute"
    
    @property
    def cart_limits(self):
        """Rate limits for cart operations"""
        if self.is_production:
            return "20 per minute"
        else:
            return "40 per minute"
    
    def get_storage_uri(self):
        """Get appropriate storage URI for rate limiter"""
        redis_url = os.getenv('REDIS_URL')
        if redis_url and self.is_production:
            return redis_url
        else:
            return "memory://"
    
    def get_limiter_config(self):
        """Get complete limiter configuration"""
        return {
            'default_limits': self.default_limits,
            'storage_uri': self.get_storage_uri(),
            'headers_enabled': True,
            'swallow_errors': True,
            'strategy': 'fixed-window'  # or 'moving-window' for more accuracy
        }
    
    def get_endpoint_limits(self):
        """Get all endpoint-specific limits"""
        return {
            'login': self.login_limits,
            'signup': self.signup_limits,
            'forgot_password': self.password_reset_limits,
            'reset_password': self.password_reset_limits,
            'dashboard': self.dashboard_limits,
            'api_endpoints': self.api_limits,
            'cart_operations': self.cart_limits
        }
    
    def print_config(self):
        """Print current rate limiting configuration"""
        print(f"🔧 Rate Limiting Configuration ({self.env})")
        print("=" * 50)
        print(f"Default limits: {self.default_limits}")
        print(f"Storage: {self.get_storage_uri()}")
        print("\nEndpoint-specific limits:")
        for endpoint, limit in self.get_endpoint_limits().items():
            print(f"  {endpoint}: {limit}")

# Global configuration instance
rate_config = RateLimitConfig()

# Helper functions for use in Flask app
def get_rate_limit_config():
    """Get rate limiting configuration for Flask app"""
    return rate_config.get_limiter_config()

def get_endpoint_limit(endpoint_name):
    """Get rate limit for specific endpoint"""
    limits = rate_config.get_endpoint_limits()
    return limits.get(endpoint_name, rate_config.default_limits[1])  # Default to hourly limit

if __name__ == '__main__':
    # Print configuration when run directly
    rate_config.print_config()
