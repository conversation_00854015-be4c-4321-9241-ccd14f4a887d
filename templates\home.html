<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVBIOLABS - Advanced Medical Diagnostics | Hyderabad</title>
    <meta name="description" content="CVBIOLABS - Premier medical diagnostic laboratory in Serilingampally, Hyderabad. Comprehensive health check-ups, affordable packages, and accurate results.">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-orange: #f58220;
            --deep-blue: #003865;
            --bright-blue: #007dc5;
            --light-bg: #f0f7fb;
            --white: #ffffff;
            --text-dark: #1a1a1a;
            --text-light: #6b7280;
            --success: #10b981;
            --gradient-primary: linear-gradient(135deg, var(--deep-blue) 0%, var(--bright-blue) 100%);
            --gradient-accent: linear-gradient(135deg, var(--primary-orange) 0%, #ff6b35 100%);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
            background: var(--white);
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-orange);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #e07020;
        }

        /* Header with Glass Effect */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo img {
            width: 70px;
            height: 70px;
            border-radius: 8px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .logo-text {
            font-family: 'Poppins', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 500;
            position: relative;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(245, 130, 32, 0.1);
            color: var(--primary-orange);
        }

        .auth-buttons {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-accent);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-dark);
            border: 2px solid rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            background: var(--light-bg);
            border-color: var(--primary-orange);
        }

        /* Dropdown Styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background: #fff;
            min-width: 180px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
            border-radius: 12px;
            z-index: 1001;
            padding: 0.5rem 0;
        }
        .dropdown-content a {
            color: #1a1a1a;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            display: block;
            font-weight: 500;
            border-radius: 0;
            transition: background 0.2s;
        }
        .dropdown-content a:hover {
            background: #f0f7fb;
            color: #f58220;
        }
        .dropdown:hover .dropdown-content,
        .dropdown:focus-within .dropdown-content {
            display: block;
        }
        .dropdown-btn {
            background: #fff;
            border: 2px solid #eee;
            border-radius: 50px;
            padding: 0.5rem 1.25rem;
            font-weight: 600;
            color: #1a1a1a;
            cursor: pointer;
            transition: border 0.2s;
        }
        .dropdown-btn:hover,
        .dropdown-btn:focus {
            border-color: #f58220;
            color: #f58220;
        }

        /* Hero Section with Diagonal Layout */
        .hero {
            min-height: 100vh;
            position: relative;
            background: linear-gradient(135deg, var(--light-bg) 0%, rgba(240, 247, 251, 0.5) 50%, white 100%);
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -10%;
            width: 80%;
            height: 150%;
            background: linear-gradient(45deg, transparent 0%, rgba(0, 125, 197, 0.05) 50%, transparent 100%);
            transform: rotate(15deg);
            z-index: 1;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 120px 2rem 2rem;
            display: grid;
            grid-template-columns: 1.2fr 1fr;
            gap: 4rem;
            align-items: center;
            min-height: 100vh;
            position: relative;
            z-index: 2;
        }

        .hero-content {
            animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(245, 130, 32, 0.1);
            color: var(--primary-orange);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(245, 130, 32, 0.2);
        }

        .hero-content h1 {
            font-family: 'Poppins', sans-serif;
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: var(--text-dark);
        }

        .hero-content .highlight {
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-content p {
            font-size: 1.1rem;
            color: var(--text-light);
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 3rem;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }

        .hero-stat {
            text-align: center;
        }

        .hero-stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-orange);
            display: block;
        }

        .hero-stat-label {
            font-size: 0.9rem;
            color: var(--text-light);
        }

        .hero-visual {
            position: relative;
            animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
        }

        .hero-image-container {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--shadow-xl);
            transform: rotate(5deg);
            transition: transform 0.3s ease;
        }

        .hero-image-container:hover {
            transform: rotate(0deg) scale(1.02);
        }

        .hero-visual img {
            width: 100%;
            height: auto;
            display: block;
        }

        .floating-cards {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-card {
            position: absolute;
            background: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            animation: float 4s ease-in-out infinite;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .floating-card:nth-child(1) {
            top: 15%;
            left: -15%;
            animation-delay: 0s;
        }

        .floating-card:nth-child(2) {
            bottom: 25%;
            right: -20%;
            animation-delay: 1.3s;
        }

        .floating-card:nth-child(3) {
            top: 60%;
            left: -10%;
            animation-delay: 2.6s;
        }

        /* Services with Card Grid */
        .services {
            padding: 6rem 0;
            background: white;
            position: relative;
        }

        .services::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: linear-gradient(180deg, var(--light-bg) 0%, transparent 100%);
        }

        .services-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-family: 'Poppins', sans-serif;
            font-size: clamp(2rem, 4vw, 2.5rem);
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .section-header p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .service-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .service-icon {
            width: 70px;
            height: 70px;
            background: var(--gradient-accent);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            margin-bottom: 1.5rem;
        }

        .service-card h3 {
            font-family: 'Poppins', sans-serif;
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .service-card p {
            color: var(--text-light);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .price-section {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .price-current {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--success);
        }

        .price-original {
            font-size: 1rem;
            color: var(--text-light);
            text-decoration: line-through;
        }

        .discount {
            background: var(--primary-orange);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* Stats Section with Animated Counters */
        .stats {
            background: var(--gradient-primary);
            padding: 4rem 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stats::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 100%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 15s linear infinite;
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .stat-item {
            animation: fadeInUp 0.3s ease-out;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-orange);
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Process with Timeline */
        .process {
            padding: 6rem 0;
            background: var(--light-bg);
            position: relative;
        }

        .process-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .process-timeline {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
            position: relative;
        }

        .process-step {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            text-align: center;
            position: relative;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .process-step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin: 0 auto 1.5rem;
            position: relative;
            z-index: 2;
        }

        .step-title {
            font-family: 'Poppins', sans-serif;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .step-description {
            color: var(--text-light);
            line-height: 1.6;
        }

        /* Features with Bento Grid */
        .features {
            padding: 6rem 0;
            background: white;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .features-bento {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: linear-gradient(135deg, var(--light-bg) 0%, white 100%);
            padding: 2.5rem;
            border-radius: 20px;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::before {
            opacity: 0.05;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1.5rem;
            position: relative;
            z-index: 2;
        }

        .feature-card h3 {
            font-family: 'Poppins', sans-serif;
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .feature-card p {
            color: var(--text-light);
            line-height: 1.6;
            position: relative;
            z-index: 2;
        }

        /* Contact with Split Layout */
        .contact {
            padding: 6rem 0;
            background: var(--gradient-primary);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .contact::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon points="0,0 1000,300 1000,1000 0,1000" fill="rgba(255,255,255,0.03)"/></svg>');
        }

        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .contact-info h3 {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            color: var(--primary-orange);
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .contact-item i {
            font-size: 1.5rem;
            color: var(--primary-orange);
            width: 30px;
        }

        .contact-visual {
            position: relative;
        }

        .contact-visual img {
            width: 100%;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
        }

        /* Modern Footer */
        .footer {
            background: var(--text-dark);
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1.5fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-brand {
            max-width: 350px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .footer-logo img {
            width: 40px;
            height: 40px;
            border-radius: 6px;
        }

        .footer-logo-text {
            font-family: 'Poppins', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-orange);
        }

        .footer-tagline {
            color: #a1a1aa;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .newsletter h5 {
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-orange);
        }

        .newsletter-form {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .newsletter-form input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #374151;
            border-radius: 8px;
            background: #1f2937;
            color: white;
        }

        .newsletter-form button {
            background: var(--gradient-accent);
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            color: white;
            cursor: pointer;
        }

        .footer-social {
            display: flex;
            gap: 1rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background: #374151;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--primary-orange);
            transform: translateY(-2px);
        }

        .footer-column h4 {
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary-orange);
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: #a1a1aa;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary-orange);
        }

        .footer-contact-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .footer-contact-item i {
            color: var(--primary-orange);
            margin-top: 0.25rem;
            width: 20px;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .footer-copyright {
            color: #a1a1aa;
        }

        .footer-legal {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .footer-legal a {
            color: #a1a1aa;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-legal a:hover {
            color: var(--primary-orange);
        }

        /* WhatsApp Button */
        .whatsapp-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #25D366;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            text-decoration: none;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            transition: all 0.3s ease;
            animation: pulse 1.5s infinite;
        }

        .whatsapp-btn:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-xl);
        }

        /* Animations */
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 3rem;
            }

            .contact-container {
                grid-template-columns: 1fr;
            }

            .footer-content {
                grid-template-columns: 1fr 1fr;
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-container {
                padding: 100px 1rem 2rem;
            }

            .hero-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .features-bento {
                grid-template-columns: 1fr;
            }

            .stats-container {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .process-timeline {
                grid-template-columns: 1fr;
            }

            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }

            .footer-legal {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="nav">
            <div class="logo">
                <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBIOLABS Logo">
                <span class="logo-text">CVBIOLABS</span>
            </div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#services">Services</a></li>
                <li><a href="#process">Process</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <div class="auth-buttons">
                {% if current_user.is_authenticated %}
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-btn">
                            <i class="fas fa-user"></i> {{ current_user.username }}
                        </button>
                        <div class="dropdown-content">
                            <a href="{{ url_for('dashboard') }}">Dashboard</a>
                            <a href="{{ url_for('dashboard') }}#bookings">My Bookings</a>
                            <a href="{{ url_for('dashboard') }}#reports">My Reports</a>
                            <a href="{{ url_for('dashboard') }}#profile">Profile</a>
                            <a href="{{ url_for('logout') }}">Logout</a>
                        </div>
                    </div>
                {% else %}
                    <a href="{{ url_for('login') }}" class="btn btn-secondary">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </a>
                    <a href="{{ url_for('signup') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Sign Up
                    </a>
                {% endif %}
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-award"></i>
                    Trusted Medical Laboratory
                </div>
                <h1>
                    Advanced <span class="highlight">Health</span><br>
                    Diagnostics for<br>
                    Better Tomorrow
                </h1>
                <p>Experience world-class diagnostic services at CVBIOLABS with cutting-edge technology, comprehensive health packages, and accurate results you can trust.</p>
                
                <div class="hero-buttons">
                    <a href="{{ url_for('test') }}" class="btn btn-primary">
                        <i class="fas fa-microscope"></i> Book Now
                    </a>
                    <a href="#contact" class="btn btn-secondary">
                        <i class="fas fa-phone"></i> Get in Touch
                    </a>
                </div>

                <div class="hero-stats">
                    <div class="hero-stat">
                        <span class="hero-stat-number">10,000+</span>
                        <span class="hero-stat-label">Tests Completed</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number">24hrs</span>
                        <span class="hero-stat-label">Quick Reports</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number">50+</span>
                        <span class="hero-stat-label">Test Parameters</span>
                    </div>
                </div>
            </div>
            
            <div class="hero-visual">
                <div class="hero-image-container">
                    
             <img src="{{ url_for('static', filename='images/test3.png') }}" alt="Modern Medical Laboratory">

                </div>
                <div class="floating-cards">
                    <div class="floating-card">
                        <i class="fas fa-shield-check" style="color: var(--success); margin-right: 0.5rem;"></i>
                        <span style="font-weight: 600;">100% Accurate</span>
                    </div>
                    <div class="floating-card">
                        <i class="fas fa-clock" style="color: var(--bright-blue); margin-right: 0.5rem;"></i>
                        <span style="font-weight: 600;">Fast Results</span>
                    </div>
                    <div class="floating-card">
                        <i class="fas fa-home" style="color: var(--primary-orange); margin-right: 0.5rem;"></i>
                        <span style="font-weight: 600;">Home Collection</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="stats-container">
            <div class="stat-item animate-on-scroll">
                <span class="stat-number">10,000+</span>
                <span class="stat-label">Tests Completed</span>
            </div>
            <div class="stat-item animate-on-scroll">
                <span class="stat-number">5,000+</span>
                <span class="stat-label">Happy Patients</span>
            </div>
            <div class="stat-item animate-on-scroll">
                <span class="stat-number">50+</span>
                <span class="stat-label">Test Parameters</span>
            </div>
            <div class="stat-item animate-on-scroll">
                <span class="stat-number">24hrs</span>
                <span class="stat-label">Quick Reports</span>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="services-container">
            <div class="section-header animate-on-scroll">
                <h2>Comprehensive Health Packages</h2>
                <p>Choose from our range of specialized diagnostic packages designed for early detection and prevention</p>
            </div>
            
            <div class="services-grid">
                <div class="service-card animate-on-scroll">
                    <div class="service-icon">
                        <i class="fas fa-heart-pulse"></i>
                    </div>
                    <h3>Master Health Check-Up</h3>
                    <p>Complete comprehensive health screening including CBC, Blood Sugar, HbA1c, Lipid Profile, Thyroid, Liver & Kidney function tests, Vitamins D & B12.</p>
                    <div class="price-section">
                        <span class="price-current">₹2,999</span>
                        <span class="price-original">₹6,015</span>
                        <span class="discount">50% OFF</span>
                    </div>
                    <a class="btn btn-primary" style="width: 100%;" href="{{ url_for('test') }}">Book Now</a>
                </div>

                <div class="service-card animate-on-scroll">
                    <div class="service-icon">
                        <i class="fas fa-bone"></i>
                    </div>
                    <h3>Joint Pain Profile</h3>
                    <p>Specialized tests for joint health including CBC, ESR, Rheumatoid Factor, ASO titer, and inflammatory markers for comprehensive joint assessment.</p>
                    <div class="price-section">
                        <span class="price-current">₹1,199</span>
                        <span class="price-original">₹2,170</span>
                        <span class="discount">45% OFF</span>
                    </div>
                    <a class="btn btn-primary" style="width: 100%;" href="{{ url_for('test') }}">Book Now</a>
                </div>

                <div class="service-card animate-on-scroll">
                    <div class="service-icon">
                        <i class="fas fa-tint"></i>
                    </div>
                    <h3>Diabetic Profile</h3>
                    <p>Essential diabetes monitoring tests including Fasting Glucose, PLBS, HbA1c, CBC, and Urine Analysis for complete diabetes management.</p>
                    <div class="price-section">
                        <span class="price-current">₹799</span>
                        <span class="price-original">₹1,380</span>
                        <span class="discount">42% OFF</span>
                    </div>
                    <a class="btn btn-primary" style="width: 100%;" href="{{ url_for('test') }}">Book Now</a>
                </div>

                <div class="service-card animate-on-scroll">
                    <div class="service-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Immunity Profile</h3>
                    <p>Comprehensive immunity assessment including CBC, Iron studies, UIBC, TIBC, and Transferrin Saturation to evaluate your immune system.</p>
                    <div class="price-section">
                        <span class="price-current">₹899</span>
                        <span class="price-original">₹1,500</span>
                        <span class="discount">40% OFF</span>
                    </div>
                    <a class="btn btn-primary" style="width: 100%;" href="{{ url_for('test') }}">Book Now</a>
                </div>

                <div class="service-card animate-on-scroll">
                    <div class="service-icon">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                    <h3>Fever Profile I</h3>
                    <p>Complete fever investigation including CBC, Typhoid, Malaria, Dengue panel, and inflammatory markers for accurate diagnosis.</p>
                    <div class="price-section">
                        <span class="price-current">₹1,999</span>
                        <span class="price-original">₹3,690</span>
                        <span class="discount">46% OFF</span>
                    </div>
                    <a class="btn btn-primary" style="width: 100%;" href="{{ url_for('test') }}">Book Now</a>
                </div>

                <div class="service-card animate-on-scroll">
                    <div class="service-icon">
                        <i class="fas fa-baby"></i>
                    </div>
                    <h3>ANC Profile</h3>
                    <p>Comprehensive antenatal care profile including CBC, HIV, Hepatitis screening, Blood grouping, and Thyroid function for expecting mothers.</p>
                    <div class="price-section">
                        <span class="price-current">₹2,499</span>
                        <span class="price-original">₹4,285</span>
                        <span class="discount">42% OFF</span>
                    </div>
                    <a class="btn btn-primary" style="width: 100%;" href="{{ url_for('test') }}">Book Now</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section class="process" id="process">
        <div class="process-container">
            <div class="section-header animate-on-scroll">
                <h2>Simple 4-Step Process</h2>
                <p>Get your health check-up done in just 4 easy steps</p>
            </div>
            
            <div class="process-timeline">
                <div class="process-step animate-on-scroll">
                    <div class="step-number">1</div>
                    <h3 class="step-title">Choose Package</h3>
                    <p class="step-description">Select the health package that suits your needs from our comprehensive range of diagnostic services.</p>
                </div>
                
                <div class="process-step animate-on-scroll">
                    <div class="step-number">2</div>
                    <h3 class="step-title">Book Appointment</h3>
                    <p class="step-description">Schedule your convenient time slot through our easy booking system or call us directly.</p>
                </div>
                
                <div class="process-step animate-on-scroll">
                    <div class="step-number">3</div>
                    <h3 class="step-title">Sample Collection</h3>
                    <p class="step-description">Visit our modern facility or opt for home collection service for your convenience.</p>
                </div>
                
                <div class="process-step animate-on-scroll">
                    <div class="step-number">4</div>
                    <h3 class="step-title">Get Results</h3>
                    <p class="step-description">Receive your accurate test results within 24-48 hours via email, SMS, or our patient portal.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="about">
        <div class="features-container">
            <div class="section-header animate-on-scroll">
                <h2>Why Choose CVBIOLABS?</h2>
                <p>We're committed to providing the highest quality diagnostic services with cutting-edge technology</p>
            </div>
            
            <div class="features-bento">
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <h3>Advanced Technology</h3>
                    <p>State-of-the-art laboratory equipment ensuring accurate and reliable results with international quality standards</p>
                </div>
                
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>Quick Turnaround</h3>
                    <p>Fast processing with most results available within 24-48 hours, ensuring timely healthcare decisions</p>
                </div>
                
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3>Expert Team</h3>
                    <p>Qualified medical professionals and experienced technicians committed to excellence in diagnostics</p>
                </div>
                
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <h3>Affordable Pricing</h3>
                    <p>Competitive pricing with significant discounts on comprehensive packages, making healthcare accessible</p>
                </div>
                
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3>Home Collection</h3>
                    <p>Convenient sample collection at your doorstep with trained phlebotomists for your comfort</p>
                </div>
                
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3>Quality Assured</h3>
                    <p>NABL accredited laboratory maintaining highest standards of quality and accuracy in all tests</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="contact-container">
            <div class="contact-info animate-on-scroll">
                <h3>Get in Touch</h3>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <div>
                        <strong>Address</strong><br>
                        Nallagandla, Serilingampally, Hyderabad, Telangana
                    </div>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <div>
                        <strong>Phone</strong><br>
                        +91 78936 20683
                    </div>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <div>
                        <strong>Email</strong><br>
                        <EMAIL>
                    </div>
                </div>
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <div>
                        <strong>Working Hours</strong><br>
                        Mon-Sat: 6:30 AM - 6:30 PM
                    </div>
                </div>
            </div>
            
            <div class="contact-visual animate-on-scroll">
                <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Medical Professional at Work">
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-brand animate-on-scroll">
                    <div class="footer-logo">
                        <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBIOLABS Logo">
                        <div class="footer-logo-text">CVBIOLABS</div>
                    </div>
                    <p class="footer-tagline">
                        Your trusted partner in health diagnostics. We provide comprehensive, accurate, and affordable medical testing services to help you maintain optimal health and wellbeing.
                    </p>
                    <div class="newsletter">
                        <h5>Stay Updated</h5>
                        <div class="newsletter-form">
                            <input type="email" placeholder="Enter your email">
                            <button type="submit">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                    <div class="footer-social">
                        <a href="#" class="social-link" target="_blank">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="https://wa.me/917893620683" class="social-link" target="_blank">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>

                <div class="footer-column animate-on-scroll">
                    <h4>Services</h4>
                    <ul class="footer-links">
                        <li><a href="#services">Master Health Check-Up</a></li>
                        <li><a href="#services">Joint Pain Profile</a></li>
                        <li><a href="#services">Diabetic Profile</a></li>
                        <li><a href="#services">Immunity Profile</a></li>
                        <li><a href="#services">Fever Profile</a></li>
                        <li><a href="#services">ANC Profile</a></li>
                    </ul>
                </div>

                <div class="footer-column animate-on-scroll">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#process">Our Process</a></li>
                        <li><a href="#contact">Contact</a></li>
                        <li><a href="#">Book Appointment</a></li>
                        <li><a href="#">Test Results</a></li>
                    </ul>
                </div>

                <div class="footer-column animate-on-scroll">
                    <h4>Contact Info</h4>
                    <div class="footer-contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            Nallagandla, Serilingampally,<br>
                            Hyderabad, Telangana
                        </div>
                    </div>
                    <div class="footer-contact-item">
                        <i class="fas fa-phone"></i>
                        <div>+91 78936 20683</div>
                    </div>
                    <div class="footer-contact-item">
                        <i class="fas fa-envelope"></i>
                        <div><EMAIL></div>
                    </div>
                    <div class="footer-contact-item">
                        <i class="fas fa-clock"></i>
                        <div>
                            Mon-Sat: 6:30 AM - 6:30 PM<br>
                            Sunday: Closed
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 CVBIOLABS. All rights reserved. | Designed for a healthy life.</p>
                </div>
                <ul class="footer-legal">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Cookie Policy</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/917893620683?text=Hello! I would like to know more about your health check-up packages." class="whatsapp-btn" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.getElementById('header');
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const headerHeight = document.getElementById('header').offsetHeight;
                    const targetPosition = target.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Scroll animations
        function handleScrollAnimations() {
            const elements = document.querySelectorAll('.animate-on-scroll');
            
            elements.forEach((element, index) => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < window.innerHeight - elementVisible) {
                    setTimeout(() => {
                        element.classList.add('animated');
                    }, index * 50);
                }
            });
        }

        // Counter animation for stats
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                if (counter.classList.contains('animated')) return;
                
                const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
                const suffix = counter.textContent.replace(/[\d,]/g, '');
                let current = 0;
                const increment = target / 50;
                
                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.floor(current).toLocaleString() + suffix;
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target.toLocaleString() + suffix;
                        counter.classList.add('animated');
                    }
                };
                
                if (counter.getBoundingClientRect().top < window.innerHeight) {
                    updateCounter();
                }
            });
        }

        // Initialize animations
        window.addEventListener('scroll', () => {
            handleScrollAnimations();
            animateCounters();
        });
        
        window.addEventListener('load', () => {
            handleScrollAnimations();
            animateCounters();
        });

        // Form submissions
        document.addEventListener('DOMContentLoaded', function() {
            // Handle service booking
            const bookButtons = document.querySelectorAll('.service-card .btn');
            bookButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const serviceName = this.closest('.service-card').querySelector('h3').textContent;
                    const originalText = this.innerHTML;
                    
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Booking...';
                    this.disabled = true;
                    
                    setTimeout(() => {
                        alert(`Booking ${serviceName}. This would redirect to the booking system!`);
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 1000);
                });
            });

            // Newsletter subscription
            const newsletterForm = document.querySelector('.newsletter-form');
            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const button = this.querySelector('button');
                    const input = this.querySelector('input');
                    
                    if (input.value) {
                        alert('Thank you for subscribing to our newsletter!');
                        input.value = '';
                    }
                });
            }
        });

        // Parallax effect for hero background
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero::before');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Image lazy loading
        // document.addEventListener('DOMContentLoaded', function() {
        //     const images = document.querySelectorAll('img');
        //     images.forEach(img => {
        //         img.style.opacity = '0';
        //         img.style.transition = 'opacity 0.5s ease';
                
        //         img.addEventListener('load', function() {
        //             this.style.opacity = '1';
        //         });
                
        //         if (img.complete) {
        //             img.style.opacity = '1';
        //         }
        //     });
        // });
    </script>
</body>
</html>
