{% extends "ADMIN/adminbase.html" %}

{% block title %}User Management - Admin Dashboard{% endblock %}

{% block page_title %}User Management{% endblock %}

{% block extra_css %}
<meta name="csrf-token" content="{{ csrf_token }}">
<style>
    .user-type-card {
        border-radius: var(--border-radius);
        transition: var(--transition);
        border: 1px solid rgba(0, 0, 0, 0.05);
        overflow: hidden;
        position: relative;
    }

    .user-type-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    .user-type-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .user-type-card:hover::before {
        opacity: 1;
    }

    .user-type-card .card-body {
        padding: 2rem;
        position: relative;
        z-index: 1;
    }

    .user-type-card h5 {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 1rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .user-type-card h2 {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        font-family: 'Poppins', sans-serif;
    }

    .user-type-card small {
        font-size: 0.8rem;
        opacity: 0.8;
        font-weight: 500;
    }

    .bg-primary {
        background: var(--gradient-primary) !important;
    }

    .bg-success {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%) !important;
    }

    .bg-info {
        background: linear-gradient(135deg, var(--bright-blue) 0%, #0284c7 100%) !important;
    }

    .bg-warning {
        background: var(--gradient-accent) !important;
    }

    .required::after {
        content: " *";
        color: var(--danger);
    }

    /* Loader styling */
    .loader {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin-left: 10px;
        transition: opacity 0.3s ease;
        opacity: 1;
    }

    .loader.visually-hidden {
        opacity: 0;
        pointer-events: none;
    }

    .loader .spinner-border {
        width: 1.2rem;
        height: 1.2rem;
        border-width: 0.2em;
    }

    .loader .loader-text {
        font-size: 0.9rem;
        color: var(--primary-orange);
        font-weight: 500;
    }

    .table-responsive {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table th {
        background: var(--light-bg);
        color: var(--deep-blue);
        font-weight: 600;
        border: none;
        padding: 1rem 1.5rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table td {
        padding: 1rem 1.5rem;
        border-color: rgba(0, 0, 0, 0.05);
        vertical-align: middle;
    }

    .badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
        border-radius: 50px;
    }

    @media (max-width: 768px) {
        .user-type-card .card-body {
            padding: 1.5rem;
        }

        .user-type-card h2 {
            font-size: 2rem;
        }

        .table-responsive {
            font-size: 0.85rem;
        }

        .table th,
        .table td {
            padding: 0.75rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
    }

    @media (max-width: 480px) {
        .user-type-card .card-body {
            padding: 1rem;
        }

        .user-type-card h2 {
            font-size: 1.75rem;
        }

        .user-type-card h5 {
            font-size: 0.8rem;
        }

        .table th,
        .table td {
            padding: 0.5rem;
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-2"></i>Add New User
        </button>
    </div>

    <!-- User Type Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card user-type-card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Doctors</h5>
                    <h2 class="mb-0">{{ doctors }}</h2>
                    <small class="text-white-50">Active Users</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card user-type-card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Receptionists</h5>
                    <h2 class="mb-0">{{ receptionists }}</h2>
                    <small class="text-white-50">Active Users</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card user-type-card bg-info">
                <div class="card-body">
                    <h5 class="card-title">Patients</h5>
                    <h2 class="mb-0">{{ patients }}</h2>
                    <small class="text-muted">Active Users</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card user-type-card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">Pickup Agents</h5>
                    <h2 class="mb-0">{{ agents }}</h2>
                    <small class="text-white-50">Active Users</small>
                </div>
            </div>
        </div>
    </div>

    <!-- User Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">User Type</label>
                    <select class="form-select" id="userTypeFilter">
                        <option value="all">All Users</option>
                        <option value="Doctor">Doctors</option>
                        <option value="Receptionist">Receptionists</option>
                        <option value="Pickup Agent">Pickup Agents</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Search users...">
                </div>
                <div class="col-md-2">
                    <label class="form-label"> </label>
                    <button class="btn btn-primary w-100" onclick="filterUsers()">
                        <i class="fas fa-filter me-2"></i>Apply Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Professional ID</th>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if users %}
                            {% for user in users %}
                            <tr>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ user.professional_id or 'N/A' }}
                                    </span>
                                </td>
                                <td>{{ user.name }}</td>
                                <td>
                                    <span class="badge bg-{{ user.role|user_badge_color }}">
                                        {{ user.role }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ user.status|status_badge_color }}">
                                        {{ user.status }}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="editUser({{ user.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning" onclick="toggleStatus({{ user.id }}, '{{ user.status }}')">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteUser({{ user.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center">No users found</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <input type="hidden" id="userId" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label required">User Type</label>
                            <select class="form-select" required id="userType" name="userType">
                                <option value="">Select User Type</option>
                                <option value="Doctor">Doctor</option>
                                <option value="Receptionist">Receptionist</option>
                                
                                <option value="Pickup Agent">Pickup Agent</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Full Name</label>
                            <input type="text" class="form-control" id="fullName" name="fullName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Phone</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   required 
                                   pattern="^[6-9][0-9]{9}$"
                                   title="Phone number must start with 6-9 and be exactly 10 digits long"
                                   oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                   maxlength="10">
                            <small class="phone-validation-error text-danger" style="display: none;">Please enter a valid 10-digit phone number starting with 6-9</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button" onclick="generatePassword()">
                                    <i class="fas fa-key"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Confirm Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('confirmPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <!-- Doctor-specific fields -->
                        <div class="col-md-6 doctor-field" style="display: none;">
                            <label class="form-label">Specialization</label>
                            <input type="text" class="form-control" id="specialization" name="specialization">
                        </div>
                        <div class="col-md-6 doctor-field" style="display: none;">
                            <label class="form-label">License Number</label>
                            <input type="text" class="form-control" id="licenseNumber" name="licenseNumber">
                        </div>
                        <!-- Agent-specific fields -->
                        <div class="col-md-6 agent-field" style="display: none;">
                            <label class="form-label required">Vehicle Number</label>
                            <input type="text" class="form-control" id="vehicleNumber" name="vehicleNumber">
                        </div>
                        <div class="col-md-6 agent-field" style="display: none;">
                            <label class="form-label required">Service Area</label>
                            <input type="text" class="form-control" id="serviceArea" name="serviceArea">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">Save User</button>
                <span class="visually-hidden loader" id="saveUserLoader">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span class="loader-text">Saving...</span>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this user? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize users array and CSRF token
    let users = [];
    try {
        const usersData = {{ users|tojson|safe }};
        users = Array.isArray(usersData) ? usersData : [];
    } catch (e) {
        console.error('Error initializing users:', e);
        showAlert('Error loading user data', 'danger');
    }
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Show alert message
    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3`;
        alertDiv.style.zIndex = '9999';
        alertDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                <div>${message}</div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 150);
        }, 5000);
    }

    // Form validation
    function validateForm() {
        const form = document.getElementById('userForm');
        const password = form.password.value;
        const confirmPassword = form.confirmPassword.value;
        const userType = form.userType.value;
        const fullName = form.fullName.value;
        const email = form.email.value;
        const phone = form.phone.value;

        // Clear previous error messages
        document.querySelectorAll('.error-message').forEach(el => el.remove());

        let isValid = true;

        // Validate required fields
        if (!userType) {
            showFieldError('userType', 'Please select a user type');
            isValid = false;
        }

        if (!fullName) {
            showFieldError('fullName', 'Please enter full name');
            isValid = false;
        }

        if (!email) {
            showFieldError('email', 'Please enter email');
            isValid = false;
        } else if (!isValidEmail(email)) {
            showFieldError('email', 'Please enter a valid email');
            isValid = false;
        }

             // Phone number validation
        if (!phone) {
            showFieldError('phone', 'Please enter phone number');
            isValid = false;
        } else if (!/^[6-9][0-9]{9}$/.test(phone)) {
            showFieldError('phone', 'Please enter a valid 10-digit phone number starting with 6-9');
            if (phoneValidationError) phoneValidationError.style.display = 'block';
            isValid = false;
        }

  

        // Password validation for new users
        if (!form.userId.value) {
            if (!password) {
                showFieldError('password', 'Please enter password');
                isValid = false;
            }
            if (!confirmPassword) {
                showFieldError('confirmPassword', 'Please confirm password');
                isValid = false;
            }
            if (password && confirmPassword && password !== confirmPassword) {
                showFieldError('confirmPassword', 'Passwords do not match');
                isValid = false;
            }
        }

        return isValid;
    }

    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-danger mt-1';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
        field.classList.add('is-invalid');
    }

    function isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    // Refresh table data
    function refreshTableFromServer() {
        fetch('/admin/users/data', {
            method: 'GET',
            headers: {
                'X-CSRF-Token': csrfToken
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }

                // Update users array
                users = data.users;

                // Update table
                refreshTable(users);

                // Update counts
                document.querySelector('.bg-primary h2').textContent = data.counts.doctors;
                document.querySelector('.bg-success h2').textContent = data.counts.receptionists;
                document.querySelector('.bg-info h2').textContent = data.counts.patients;
                document.querySelector('.bg-warning h2').textContent = data.counts.agents;
            })
            .catch(error => {
                console.error('Error refreshing table:', error);
                showAlert('Error refreshing data', 'danger');
            });
    }

    // Toggle password visibility
    function togglePasswordVisibility(fieldId) {
        const passwordField = document.getElementById(fieldId);
        const icon = passwordField.nextElementSibling.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Generate random password
    function generatePassword() {
        fetch('/admin/users/generate-password', {
            method: 'GET',
            headers: {
                'X-CSRF-Token': csrfToken
            }
        })
            .then(response => response.json())
            .then(data => {
                const passwordField = document.getElementById('password');
                const confirmPasswordField = document.getElementById('confirmPassword');
                
                passwordField.value = data.password;
                confirmPasswordField.value = data.password;
                
                // Show the generated password briefly
                passwordField.type = 'text';
                confirmPasswordField.type = 'text';
                
                setTimeout(() => {
                    passwordField.type = 'password';
                    confirmPasswordField.type = 'password';
                }, 2000);
            })
            .catch(error => {
                console.error('Error generating password:', error);
                showAlert('Error generating password', 'danger');
            });
    }

    // Show/hide fields based on user type
    document.getElementById('userType').addEventListener('change', function() {
        const userType = this.value;
        const doctorFields = document.querySelectorAll('.doctor-field');
        const agentFields = document.querySelectorAll('.agent-field');
        
        // Hide all special fields first
        doctorFields.forEach(field => field.style.display = 'none');
        agentFields.forEach(field => field.style.display = 'none');
        
        // Show relevant fields based on user type
        if (userType === 'Doctor') {
            doctorFields.forEach(field => field.style.display = 'block');
        } else if (userType === 'Pickup Agent') {
            agentFields.forEach(field => field.style.display = 'block');
        }
    });

    // Save user
    function saveUser() {
        if (!validateForm()) return;

        const saveButton = document.querySelector('#addUserModal .btn-primary');
        const loader = document.querySelector('#addUserModal .modal-footer .visually-hidden');
    // Show loader and disable button
    if (loader) loader.classList.remove('visually-hidden');
    if (saveButton) {
        saveButton.disabled = true;
        saveButton.textContent = 'Saving...';
    }

        const form = document.getElementById('userForm');
        const formData = new FormData(form);
        const userId = form.userId.value;
        const userType = form.userType.value;
        const userData = {
            id: userId || null,
            name: form.fullName.value,
            email: form.email.value,
            phone: form.phone.value,
            status: 'active',
            csrf_token: formData.get('csrf_token')
        };

        // Add role only for admin users
        if (userType !== 'Pickup Agent') {
            userData.role = userType;
            userData.userType = 'admin';
            
            // Add doctor-specific data if user is a doctor
            if (userType === 'Doctor') {
                userData.specialization = form.specialization.value;
                userData.licenseNumber = form.licenseNumber.value;
            }
        } else {
            userData.userType = 'agent';
            userData.vehicleNumber = form.vehicleNumber.value;
            userData.serviceArea = form.serviceArea.value;
        }

        // Only include password if it's a new user or if password was changed
        if (!userId || form.password.value) {
            userData.password = form.password.value;
        }

        fetch('/admin/users/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify(userData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                closeModal('addUserModal');
                resetForm();
                
                // Add new user to the array or update existing one
                if (userId) {
                    const index = users.findIndex(u => u.id === parseInt(userId));
                    if (index !== -1) {
                        users[index] = { ...users[index], ...data.user };
                    }
                } else {
                    users.unshift(data.user);
                }
                
                // Update the table
                refreshTable(users);
                
                // Update the counts
                updateUserCounts();
                
                // Refresh from server after a short delay
                setTimeout(() => {
                    refreshTableFromServer();
                }, 500);
            } else {
                throw new Error(data.error || 'Failed to save user');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert(error.message, 'danger');
        }).finally(() => {
        // Hide loader and re-enable button
        if (loader) loader.classList.add('visually-hidden');
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.textContent = 'Save User';
        }
    });
}

    // Update user counts in cards
    function updateUserCounts() {
        const counts = {
            doctors: 0,
            receptionists: 0,
            patients: 0,
            agents: 0
        };

        users.forEach(user => {
            if (user.role === 'Doctor' && user.status === 'active') counts.doctors++;
            else if (user.role === 'Receptionist' && user.status === 'active') counts.receptionists++;
            else if (user.role === 'Patient' && user.status === 'active') counts.patients++;
            else if (user.role === 'Pickup Agent' && (user.status === 'active' || user.status === 'Available')) counts.agents++;
        });

        document.querySelector('.bg-primary h2').textContent = counts.doctors;
        document.querySelector('.bg-success h2').textContent = counts.receptionists;
        document.querySelector('.bg-info h2').textContent = counts.patients;
        document.querySelector('.bg-warning h2').textContent = counts.agents;
    }

    // Toggle user status
    function toggleStatus(userId, currentStatus) {
        const newStatus = currentStatus === 'active' || currentStatus === 'Available' ? 'inactive' : 'active';
        
        fetch(`/admin/users/status/${userId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify({ status: newStatus, csrf_token: csrfToken })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Status updated successfully', 'success');
                
                // Update the user in the local array
                const userIndex = users.findIndex(u => u.id === userId);
                if (userIndex !== -1) {
                    users[userIndex].status = data.new_status;
                }
                
                // Update the table with the new status
                refreshTable(users);
                
                // Update the counts
                updateUserCounts();
                
                // Refresh from server after a short delay
                setTimeout(() => {
                    refreshTableFromServer();
                }, 500);
            } else {
                throw new Error(data.error || 'Failed to update status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert(error.message, 'danger');
        });
    }

    // Delete user
    function deleteUser(userId) {
        const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        modal.show();
        
        document.getElementById('confirmDeleteBtn').onclick = function() {
            fetch(`/admin/users/delete/${userId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-Token': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('User deleted successfully', 'success');
                    modal.hide();
                    
                    // Remove the deleted user from the users array
                    users = users.filter(user => user.id !== userId);
                    
                    // Update the table with the filtered users
                    refreshTable(users);
                    
                    // Update the user counts
                    updateUserCounts();
                    
                    // Refresh data from server to ensure consistency
                    setTimeout(() => {
                        refreshTableFromServer();
                    }, 500);
                } else {
                    throw new Error(data.error || 'Failed to delete user');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert(error.message, 'danger');
            });
        };
    }

    // Edit user
    function editUser(userId) {
        const user = users.find(u => u.id === userId);
        if (!user) return;

        document.getElementById('userId').value = user.id;
        document.getElementById('userType').value = user.role;
        document.getElementById('fullName').value = user.name;
        document.getElementById('email').value = user.email;
        document.getElementById('phone').value = user.phone;
        document.getElementById('password').value = '';
        document.getElementById('confirmPassword').value = '';

        // Show/hide password fields based on whether it's a new user
        const passwordFields = document.querySelectorAll('.password-field');
        passwordFields.forEach(field => field.style.display = 'none');

        const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
        modal.show();
    }

    // Reset form
    function resetForm() {
        const form = document.getElementById('userForm');
        form.reset();
        form.userId.value = '';
        document.querySelectorAll('.error-message').forEach(el => el.remove());
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.password-field').forEach(field => field.style.display = 'block');
    }

    // Filter users
    function filterUsers() {
        const typeFilter = document.getElementById('userTypeFilter').value;
        const searchTerm = document.getElementById('searchInput').value;

        // Show loading state
        const tbody = document.querySelector('table tbody');
        tbody.innerHTML = '<tr><td colspan="5" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading...</td></tr>';

        // Fetch filtered data from server
        fetch(`/admin/users/data?userType=${typeFilter}&search=${encodeURIComponent(searchTerm)}`, {
            method: 'GET',
            headers: {
                'X-CSRF-Token': csrfToken
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }

                // Update users array
                users = data.users;

                // Update table
                refreshTable(users);

                // Update counts
                document.querySelector('.bg-primary h2').textContent = data.counts.doctors;
                document.querySelector('.bg-success h2').textContent = data.counts.receptionists;
                document.querySelector('.bg-info h2').textContent = data.counts.patients;
                document.querySelector('.bg-warning h2').textContent = data.counts.agents;
            })
            .catch(error => {
                console.error('Error filtering users:', error);
                showAlert('Error filtering users', 'danger');
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">Error loading data</td></tr>';
            });
    }

    // Utility functions
    function getUserTypeBadgeColor(type) {
        const colors = {
            'Doctor': 'primary',
            'Receptionist': 'success',
            'Admin': 'info'
        };
        return colors[type] || 'secondary';
    }

    function getStatusBadgeColor(status) {
        const statusColors = {
            'active': 'success',
            'inactive': 'danger',
            'Available': 'success',
            'Busy': 'warning',
            'Inactive': 'danger'
        };
        return statusColors[status] || 'secondary';
    }

    function closeModal(modalId) {
        const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
        if (modal) modal.hide();
    }

    function refreshTable(users) {
        const tbody = document.querySelector('table tbody');
        if (!users || users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">No users found</td></tr>';
            return;
        }

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <span class="badge bg-secondary">
                        ${user.professional_id || 'N/A'}
                    </span>
                </td>
                <td>${user.name}</td>
                <td>
                    <span class="badge bg-${getUserTypeBadgeColor(user.role)}">
                        ${user.role}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${getStatusBadgeColor(user.status)}">
                        ${user.status}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editUser(${user.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="toggleStatus(${user.id}, '${user.status}')">
                        <i class="fas fa-power-off"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Add password field class
        document.querySelectorAll('#password, #confirmPassword').forEach(field => {
            field.classList.add('password-field');
        });

        // Initialize form reset on modal close
        const addUserModal = document.getElementById('addUserModal');
        addUserModal.addEventListener('hidden.bs.modal', resetForm);

        // Initial data load
        filterUsers();

        // Add event listeners for filters
        document.getElementById('searchInput').addEventListener('input', debounce(filterUsers, 500));
        document.getElementById('userTypeFilter').addEventListener('change', filterUsers);
    });

    // Debounce function to prevent too many API calls
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
</script>
{% endblock %}