{% extends "ADMIN/adminbase.html" %}

{% block title %}Analytics Dashboard - Admin Panel{% endblock %}

{% block page_title %}Analytics Dashboard{% endblock %}

{% block extra_css %}
<style>
    /* Professional Chart Container */
    .chart-container {
        position: relative;
        height: 350px;
        width: 100%;
        margin-bottom: 1rem;
        background: var(--white);
        border-radius: var(--card-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Professional Stat Cards - No Glossy Effects */
    .stat-card {
        border-radius: var(--card-radius);
        transition: var(--transition);
        height: 100%;
        border: 1px solid rgba(0, 0, 0, 0.05);
        overflow: hidden;
        position: relative;
        background: var(--white);
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-accent);
    }

    .stat-card .card-body {
        padding: 2rem;
        position: relative;
        z-index: 1;
        background: var(--white);
    }

    .stat-card h5 {
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-light);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-card h2 {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        font-family: 'Poppins', sans-serif;
        color: var(--text-dark);
    }

    .stat-card small {
        font-size: 0.8rem;
        color: var(--text-light);
        font-weight: 500;
    }

    /* Professional Color Scheme - No Gradients */
    .bg-primary {
        background: var(--deep-blue) !important;
    }

    .bg-success {
        background: var(--success) !important;
    }

    .bg-info {
        background: var(--bright-blue) !important;
    }

    .bg-warning {
        background: var(--primary-orange) !important;
    }

    @media (max-width: 768px) {
        .chart-container {
            height: 250px;
            padding: 0.75rem;
        }

        .stat-card .card-body {
            padding: 1.5rem;
            text-align: center;
        }

        .stat-card h2 {
            font-size: 2rem;
        }

        /* Stack charts vertically on tablets */
        .row.g-4 .col-md-8,
        .row.g-4 .col-md-6,
        .row.g-4 .col-md-4 {
            margin-bottom: 1.5rem;
        }

        /* Improve card spacing */
        .card {
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.1rem;
            text-align: center;
            margin-bottom: 1rem;
        }
    }

    @media (max-width: 480px) {
        .chart-container {
            height: 200px;
            padding: 0.5rem;
        }

        .stat-card .card-body {
            padding: 1rem;
            text-align: center;
        }

        .stat-card h2 {
            font-size: 1.75rem;
        }

        .stat-card h5 {
            font-size: 0.8rem;
        }

        /* Further optimize for mobile */
        .card {
            margin-bottom: 1rem;
            border-radius: 12px;
        }

        .card-body {
            padding: 1rem;
        }

        .card-title {
            font-size: 1rem;
            margin-bottom: 0.75rem;
        }

        /* Optimize chart legends for mobile */
        .chart-container canvas {
            max-height: 180px;
        }

        /* Improve row spacing */
        .row.g-4 {
            --bs-gutter-x: 0.75rem;
            --bs-gutter-y: 0.75rem;
        }

        /* Container padding */
        .container-fluid {
            padding: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Debug Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>Analytics Dashboard Status</h6>
                <p class="mb-2">Page loaded successfully. Checking data availability...</p>
                <div id="debug-info">
                    <small class="text-muted">Initializing...</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Overview -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8 col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-chart-line me-2"></i>Revenue Trend</h5>
                    <div class="chart-container">
                        <div class="chart-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading chart...</p>
                        </div>
                        <canvas id="revenueChart" style="display: none;"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-credit-card me-2"></i>Payment Methods</h5>
                    <div class="chart-container">
                        <canvas id="paymentMethodsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bookings and Users -->
    <div class="row g-4 mb-4">
        <div class="col-lg-6 col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-calendar-check me-2"></i>Daily Bookings Trend</h5>
                    <div class="chart-container">
                        <canvas id="bookingsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-users me-2"></i>User Growth</h5>
                    <div class="chart-container">
                        <canvas id="userGrowthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Categories and Performance -->
    <div class="row g-4 mb-4">
        <div class="col-lg-6 col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-vial me-2"></i>Test Categories Distribution</h5>
                    <div class="chart-container">
                        <canvas id="testCategoriesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-stopwatch me-2"></i>Test Performance Metrics</h5>
                    <div class="chart-container">
                        <canvas id="testPerformanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Distributions -->
    <div class="row g-4">
        <div class="col-lg-6 col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-clipboard-list me-2"></i>Booking Status Distribution</h5>
                    <div class="chart-container">
                        <canvas id="bookingStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-flask me-2"></i>Sample Collection Status</h5>
                    <div class="chart-container">
                        <canvas id="sampleCollectionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js" integrity="sha512-ElRFoEQdI5Ht6kZvyzXhYG9NqjtkmlkfYk0wr6wHxU9JEHakS7UJZNeml5ALk+8IKlU6jDgMabC3vkumRokgJA==" crossorigin="anonymous" referrerpolicy="no-referrer" onerror="loadFallbackChart()"></script>
<script>
    // Page load test
    console.log('Analytics page JavaScript loaded successfully');

    // Test if basic DOM elements are available
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM Content Loaded - Analytics page ready');
        const debugInfo = document.getElementById('debug-info');
        if (debugInfo) {
            debugInfo.innerHTML = '<span class="text-primary">🔄 Page loaded, checking Chart.js...</span>';
        }
    });
    // Suppress browser extension errors
    window.addEventListener('error', function(e) {
        // Suppress chrome-extension errors
        if (e.filename && e.filename.includes('chrome-extension://')) {
            e.preventDefault();
            return false;
        }
        // Suppress extension-related content.js errors
        if (e.message && e.message.includes('content.js')) {
            e.preventDefault();
            return false;
        }
    });

    // Suppress unhandled promise rejections from extensions
    window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && typeof e.reason === 'string' && e.reason.includes('chrome-extension://')) {
            e.preventDefault();
            return false;
        }
    });

    // Override console.error to filter extension errors
    const originalConsoleError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        // Don't log chrome extension errors
        if (message.includes('chrome-extension://') ||
            message.includes('content.js') ||
            message.includes('inpage.js')) {
            return;
        }
        originalConsoleError.apply(console, args);
    };

    // Fallback Chart.js loader
    function loadFallbackChart() {
        console.warn('Primary Chart.js CDN failed, trying fallback...');
        const fallbackScript = document.createElement('script');
        fallbackScript.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
        fallbackScript.crossOrigin = 'anonymous';
        fallbackScript.onerror = function() {
            console.error('All Chart.js CDNs failed to load.');
            showChartError();
        };
        fallbackScript.onload = function() {
            console.log('Fallback Chart.js loaded successfully');
            // Wait a bit for the script to fully initialize
            setTimeout(initializeCharts, 100);
        };
        document.head.appendChild(fallbackScript);
    }

    // Show error message when charts fail to load
    function showChartError() {
        document.querySelectorAll('.chart-container').forEach(container => {
            container.innerHTML = `
                <div class="alert alert-warning text-center p-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3 text-warning"></i>
                    <h6>Charts Unavailable</h6>
                    <p class="mb-2">Unable to load chart library. Please:</p>
                    <ul class="list-unstyled mb-3">
                        <li>• Check your internet connection</li>
                        <li>• Refresh the page</li>
                        <li>• Try again later</li>
                    </ul>
                    <button class="btn btn-sm btn-warning" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>Retry
                    </button>
                </div>
            `;
        });
    }

    // Initialize charts function
    function initializeCharts() {
        // Update debug info
        const debugInfo = document.getElementById('debug-info');

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is still not available after loading attempts.');
            debugInfo.innerHTML = '<span class="text-danger">❌ Chart.js library failed to load</span>';
            showChartError();
            return;
        }
        console.log('Chart.js loaded successfully, initializing charts...');
        debugInfo.innerHTML = '<span class="text-success">✅ Chart.js loaded successfully</span>';

        // Parse the JSON data from Flask (with fallback sample data for testing)
        let revenueData, testCategories, bookingStatus, paymentMethods, dailyBookings, userGrowth, sampleCollections, testPerformance;

        try {
            // Try to parse Flask data, fall back to sample data if parsing fails
            revenueData = {{ revenue_data|safe }} || [
                {month: 'Jan', total_revenue: 50000},
                {month: 'Feb', total_revenue: 65000},
                {month: 'Mar', total_revenue: 80000},
                {month: 'Apr', total_revenue: 75000},
                {month: 'May', total_revenue: 90000}
            ];

            debugInfo.innerHTML += '<br><span class="text-info">📊 Data loaded: ' + (Array.isArray(revenueData) ? revenueData.length : 0) + ' revenue records</span>';
        } catch (error) {
            console.error('Error parsing revenue data:', error);
            debugInfo.innerHTML += '<br><span class="text-warning">⚠️ Using fallback revenue data</span>';
            revenueData = [
                {month: 'Jan', total_revenue: 50000},
                {month: 'Feb', total_revenue: 65000},
                {month: 'Mar', total_revenue: 80000},
                {month: 'Apr', total_revenue: 75000},
                {month: 'May', total_revenue: 90000}
            ];
        }
        try {
            testCategories = {{ test_categories|safe }} || [
                {TestCategory: 'Blood Test', test_count: 45},
                {TestCategory: 'Urine Test', test_count: 30},
                {TestCategory: 'X-Ray', test_count: 25}
            ];
        } catch (error) {
            console.error('Error parsing test categories:', error);
            testCategories = [
                {TestCategory: 'Blood Test', test_count: 45},
                {TestCategory: 'Urine Test', test_count: 30},
                {TestCategory: 'X-Ray', test_count: 25}
            ];
        }

        try {
            bookingStatus = {{ booking_status|safe }} || [
                {booking_status: 'Confirmed', count: 120},
                {booking_status: 'Pending', count: 45},
                {booking_status: 'Cancelled', count: 15}
            ];
        } catch (error) {
            console.error('Error parsing booking status:', error);
            bookingStatus = [
                {booking_status: 'Confirmed', count: 120},
                {booking_status: 'Pending', count: 45},
                {booking_status: 'Cancelled', count: 15}
            ];
        }

        try {
            paymentMethods = {{ payment_methods|safe }} || [
                {payment_method: 'Online', total_amount: 150000},
                {payment_method: 'Cash', total_amount: 80000},
                {payment_method: 'Card', total_amount: 120000}
            ];
        } catch (error) {
            console.error('Error parsing payment methods:', error);
            paymentMethods = [
                {payment_method: 'Online', total_amount: 150000},
                {payment_method: 'Cash', total_amount: 80000},
                {payment_method: 'Card', total_amount: 120000}
            ];
        }

        try {
            dailyBookings = {{ daily_bookings|safe }} || [
                {date: '2024-01-01', booking_count: 12},
                {date: '2024-01-02', booking_count: 15},
                {date: '2024-01-03', booking_count: 18},
                {date: '2024-01-04', booking_count: 22},
                {date: '2024-01-05', booking_count: 19}
            ];
        } catch (error) {
            console.error('Error parsing daily bookings:', error);
            dailyBookings = [
                {date: '2024-01-01', booking_count: 12},
                {date: '2024-01-02', booking_count: 15},
                {date: '2024-01-03', booking_count: 18},
                {date: '2024-01-04', booking_count: 22},
                {date: '2024-01-05', booking_count: 19}
            ];
        }

        try {
            userGrowth = {{ user_growth|safe }} || [
                {month: 'Jan', new_users: 25},
                {month: 'Feb', new_users: 35},
                {month: 'Mar', new_users: 45},
                {month: 'Apr', new_users: 40},
                {month: 'May', new_users: 55}
            ];
        } catch (error) {
            console.error('Error parsing user growth:', error);
            userGrowth = [
                {month: 'Jan', new_users: 25},
                {month: 'Feb', new_users: 35},
                {month: 'Mar', new_users: 45},
                {month: 'Apr', new_users: 40},
                {month: 'May', new_users: 55}
            ];
        }

        try {
            sampleCollections = {{ sample_collections|safe }} || [
                {collection_status: 'Collected', count: 85},
                {collection_status: 'Pending', count: 25},
                {collection_status: 'Failed', count: 5}
            ];
        } catch (error) {
            console.error('Error parsing sample collections:', error);
            sampleCollections = [
                {collection_status: 'Collected', count: 85},
                {collection_status: 'Pending', count: 25},
                {collection_status: 'Failed', count: 5}
            ];
        }

        try {
            testPerformance = {{ test_performance|safe }} || [
                {TestCategory: 'Blood Test', avg_turnaround_time: 24},
                {TestCategory: 'Urine Test', avg_turnaround_time: 12},
                {TestCategory: 'X-Ray', avg_turnaround_time: 6}
            ];
        } catch (error) {
            console.error('Error parsing test performance:', error);
            testPerformance = [
                {TestCategory: 'Blood Test', avg_turnaround_time: 24},
                {TestCategory: 'Urine Test', avg_turnaround_time: 12},
                {TestCategory: 'X-Ray', avg_turnaround_time: 6}
            ];
        }

        try {
            // Update debug info
            debugInfo.innerHTML += '<br><span class="text-success">🎨 Initializing charts...</span>';

            // Hide loading indicators and show canvases
            document.querySelectorAll('.chart-loading').forEach(loader => {
                loader.style.display = 'none';
            });
            document.querySelectorAll('.chart-container canvas').forEach(canvas => {
                canvas.style.display = 'block';
            });

            // Chart.js configuration
            Chart.defaults.font.family = "'Inter', sans-serif";
            Chart.defaults.color = '#002f6c';
            Chart.defaults.plugins.legend.position = 'bottom';

            // Responsive configuration
            Chart.defaults.responsive = true;
            Chart.defaults.maintainAspectRatio = false;

    // Revenue Chart
    new Chart(document.getElementById('revenueChart'), {
        type: 'line',
        data: {
            labels: revenueData.map(d => d.month),
            datasets: [{
                label: 'Revenue',
                data: revenueData.map(d => d.total_revenue),
                borderColor: '#002f6c',
                backgroundColor: 'rgba(0, 47, 108, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Payment Methods Chart
    new Chart(document.getElementById('paymentMethodsChart'), {
        type: 'doughnut',
        data: {
            labels: paymentMethods.map(p => p.payment_method),
            datasets: [{
                data: paymentMethods.map(p => p.total_amount),
                backgroundColor: ['#002f6c', '#f47c20', '#506da7', '#8eb5c4', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Bookings Chart
    new Chart(document.getElementById('bookingsChart'), {
        type: 'bar',
        data: {
            labels: dailyBookings.map(b => b.date),
            datasets: [{
                label: 'Daily Bookings',
                data: dailyBookings.map(b => b.booking_count),
                backgroundColor: '#002f6c'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // User Growth Chart
    new Chart(document.getElementById('userGrowthChart'), {
        type: 'line',
        data: {
            labels: userGrowth.map(u => u.month),
            datasets: [{
                label: 'New Users',
                data: userGrowth.map(u => u.new_users),
                borderColor: '#f47c20',
                backgroundColor: 'rgba(244, 124, 32, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Test Categories Chart
    new Chart(document.getElementById('testCategoriesChart'), {
        type: 'pie',
        data: {
            labels: testCategories.map(t => t.TestCategory),
            datasets: [{
                data: testCategories.map(t => t.test_count),
                backgroundColor: [
                    '#002f6c', '#f47c20', '#506da7', '#8eb5c4', '#dc3545',
                    '#28a745', '#17a2b8', '#6c757d', '#343a40', '#007bff'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Test Performance Chart
    new Chart(document.getElementById('testPerformanceChart'), {
        type: 'bar',
        data: {
            labels: testPerformance.map(t => t.TestCategory),
            datasets: [{
                label: 'Average Turnaround Time (hours)',
                data: testPerformance.map(t => t.avg_turnaround_time),
                backgroundColor: '#002f6c'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Booking Status Chart
    new Chart(document.getElementById('bookingStatusChart'), {
        type: 'doughnut',
        data: {
            labels: bookingStatus.map(b => b.booking_status),
            datasets: [{
                data: bookingStatus.map(b => b.count),
                backgroundColor: ['#002f6c', '#f47c20', '#506da7', '#8eb5c4', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Sample Collection Chart
    new Chart(document.getElementById('sampleCollectionChart'), {
        type: 'pie',
        data: {
            labels: sampleCollections.map(s => s.collection_status),
            datasets: [{
                data: sampleCollections.map(s => s.count),
                backgroundColor: ['#002f6c', '#f47c20', '#506da7']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

            // Update debug info with success
            debugInfo.innerHTML += '<br><span class="text-success">✅ All charts initialized successfully!</span>';

            // Hide debug info after 5 seconds
            setTimeout(() => {
                const debugAlert = document.querySelector('.alert-info');
                if (debugAlert) {
                    debugAlert.style.transition = 'opacity 0.5s ease';
                    debugAlert.style.opacity = '0';
                    setTimeout(() => {
                        debugAlert.style.display = 'none';
                    }, 500);
                }
            }, 5000);

        } catch (error) {
            console.error('Error initializing charts:', error);
            debugInfo.innerHTML += '<br><span class="text-danger">❌ Error initializing charts: ' + error.message + '</span>';
            showChartError();
        }
    } // End of initializeCharts function

    // Check if Chart.js is already loaded, if so initialize immediately
    function checkAndInitialize() {
        if (typeof Chart !== 'undefined') {
            console.log('Chart.js detected, initializing charts...');
            initializeCharts();
        } else {
            console.log('Chart.js not yet available, waiting...');
            // Try again after a short delay
            setTimeout(checkAndInitialize, 500);
        }
    }

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkAndInitialize);
    } else {
        checkAndInitialize();
    }

    // Add mobile-specific chart interactions
    if (typeof Chart !== 'undefined' && window.innerWidth < 768) {
        // Disable hover animations on mobile for better performance
        Chart.defaults.interaction.intersect = false;
        Chart.defaults.animation.duration = 0;
    }

    // Handle window resize for better mobile experience
    window.addEventListener('resize', function() {
        if (typeof Chart !== 'undefined' && Chart.helpers) {
            Chart.helpers.each(Chart.instances, function(instance) {
                instance.resize();
            });
        }
    });
</script>
{% endblock %}