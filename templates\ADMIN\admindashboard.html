{% extends "ADMIN/adminbase.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block extra_css %}
<style>
    /* COMFORTABLE PROFESSIONAL DASHBOARD - EYE-FRIENDLY DESIGN */
    :root {
        /* Comfortable Eye-Friendly Color Palette */
        --dashboard-bg: #f6f8fa;
        --card-bg: #fafbfc;
        --secondary-bg: #f1f3f5;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --border-light: #e2e8f0;
        --border-medium: #d1d5db;

        /* Soft Professional Accent Colors - No Bright Colors */
        --accent-blue: #4299e1;
        --accent-green: #48bb78;
        --accent-purple: #805ad5;
        --accent-orange: #ed8936;
        --accent-red: #e53e3e;

        /* Very Subtle Background Colors - Almost Neutral */
        --bg-blue: #f7fafc;
        --bg-green: #f7fafc;
        --bg-purple: #f7fafc;
        --bg-orange: #f7fafc;

        /* Minimal Shadows - No Harsh Effects */
        --shadow-minimal: 0 1px 3px 0 rgb(0 0 0 / 0.05);
        --shadow-soft: 0 1px 2px 0 rgb(0 0 0 / 0.03);
        --shadow-none: none;
    }

    /* Comfortable Dashboard Layout */
    .dashboard-container {
        background: var(--dashboard-bg);
        min-height: 100vh;
        padding: 1.5rem;
    }

    /* Comfortable Stat Cards - No Disturbing Effects */
    .modern-stat-card {
        background: var(--card-bg);
        border: 1px solid var(--border-light);
        border-radius: 8px;
        padding: 1.5rem;
        transition: none;
        position: relative;
        overflow: hidden;
    }

    /* Remove hover effects - keep it static and comfortable */
    .modern-stat-card:hover {
        transform: none;
        box-shadow: var(--shadow-soft);
        border-color: var(--border-light);
    }

    .stat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .stat-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.025em;
        margin: 0;
    }

    .stat-icon {
        width: 36px;
        height: 36px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
        background: var(--secondary-bg);
        color: var(--text-secondary);
    }

    .stat-value {
        font-size: 1.875rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
        font-family: 'Inter', sans-serif;
        line-height: 1.2;
    }

    .stat-change {
        font-size: 0.8rem;
        font-weight: 400;
        margin-top: 0.5rem;
        color: var(--text-muted);
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    /* Remove color coding for changes - keep neutral */
    .stat-change.positive,
    .stat-change.negative {
        color: var(--text-muted);
    }

    /* Unified Color Variants - All Same Subtle Style */
    .stat-blue .stat-icon,
    .stat-green .stat-icon,
    .stat-purple .stat-icon,
    .stat-orange .stat-icon {
        background: var(--secondary-bg);
        color: var(--text-secondary);
    }

    /* Comfortable Dashboard Header */
    .dashboard-header {
        background: var(--card-bg);
        border: 1px solid var(--border-light);
        border-radius: 6px;
        padding: 1.25rem;
        margin-bottom: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .header-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .modern-btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 400;
        font-size: 0.875rem;
        border: 1px solid var(--border-light);
        cursor: pointer;
        transition: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        background: var(--card-bg);
        color: var(--text-primary);
    }

    .modern-btn-primary {
        background: var(--secondary-bg);
        color: var(--text-primary);
        border-color: var(--border-medium);
    }

    .modern-btn-primary:hover {
        background: var(--border-light);
        transform: none;
        box-shadow: none;
        color: var(--text-primary);
    }

    .modern-btn-secondary {
        background: var(--card-bg);
        color: var(--text-secondary);
        border: 1px solid var(--border-light);
    }

    .modern-btn-secondary:hover {
        background: var(--secondary-bg);
        border-color: var(--border-medium);
        color: var(--text-primary);
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 400;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        background: var(--secondary-bg);
        color: var(--text-secondary);
        border: 1px solid var(--border-light);
    }

    .status-badge.updated {
        background: var(--secondary-bg);
        color: var(--text-secondary);
    }

    /* Comfortable Table Design */
    .modern-table-container {
        background: var(--card-bg);
        border: 1px solid var(--border-light);
        border-radius: 6px;
        overflow: hidden;
    }

    .table-header {
        padding: 1.25rem;
        border-bottom: 1px solid var(--border-light);
        background: var(--secondary-bg);
    }

    .table-title {
        font-size: 1rem;
        font-weight: 500;
        color: var(--text-primary);
        margin: 0;
    }

    .modern-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .modern-table th {
        background: var(--secondary-bg);
        color: var(--text-secondary);
        font-weight: 400;
        font-size: 0.8rem;
        text-transform: none;
        letter-spacing: 0;
        padding: 0.875rem 1.25rem;
        border-bottom: 1px solid var(--border-light);
        text-align: left;
    }

    .modern-table td {
        padding: 1rem 1.25rem;
        border-bottom: 1px solid var(--border-light);
        color: var(--text-primary);
        font-size: 0.875rem;
        vertical-align: middle;
    }

    .modern-table tbody tr:hover {
        background: var(--secondary-bg);
    }

    .modern-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Neutral Status Badges - No Color Coding */
    .modern-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 400;
        text-transform: capitalize;
        background: var(--secondary-bg);
        color: var(--text-secondary);
        border: 1px solid var(--border-light);
    }

    .modern-badge.confirmed,
    .modern-badge.pending,
    .modern-badge.cancelled {
        background: var(--secondary-bg);
        color: var(--text-secondary);
        border: 1px solid var(--border-light);
    }

    /* Neutral Action Buttons */
    .action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 4px;
        border: 1px solid var(--border-light);
        font-size: 0.75rem;
        font-weight: 400;
        cursor: pointer;
        transition: none;
        margin-right: 0.5rem;
        background: var(--card-bg);
        color: var(--text-secondary);
    }

    .action-btn.edit,
    .action-btn.delete {
        background: var(--card-bg);
        color: var(--text-secondary);
        border: 1px solid var(--border-light);
    }

    .action-btn.edit:hover,
    .action-btn.delete:hover {
        background: var(--secondary-bg);
        color: var(--text-primary);
        border-color: var(--border-medium);
    }

    /* Empty State Styling */
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--text-muted);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.3;
        display: block;
    }

    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Improved Focus States */
    .modern-btn:focus,
    .action-btn:focus {
        outline: 2px solid var(--accent-blue);
        outline-offset: 2px;
    }

    /* Better Typography */
    .table-title {
        font-family: 'Inter', sans-serif;
        letter-spacing: -0.025em;
    }

    .stat-title {
        font-family: 'Inter', sans-serif;
    }

    .stat-value {
        letter-spacing: -0.02em;
    }

    /* Modern Mobile Responsiveness */
    @media (max-width: 1024px) {
        .dashboard-container {
            padding: 1rem;
        }

        .dashboard-header {
            padding: 1rem;
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .header-actions {
            justify-content: center;
        }
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 0.75rem;
        }

        .modern-stat-card {
            padding: 1rem;
        }

        .stat-value {
            font-size: 1.75rem;
        }

        .stat-icon {
            width: 36px;
            height: 36px;
            font-size: 1rem;
        }

        .modern-table th,
        .modern-table td {
            padding: 0.75rem 1rem;
            font-size: 0.8rem;
        }

        .modern-btn {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            margin-right: 0.25rem;
        }
    }

    @media (max-width: 480px) {
        .dashboard-container {
            padding: 0.5rem;
        }

        .dashboard-header {
            padding: 0.75rem;
        }

        .modern-stat-card {
            padding: 0.75rem;
        }

        .stat-header {
            margin-bottom: 0.75rem;
        }

        .stat-value {
            font-size: 1.5rem;
        }

        .stat-title {
            font-size: 0.75rem;
        }

        .modern-table th,
        .modern-table td {
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
        }

        /* Hide less important columns on mobile */
        .modern-table th:nth-child(n+4),
        .modern-table td:nth-child(n+4) {
            display: none;
        }

        .header-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .modern-btn {
            width: 100%;
            justify-content: center;
        }
    }

    /* Mobile-specific stat card improvements */
    @media (max-width: 576px) {
        .stat-card .card-body .d-flex {
            flex-direction: column;
            text-align: center;
            gap: 0.5rem;
            min-height: auto;
        }

        .stat-card .stat-icon {
            order: -1;
            margin-bottom: 0.5rem;
        }

        .stat-card .stat-icon i {
            font-size: 1.5rem !important;
        }
    }

    @media (max-width: 768px) {
        .stat-card .card-body {
            padding: 1.5rem;
            text-align: center;
        }

        .stat-card h2 {
            font-size: 2rem;
        }

        .stat-card h5 {
            font-size: 0.85rem;
        }

        .table-responsive {
            font-size: 0.85rem;
            margin: 0 -0.75rem;
        }

        .table th,
        .table td {
            padding: 0.75rem 0.5rem;
        }

        /* Hide less important columns on tablets */
        .table th:nth-child(5),
        .table td:nth-child(5) {
            display: none;
        }

        /* Improve button layout in table */
        .table .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            margin: 0.1rem;
        }

        /* Stats cards in 2x2 grid on tablets */
        .row.g-4 {
            --bs-gutter-x: 1rem;
            --bs-gutter-y: 1rem;
        }
    }

    @media (max-width: 480px) {
        .stat-card .card-body {
            padding: 1rem;
            text-align: center;
        }

        .stat-card h2 {
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }

        .stat-card h5 {
            font-size: 0.8rem;
            margin-bottom: 0.75rem;
        }

        /* Stack stats cards vertically on mobile */
        .row.g-4 .col-md-3 {
            margin-bottom: 1rem;
        }

        /* Simplify table for mobile */
        .table-responsive {
            margin: 0 -0.5rem;
            font-size: 0.75rem;
        }

        .table th,
        .table td {
            padding: 0.5rem 0.25rem;
            font-size: 0.75rem;
        }

        /* Hide even more columns on mobile */
        .table th:nth-child(n+4),
        .table td:nth-child(n+4) {
            display: none;
        }

        /* Stack action buttons vertically */
        .table .btn {
            display: block;
            width: 100%;
            margin: 0.1rem 0;
            padding: 0.25rem;
            font-size: 0.7rem;
        }

        /* Improve modal for mobile */
        .modal-dialog {
            margin: 0.5rem;
        }

        .modal-body .mb-3 {
            margin-bottom: 1rem !important;
        }

        .modal-footer .btn {
            width: 100%;
            margin: 0.25rem 0;
        }

        /* Header improvements */
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .d-flex.gap-2 {
            justify-content: center;
        }

        .d-flex.gap-2 .btn {
            flex: 1;
            max-width: 200px;
        }
    }

    /* Touch-friendly improvements for admin dashboard */
    @media (hover: none) and (pointer: coarse) {
        .stat-card {
            transition: none;
        }

        .stat-card:hover {
            transform: none;
        }

        .btn {
            min-height: 44px;
        }

        .table .btn {
            min-height: 36px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Modern Dashboard Header -->
    <div class="dashboard-header">
        <div class="header-actions">
            <button class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#createBookingModal">
                <i class="fas fa-plus"></i>
                <span>Add Booking</span>
            </button>
            <button class="modern-btn modern-btn-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i>
                <span>Refresh</span>
            </button>
        </div>
        <div class="d-flex gap-2 align-items-center">
            <span class="status-badge updated">
                <i class="fas fa-clock"></i>
                Last updated: <span id="lastUpdated"></span>
            </span>
        </div>
    </div>

    <!-- Modern Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6 col-sm-6 col-12">
            <div class="modern-stat-card stat-blue">
                <div class="stat-header">
                    <h6 class="stat-title">Total Bookings</h6>
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                </div>
                <div class="stat-value">{{ total_bookings }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12% from last month</span>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 col-12">
            <div class="modern-stat-card stat-green">
                <div class="stat-header">
                    <h6 class="stat-title">Revenue</h6>
                    <div class="stat-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                </div>
                <div class="stat-value">₹{{ revenue|round(2) }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8% from last month</span>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 col-12">
            <div class="modern-stat-card stat-purple">
                <div class="stat-header">
                    <h6 class="stat-title">Active Users</h6>
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">{{ active_users }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+5% from last month</span>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 col-12">
            <div class="modern-stat-card stat-orange">
                <div class="stat-header">
                    <h6 class="stat-title">Pending Bookings</h6>
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">{{ pending_bookings }}</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span>-3% from last month</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Recent Bookings Table -->
    <div class="modern-table-container">
        <div class="table-header">
            <h5 class="table-title">Recent Bookings</h5>
        </div>
        <div class="table-responsive">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Customer</th>
                        <th>Service</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="bookingsTableBody">
                    {% if recent_bookings %}
                        {% for booking in recent_bookings %}
                        <tr data-id="{{ booking.id }}">
                            <td><strong>#{{ booking.id }}</strong></td>
                            <td>{{ booking.customer }}</td>
                            <td>{{ booking.service }}</td>
                            <td>{{ booking.date }}</td>
                            <td>
                                <span class="modern-badge {{ booking.status }}">
                                    {{ booking.status|capitalize }}
                                </span>
                            </td>
                            <td>
                                <button class="action-btn edit" onclick="editBooking({{ booking.id }})" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete" onclick="deleteBooking({{ booking.id }})" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center" style="color: var(--text-muted); padding: 2rem;">
                                <i class="fas fa-inbox fa-2x mb-2" style="display: block; opacity: 0.3;"></i>
                                No recent bookings found
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Booking Modal -->
<div class="modal fade" id="createBookingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Booking</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createBookingForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <div class="mb-3">
                        <label class="form-label">Customer (Username)</label>
                        <input type="text" class="form-control" id="createCustomer" name="customer" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Service (Test Name)</label>
                        <input type="text" class="form-control" id="createService" name="service" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Booking Date</label>
                        <input type="date" class="form-control" id="createBookingDate" name="booking_date" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" id="createStatus" name="status" required>
                            <option value="pending">Pending</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveBooking()">Save Booking</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Booking Modal -->
<div class="modal fade" id="editBookingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Booking</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editBookingForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <input type="hidden" id="editBookingId" name="id">
                    <div class="mb-3">
                        <label class="form-label">Customer (Username)</label>
                        <input type="text" class="form-control" id="editCustomer" name="customer" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Service (Test Name)</label>
                        <input type="text" class="form-control" id="editService" name="service" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Booking Date</label>
                        <input type="date" class="form-control" id="editBookingDate" name="booking_date" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" id="editStatus" name="status" required>
                            <option value="pending">Pending</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateBooking()">Update Booking</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const bookings = {{ recent_bookings|tojson|safe }};
    const csrfToken = "{{ csrf_token }}";

    // Update timestamp
    function updateTimestamp() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: true,
            hour: '2-digit',
            minute: '2-digit'
        });
        const timestampElement = document.getElementById('lastUpdated');
        if (timestampElement) {
            timestampElement.textContent = timeString;
        }
    }

    // Initialize timestamp on page load
    document.addEventListener('DOMContentLoaded', function() {
        updateTimestamp();

        // Update timestamp every minute
        setInterval(updateTimestamp, 60000);

        // Add touch feedback for mobile
        if ('ontouchstart' in window) {
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });

                card.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });
        }
    });

    function saveBooking() {
        const form = document.getElementById('createBookingForm');
        const formData = new FormData(form);
        formData.append('csrf_token', csrfToken); // Ensure CSRF token is included

        fetch('/admin/bookings/create', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) throw new Error(data.error);
            alert('Booking created successfully!');
            location.reload();
        })
        .catch(error => alert('Error creating booking: ' + error.message));

        bootstrap.Modal.getInstance(document.getElementById('createBookingModal')).hide();
    }

    function editBooking(bookingId) {
        const booking = bookings.find(b => b.id == bookingId);
        if (!booking) return;

        document.getElementById('editBookingId').value = booking.id;
        document.getElementById('editCustomer').value = booking.customer;
        document.getElementById('editService').value = booking.service;
        document.getElementById('editBookingDate').value = booking.date;
        document.getElementById('editStatus').value = booking.status;

        const modal = new bootstrap.Modal(document.getElementById('editBookingModal'));
        modal.show();
    }

    function updateBooking() {
        const form = document.getElementById('editBookingForm');
        const formData = new FormData(form);
        formData.append('csrf_token', csrfToken); // Ensure CSRF token is included

        fetch('/admin/bookings/update', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) throw new Error(data.error);
            alert('Booking updated successfully!');
            location.reload();
        })
        .catch(error => alert('Error updating booking: ' + error.message));

        bootstrap.Modal.getInstance(document.getElementById('editBookingModal')).hide();
    }

    function deleteBooking(bookingId) {
        if (confirm('Are you sure you want to delete this booking?')) {
            const formData = new FormData();
            formData.append('id', bookingId);
            formData.append('csrf_token', csrfToken); // Include CSRF token

            fetch('/admin/bookings/delete', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) throw new Error(data.error);
                alert('Booking deleted successfully!');
                location.reload();
            })
            .catch(error => alert('Error deleting booking: ' + error.message));
        }
    }
</script>
{% endblock %}