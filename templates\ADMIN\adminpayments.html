{% extends "ADMIN/adminbase.html" %}

{% block title %}Payments Management - Admin Dashboard{% endblock %}

{% block page_title %}Payments Management{% endblock %}

{% block extra_css %}
<meta name="csrf-token" content="{{ csrf_token }}">
<!-- Chart.js CDN from allowed domain -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js" integrity="sha512-7U4rRB8aGAHGVad3u2jiC7GA5/1YhQcQjxKeaVms/bT66i3LVBMRcBI9KwABNWnxOSwulkuSXxZLGuyfvo7V1A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-adapter-date-fns/3.0.0/chartjs-adapter-date-fns.bundle.min.js" integrity="sha512-rwTlpHLluJlTF7GbYr2L6YBZL8KVhLGGX8wJyZXJTt8Iq6qlKKpJhQlJLwJhJlBJJsHhJhNhJGJJJjhJJJJjhQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<style>
    .stat-card {
        border-radius: 15px;
        transition: transform 0.3s;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .payment-filters {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        border: 1px solid rgba(244, 124, 32, 0.1);
        position: relative;
    }

    .payment-filters::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #f47c20, #002f6c);
        border-radius: 15px 15px 0 0;
    }

    .payment-filters .form-label {
        color: #002f6c;
        font-weight: 600;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .payment-filters .form-select {
        border: 2px solid #e6f7ff;
        border-radius: 8px;
        padding: 10px 15px;
        transition: all 0.3s ease;
        background-color: #fff;
    }

    .payment-filters .form-select:focus {
        border-color: #f47c20;
        box-shadow: 0 0 0 0.2rem rgba(244, 124, 32, 0.25);
        outline: none;
    }

    .payment-filters .btn-primary {
        background: linear-gradient(135deg, #f47c20 0%, #ff8c42 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(244, 124, 32, 0.3);
    }

    .payment-filters .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(244, 124, 32, 0.4);
        background: linear-gradient(135deg, #e66b1a 0%, #f47c20 100%);
    }

    /* Enhanced stat cards */
    .stat-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        position: relative;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color, #f47c20), var(--card-color-light, #ff8c42));
    }

    .stat-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-card .card-body {
        padding: 25px;
    }

    .stat-card h3 {
        font-weight: 700;
        margin-bottom: 5px;
        font-size: 2rem;
    }

    .stat-card p {
        margin-bottom: 0;
        font-weight: 500;
        opacity: 0.8;
    }

    /* Chart controls styling */
    .chart-controls {
        margin-bottom: 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
    }

    .chart-controls .btn {
        border-radius: 25px;
        padding: 8px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .chart-controls .btn.active {
        background: linear-gradient(135deg, #f47c20 0%, #ff8c42 100%);
        color: white;
        border-color: #f47c20;
        box-shadow: 0 4px 15px rgba(244, 124, 32, 0.3);
    }

    .chart-controls .btn:not(.active) {
        background: #e6f7ff;
        color: #002f6c;
        border-color: #e6f7ff;
    }

    .chart-controls .btn:not(.active):hover {
        background: #f47c20;
        color: white;
        border-color: #f47c20;
        transform: translateY(-2px);
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(244, 124, 32, 0.1);
        position: relative;
        overflow: hidden;
    }

    .chart-container:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.12);
        border-color: rgba(244, 124, 32, 0.2);
    }

    .chart-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #f47c20, #002f6c);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .chart-container:hover::before {
        opacity: 1;
    }

    .chart-title {
        color: #002f6c;
        font-weight: 600;
        margin-bottom: 20px;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        border-bottom: 2px solid #e6f7ff;
        padding-bottom: 10px;
    }

    .chart-title i {
        color: #f47c20;
        margin-right: 10px;
        font-size: 1.2rem;
    }

    .chart-canvas {
        max-height: 400px;
        transition: opacity 0.3s ease;
    }

    .loading-spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        text-align: center;
        background: rgba(255, 255, 255, 0.95);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .loading-spinner .spinner-border {
        color: #f47c20;
        width: 3rem;
        height: 3rem;
        margin-bottom: 10px;
    }

    .loading-spinner .loading-text {
        color: #002f6c;
        font-weight: 500;
        font-size: 0.9rem;
        margin-top: 10px;
    }

    /* Enhanced loading animation */
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .chart-container.loading {
        animation: pulse 2s infinite;
    }

    .chart-container.loading .chart-canvas {
        opacity: 0.3;
        pointer-events: none;
    }

    /* Error states */
    .chart-error {
        margin: 20px 0;
        border-left: 4px solid #f47c20;
        background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
        border-radius: 8px;
        animation: slideIn 0.3s ease-out;
    }

    .chart-error .alert {
        margin: 0;
        border: none;
        background: transparent;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* No data states */
    .no-data-message {
        background: linear-gradient(135deg, #e6f7ff 0%, #f8f9fa 100%);
        border-radius: 10px;
        margin: 20px 0;
        border: 2px dashed #dee2e6;
        animation: fadeIn 0.5s ease-out;
    }

    .no-data-message i {
        color: #6c757d;
        animation: bounce 2s infinite;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    /* Success states */
    .chart-success {
        border-left: 4px solid #28a745;
        background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        animation: slideIn 0.3s ease-out;
    }

    .chart-success i {
        color: #28a745;
        margin-right: 10px;
    }

    .no-data-message {
        background: linear-gradient(135deg, #e6f7ff 0%, #f8f9fa 100%);
        border-radius: 10px;
        margin: 20px 0;
        border: 2px dashed #dee2e6;
    }

    .no-data-message i {
        color: #6c757d;
    }

    .chart-error {
        margin: 20px 0;
        border-left: 4px solid #f47c20;
    }

    /* Enhanced responsive design */
    @media (max-width: 768px) {
        .chart-container {
            padding: 15px;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 1rem;
        }

        .chart-canvas {
            max-height: 300px;
        }
    }

    @media (max-width: 576px) {
        .chart-container {
            padding: 12px;
            border-radius: 10px;
        }

        .chart-title {
            font-size: 0.9rem;
            flex-direction: column;
            align-items: flex-start;
        }

        .chart-title i {
            margin-bottom: 5px;
        }
    }

    .chart-title {
        color: #002f6c;
        font-weight: bold;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }

    .chart-canvas {
        max-height: 400px;
    }

    .chart-controls {
        margin-bottom: 15px;
    }

    .chart-controls .btn {
        margin-right: 10px;
        margin-bottom: 5px;
    }

    .analytics-section {
        margin-top: 30px;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }

    /* CVBioLabs color scheme */
    :root {
        --cvbio-orange: #f47c20;
        --cvbio-dark-blue: #002f6c;
        --cvbio-light-blue: #e6f7ff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Payment Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card stat-card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Revenue</h5>
                    <h2 class="mb-0">₹{{ total_revenue|round(2) }}</h2>
                    <small class="text-white-50">This Month</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Pending Payments</h5>
                    <h2 class="mb-0">₹{{ pending_payments|round(2) }}</h2>
                    <small class="text-white-50">{{ pending_count }} transactions</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">Failed Payments</h5>
                    <h2 class="mb-0">₹{{ failed_payments|round(2) }}</h2>
                    <small class="text-white-50">{{ failed_count }} transactions</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Refunds</h5>
                    <h2 class="mb-0">₹{{ refunds|abs|round(2) }}</h2>
                    <small class="text-white-50">This Month</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Analytics Charts Section -->
    <div class="analytics-section">
        <h3 class="mb-4" style="color: var(--cvbio-dark-blue);">
            <i class="fas fa-chart-line me-2"></i>Payment Analytics
        </h3>

        <!-- Chart Controls -->
        <div class="chart-controls">
            <button class="btn btn-outline-primary" onclick="updateTrendsChart('daily')">Daily</button>
            <button class="btn btn-outline-primary active" onclick="updateTrendsChart('weekly')">Weekly</button>
            <button class="btn btn-outline-primary" onclick="updateTrendsChart('monthly')">Monthly</button>
            <button class="btn btn-outline-secondary ms-3" onclick="refreshAllCharts()">
                <i class="fas fa-sync-alt me-1"></i>Refresh Charts
            </button>
        </div>

        <!-- Charts Row 1: Trends and Status Distribution -->
        <div class="row g-4 mb-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="chart-title">
                        <i class="fas fa-chart-line me-2"></i>Payment Trends Over Time
                    </h5>
                    <div class="loading-spinner" id="trendsLoading">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="loading-text">Loading payment trends...</div>
                    </div>
                    <canvas id="trendsChart" class="chart-canvas"></canvas>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="chart-title">
                        <i class="fas fa-chart-pie me-2"></i>Payment Status Distribution
                    </h5>
                    <div class="loading-spinner" id="statusLoading">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="loading-text">Loading status distribution...</div>
                    </div>
                    <canvas id="statusChart" class="chart-canvas"></canvas>
                </div>
            </div>
        </div>

        <!-- Charts Row 2: Payment Methods and Revenue Analysis -->
        <div class="row g-4 mb-4">
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="chart-title">
                        <i class="fas fa-credit-card me-2"></i>Payment Methods Comparison
                    </h5>
                    <div class="loading-spinner" id="methodsLoading">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="loading-text">Loading payment methods...</div>
                    </div>
                    <canvas id="methodsChart" class="chart-canvas"></canvas>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="chart-title">
                        <i class="fas fa-chart-bar me-2"></i>Monthly Revenue Analysis
                    </h5>
                    <div class="loading-spinner" id="revenueLoading">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="loading-text">Loading revenue analysis...</div>
                    </div>
                    <canvas id="revenueChart" class="chart-canvas"></canvas>
                </div>
            </div>
        </div>

        <!-- Charts Row 3: Success Rates -->
        <div class="row g-4 mb-4">
            <div class="col-lg-12">
                <div class="chart-container">
                    <h5 class="chart-title">
                        <i class="fas fa-percentage me-2"></i>Payment Success Rates by Method
                    </h5>
                    <div class="loading-spinner" id="successLoading">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="loading-text">Loading success rates...</div>
                    </div>
                    <canvas id="successChart" class="chart-canvas"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Filters and Controls -->
    <div class="payment-filters">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Date Range</label>
                <select class="form-select" id="dateRange">
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly" selected>Monthly</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Payment Status</label>
                <select class="form-select" id="paymentStatus">
                    <option value="all">All Payments</option>
                    <option value="paid">Paid</option>
                    <option value="pending">Pending</option>
                    <option value="failed">Failed</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Payment Method</label>
                <select class="form-select" id="paymentMethod">
                    <option value="all">All Methods</option>
                    <option value="RazorPay">RazorPay</option>
                    <option value="UPI">UPI</option>
                    <option value="Card">Card</option>
                    <option value="Net Banking">Net Banking</option>
                    <option value="Cash">Cash</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label"> </label>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="filterPayments()">
                        <i class="fas fa-sync-alt me-2"></i>Generate Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Actions -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="btn-group">
                <button class="btn btn-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>Export to Excel
                </button>
                <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#generateLinkModal">
                    <i class="fas fa-link me-2"></i>Generate Payment Link
                </button>
                <button class="btn btn-warning" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>Print Report
                </button>
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Transaction ID</th>
                            <th>Customer</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Payment Method</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="paymentsTableBody">
                        {% for payment in recent_payments %}
                        <tr>
                            <td>{{ payment.transaction_id or 'N/A' }}</td>
                            <td>{{ payment.customer }}</td>
                            <td>₹{{ payment.amount|round(2) }}</td>
                            <td>{{ payment.date }}</td>
                            <td><span class="badge bg-{{ payment.status|get_badge_color }}">{{ payment.status|capitalize }}</span></td>
                            <td>{{ payment.payment_method }}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="viewDetails('{{ payment.transaction_id or 'N/A' }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-info" onclick="sendReceipt('{{ payment.transaction_id or 'N/A' }}')">
                                    <i class="fas fa-envelope"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Generate Payment Link Modal -->
<div class="modal fade" id="generateLinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Generate Payment Link</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="paymentLinkForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <div class="mb-3">
                        <label class="form-label">Customer Email</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Amount</label>
                        <input type="number" class="form-control" name="amount" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Expiry (Days)</label>
                        <input type="number" class="form-control" name="expiry" value="7">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Share Via</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="share_via" value="email" checked>
                            <label class="form-check-label">Email</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="share_via" value="whatsapp">
                            <label class="form-check-label">WhatsApp</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="generatePaymentLink()">Generate Link</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initial payments data from Flask
    const payments = {{ recent_payments|tojson|safe }};
    let filteredPayments = [...payments]; // Store filtered payments for export/print
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // CVBioLabs color scheme
    const cvbioColors = {
        orange: '#f47c20',
        darkBlue: '#002f6c',
        lightBlue: '#e6f7ff',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8'
    };

    // Chart instances
    let trendsChart = null;
    let statusChart = null;
    let methodsChart = null;
    let revenueChart = null;
    let successChart = null;

    // Helper functions for error handling and no data states
    function showNoDataMessage(container, message) {
        const canvas = container.querySelector('canvas');
        const existingMessage = container.querySelector('.no-data-message');

        if (canvas) canvas.style.display = 'none';
        if (existingMessage) existingMessage.remove();

        const noDataHtml = `
            <div class="no-data-message text-center py-4">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <p class="text-muted">${message}</p>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshAllCharts()">
                    <i class="fas fa-refresh me-1"></i>Refresh Data
                </button>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', noDataHtml);
    }

    function showChartError(container, message) {
        const canvas = container.querySelector('canvas');
        const existingError = container.querySelector('.chart-error');

        if (canvas) canvas.style.display = 'none';
        if (existingError) existingError.remove();

        const errorHtml = `
            <div class="chart-error alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Error:</strong> ${message}
                <button class="btn btn-sm btn-outline-primary ms-2" onclick="refreshAllCharts()">
                    <i class="fas fa-refresh me-1"></i>Retry
                </button>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', errorHtml);
    }

    function clearChartMessages(container) {
        const messages = container.querySelectorAll('.no-data-message, .chart-error');
        messages.forEach(msg => msg.remove());

        const canvas = container.querySelector('canvas');
        if (canvas) canvas.style.display = 'block';
    }

    // Chart initialization functions
    function initializeTrendsChart(data, period = 'weekly') {
        const ctx = document.getElementById('trendsChart').getContext('2d');

        if (trendsChart) {
            trendsChart.destroy();
        }

        const labels = data.map(item => {
            if (period === 'daily') {
                return new Date(item.date).toLocaleDateString();
            } else if (period === 'weekly') {
                // Convert YEARWEEK format (202425) to readable format
                const yearWeek = item.date.toString();
                const year = yearWeek.substring(0, 4);
                const week = yearWeek.substring(4);
                return `${year}-W${week}`;
            } else {
                return new Date(item.date + '-01').toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
            }
        });

        trendsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Total Revenue (₹)',
                    data: data.map(item => item.total_amount),
                    borderColor: cvbioColors.orange,
                    backgroundColor: cvbioColors.orange + '15',
                    borderWidth: 3,
                    pointBackgroundColor: cvbioColors.orange,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    tension: 0.4,
                    fill: true,
                    yAxisID: 'y'
                }, {
                    label: 'Successful Payments',
                    data: data.map(item => item.successful_payments),
                    borderColor: cvbioColors.success,
                    backgroundColor: cvbioColors.success + '15',
                    borderWidth: 3,
                    pointBackgroundColor: cvbioColors.success,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    tension: 0.4,
                    fill: false,
                    yAxisID: 'y1'
                }, {
                    label: 'Failed Payments',
                    data: data.map(item => item.failed_payments),
                    borderColor: cvbioColors.danger,
                    backgroundColor: cvbioColors.danger + '15',
                    borderWidth: 2,
                    pointBackgroundColor: cvbioColors.danger,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.4,
                    fill: false,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: period.charAt(0).toUpperCase() + period.slice(1) + ' Period',
                            color: '#002f6c',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 47, 108, 0.1)',
                            borderColor: 'rgba(0, 47, 108, 0.2)'
                        },
                        ticks: {
                            color: '#6c757d',
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Revenue (₹)',
                            color: '#f47c20',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(244, 124, 32, 0.1)',
                            borderColor: 'rgba(244, 124, 32, 0.2)'
                        },
                        ticks: {
                            color: '#6c757d',
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Transaction Count',
                            color: '#002f6c',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            color: '#6c757d',
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            color: '#002f6c',
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 47, 108, 0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#f47c20',
                        borderWidth: 2,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString();
                                }
                                return context.dataset.label + ': ' + context.parsed.y;
                            }
                        }
                    }
                }
            }
        });
    }

    function initializeStatusChart(data) {
        const ctx = document.getElementById('statusChart').getContext('2d');

        if (statusChart) {
            statusChart.destroy();
        }

        const colors = data.map(item => {
            switch(item.status.toLowerCase()) {
                case 'paid': return cvbioColors.success;
                case 'pending': return cvbioColors.warning;
                case 'failed': return cvbioColors.danger;
                default: return cvbioColors.info;
            }
        });

        statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1)),
                datasets: [{
                    data: data.map(item => item.count),
                    backgroundColor: colors,
                    borderWidth: 4,
                    borderColor: '#fff',
                    hoverBorderWidth: 6,
                    hoverBorderColor: '#f47c20',
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            color: '#002f6c',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    return data.labels.map((label, i) => {
                                        const value = data.datasets[0].data[i];
                                        const percentage = ((value * 100) / total).toFixed(1);
                                        return {
                                            text: `${label}: ${value} (${percentage}%)`,
                                            fillStyle: data.datasets[0].backgroundColor[i],
                                            strokeStyle: data.datasets[0].borderColor,
                                            lineWidth: data.datasets[0].borderWidth,
                                            pointStyle: 'circle',
                                            hidden: false,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 47, 108, 0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#f47c20',
                        borderWidth: 2,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed * 100) / total).toFixed(1);
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    function initializeMethodsChart(data) {
        const ctx = document.getElementById('methodsChart').getContext('2d');

        if (methodsChart) {
            methodsChart.destroy();
        }

        methodsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map(item => item.method),
                datasets: [{
                    label: 'Total Amount (₹)',
                    data: data.map(item => item.total_amount),
                    backgroundColor: cvbioColors.darkBlue,
                    borderColor: cvbioColors.darkBlue,
                    borderWidth: 1
                }, {
                    label: 'Transaction Count',
                    data: data.map(item => item.transaction_count),
                    backgroundColor: cvbioColors.orange,
                    borderColor: cvbioColors.orange,
                    borderWidth: 1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Payment Methods'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Amount (₹)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Transaction Count'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString();
                                }
                                return context.dataset.label + ': ' + context.parsed.y;
                            }
                        }
                    }
                }
            }
        });
    }

    function initializeRevenueChart(data) {
        const ctx = document.getElementById('revenueChart').getContext('2d');

        if (revenueChart) {
            revenueChart.destroy();
        }

        const labels = data.map(item => {
            return new Date(item.month + '-01').toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
        });

        revenueChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Successful Revenue (₹)',
                    data: data.map(item => item.revenue),
                    backgroundColor: cvbioColors.success,
                    borderColor: cvbioColors.success,
                    borderWidth: 1
                }, {
                    label: 'Failed Revenue (₹)',
                    data: data.map(item => item.failed_revenue),
                    backgroundColor: cvbioColors.danger,
                    borderColor: cvbioColors.danger,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Revenue (₹)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    function initializeSuccessChart(data) {
        const ctx = document.getElementById('successChart').getContext('2d');

        if (successChart) {
            successChart.destroy();
        }

        successChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map(item => item.method),
                datasets: [{
                    label: 'Success Rate (%)',
                    data: data.map(item => item.success_rate),
                    backgroundColor: data.map(item => {
                        if (item.success_rate >= 90) return cvbioColors.success;
                        if (item.success_rate >= 70) return cvbioColors.warning;
                        return cvbioColors.danger;
                    }),
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Success Rate (%)'
                        },
                        min: 0,
                        max: 100
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Payment Methods'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const item = data[context.dataIndex];
                                return [
                                    `Success Rate: ${context.parsed.x.toFixed(1)}%`,
                                    `Successful: ${item.successful_payments}`,
                                    `Failed: ${item.failed_payments}`,
                                    `Total Attempts: ${item.total_attempts}`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }

    // Enhanced data fetching functions with better error handling
    async function fetchTrendsData(period = 'weekly') {
        const loadingElement = document.getElementById('trendsLoading');
        const chartContainer = document.querySelector('#trendsChart').closest('.chart-container');

        try {
            if (loadingElement) loadingElement.style.display = 'block';

            console.log(`Fetching trends data for period: ${period}`);
            console.log(`CSRF Token: ${csrfToken ? 'Present' : 'Missing'}`);

            const response = await fetch(`/admin/api/payments/analytics/trends?period=${period}`, {
                method: 'GET',
                headers: {
                    'X-CSRF-Token': csrfToken,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            });

            if (loadingElement) loadingElement.style.display = 'none';

            console.log(`Response status: ${response.status}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`API Error Response: ${errorText}`);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            const result = await response.json();
            console.log('Trends data received:', result);

            if (result.success && result.data) {
                if (result.data.length === 0) {
                    showNoDataMessage(chartContainer, 'No payment trends data available for the selected period.');
                } else {
                    initializeTrendsChart(result.data, period);
                }
            } else {
                throw new Error(result.error || 'Failed to fetch trends data');
            }
        } catch (error) {
            if (loadingElement) loadingElement.style.display = 'none';
            console.error('Error fetching trends data:', error);
            showChartError(chartContainer, `Failed to load payment trends: ${error.message}`);
        }
    }

    async function fetchStatusData() {
        const loadingElement = document.getElementById('statusLoading');
        const chartContainer = document.querySelector('#statusChart').closest('.chart-container');

        try {
            if (loadingElement) loadingElement.style.display = 'block';

            const response = await fetch('/admin/api/payments/analytics/status-distribution', {
                headers: {
                    'X-CSRF-Token': csrfToken,
                    'Content-Type': 'application/json'
                }
            });

            if (loadingElement) loadingElement.style.display = 'none';

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                if (result.data.length === 0) {
                    showNoDataMessage(chartContainer, 'No payment status data available.');
                } else {
                    clearChartMessages(chartContainer);
                    initializeStatusChart(result.data);
                }
            } else {
                throw new Error(result.error || 'Failed to fetch status data');
            }
        } catch (error) {
            if (loadingElement) loadingElement.style.display = 'none';
            console.error('Error fetching status data:', error);
            showChartError(chartContainer, `Failed to load payment status: ${error.message}`);
        }
    }

    async function fetchMethodsData() {
        const loadingElement = document.getElementById('methodsLoading');
        const chartContainer = document.querySelector('#methodsChart').closest('.chart-container');

        try {
            if (loadingElement) loadingElement.style.display = 'block';

            const response = await fetch('/admin/api/payments/analytics/methods', {
                headers: {
                    'X-CSRF-Token': csrfToken,
                    'Content-Type': 'application/json'
                }
            });

            if (loadingElement) loadingElement.style.display = 'none';

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                if (result.data.length === 0) {
                    showNoDataMessage(chartContainer, 'No payment methods data available.');
                } else {
                    clearChartMessages(chartContainer);
                    initializeMethodsChart(result.data);
                }
            } else {
                throw new Error(result.error || 'Failed to fetch methods data');
            }
        } catch (error) {
            if (loadingElement) loadingElement.style.display = 'none';
            console.error('Error fetching methods data:', error);
            showChartError(chartContainer, `Failed to load payment methods: ${error.message}`);
        }
    }

    async function fetchRevenueData() {
        const loadingElement = document.getElementById('revenueLoading');
        const chartContainer = document.querySelector('#revenueChart').closest('.chart-container');

        try {
            if (loadingElement) loadingElement.style.display = 'block';

            const response = await fetch('/admin/api/payments/analytics/revenue', {
                headers: {
                    'X-CSRF-Token': csrfToken,
                    'Content-Type': 'application/json'
                }
            });

            if (loadingElement) loadingElement.style.display = 'none';

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                if (result.data.length === 0) {
                    showNoDataMessage(chartContainer, 'No revenue data available.');
                } else {
                    clearChartMessages(chartContainer);
                    initializeRevenueChart(result.data);
                }
            } else {
                throw new Error(result.error || 'Failed to fetch revenue data');
            }
        } catch (error) {
            if (loadingElement) loadingElement.style.display = 'none';
            console.error('Error fetching revenue data:', error);
            showChartError(chartContainer, `Failed to load revenue data: ${error.message}`);
        }
    }

    async function fetchSuccessRatesData() {
        const loadingElement = document.getElementById('successLoading');
        const chartContainer = document.querySelector('#successChart').closest('.chart-container');

        try {
            if (loadingElement) loadingElement.style.display = 'block';

            console.log('Fetching success rates data...');
            console.log(`CSRF Token: ${csrfToken ? 'Present' : 'Missing'}`);

            const response = await fetch('/admin/api/payments/analytics/success-rates', {
                method: 'GET',
                headers: {
                    'X-CSRF-Token': csrfToken,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            });

            if (loadingElement) loadingElement.style.display = 'none';

            console.log(`Success rates response status: ${response.status}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Success rates API Error Response: ${errorText}`);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            const result = await response.json();
            console.log('Success rates data received:', result);

            if (result.success && result.data) {
                if (result.data.length === 0) {
                    showNoDataMessage(chartContainer, 'No success rates data available.');
                } else {
                    clearChartMessages(chartContainer);
                    initializeSuccessChart(result.data);
                }
            } else {
                throw new Error(result.error || 'Failed to fetch success rates data');
            }
        } catch (error) {
            if (loadingElement) loadingElement.style.display = 'none';
            console.error('Error fetching success rates data:', error);
            showChartError(chartContainer, `Failed to load success rates: ${error.message}`);
        }
    }

    // Chart control functions
    function updateTrendsChart(period) {
        // Update active button
        document.querySelectorAll('.chart-controls .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');

        fetchTrendsData(period);
    }

    function refreshAllCharts() {
        const currentPeriod = document.querySelector('.chart-controls .btn.active').textContent.toLowerCase();
        fetchTrendsData(currentPeriod);
        fetchStatusData();
        fetchMethodsData();
        fetchRevenueData();
        fetchSuccessRatesData();
    }

    // Initialize all charts on page load
    function initializeAllCharts() {
        fetchTrendsData('weekly');
        fetchStatusData();
        fetchMethodsData();
        fetchRevenueData();
        fetchSuccessRatesData();
    }

    // Chart export functions
    function exportChartAsImage(chartInstance, filename) {
        if (chartInstance) {
            const url = chartInstance.toBase64Image();
            const a = document.createElement('a');
            a.href = url;
            a.download = filename + '.png';
            document.body.appendChild(a);
            a.click();
            a.remove();
        }
    }

    function exportAllCharts() {
        if (trendsChart) exportChartAsImage(trendsChart, 'payment-trends');
        if (statusChart) exportChartAsImage(statusChart, 'payment-status-distribution');
        if (methodsChart) exportChartAsImage(methodsChart, 'payment-methods-comparison');
        if (revenueChart) exportChartAsImage(revenueChart, 'monthly-revenue-analysis');
        if (successChart) exportChartAsImage(successChart, 'payment-success-rates');
    }

    // Add export button functionality
    function addChartExportButtons() {
        const exportButton = document.createElement('button');
        exportButton.className = 'btn btn-outline-info ms-2';
        exportButton.innerHTML = '<i class="fas fa-download me-1"></i>Export Charts';
        exportButton.onclick = exportAllCharts;

        const chartControls = document.querySelector('.chart-controls');
        if (chartControls) {
            chartControls.appendChild(exportButton);
        }
    }

    // Filter payments based on user input
    function filterPayments() {
        const dateRange = document.getElementById('dateRange').value;
        const statusFilter = document.getElementById('paymentStatus').value;
        const methodFilter = document.getElementById('paymentMethod').value;

        filteredPayments = payments.filter(payment => {
            const date = new Date(payment.date);
            const now = new Date();
            let matchesDate = true;

            if (dateRange === 'daily') {
                matchesDate = date.toDateString() === now.toDateString();
            } else if (dateRange === 'weekly') {
                const weekAgo = new Date(now.setDate(now.getDate() - 7));
                matchesDate = date >= weekAgo;
            } else if (dateRange === 'monthly') {
                const monthAgo = new Date(now.setMonth(now.getMonth() - 1));
                matchesDate = date >= monthAgo;
            }

            const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
            const matchesMethod = methodFilter === 'all' || payment.payment_method === methodFilter;

            return matchesDate && matchesStatus && matchesMethod;
        });

        updatePaymentsTable(filteredPayments);
        alert('Filtered report with: ' + dateRange + ', ' + statusFilter + ', ' + methodFilter);
    }

    // Update the payments table with filtered results
    function updatePaymentsTable(filteredPayments) {
        const tbody = document.getElementById('paymentsTableBody');
        tbody.innerHTML = ''; // Clear existing rows

        filteredPayments.forEach(payment => {
            const row = `
                <tr>
                    <td>${payment.transaction_id || 'N/A'}</td>
                    <td>${payment.customer}</td>
                    <td>₹${parseFloat(payment.amount).toFixed(2)}</td>
                    <td>${payment.date}</td>
                    <td><span class="badge bg-${payment.status === 'paid' ? 'success' : (payment.status === 'pending' ? 'warning' : 'danger')}">${payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}</span></td>
                    <td>${payment.payment_method}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="viewDetails('${payment.transaction_id || 'N/A'}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="sendReceipt('${payment.transaction_id || 'N/A'}')">
                            <i class="fas fa-envelope"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });

        if (filteredPayments.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">No payments found matching the selected criteria</td>
                </tr>
            `;
        }
    }

    // Export to Excel
    function exportToExcel() {
        fetch('/admin/payments/export_excel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify({ payments: filteredPayments, csrf_token: csrfToken })
        })
        .then(response => {
            if (!response.ok) throw new Error('Failed to export Excel');
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'payments_report.xlsx';
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        })
        .catch(error => alert('Error exporting to Excel: ' + error.message));
    }

    // Print Report (Export to PDF)
    function printReport() {
        fetch('/admin/payments/print_report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify({ payments: filteredPayments, csrf_token: csrfToken })
        })
        .then(response => {
            if (!response.ok) throw new Error('Failed to generate PDF');
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'payments_report.pdf';
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        })
        .catch(error => alert('Error generating PDF report: ' + error.message));
    }

    // View transaction details
    function viewDetails(transactionId) {
        alert('Viewing details for transaction: ' + transactionId);
    }

    // Send receipt
    function sendReceipt(transactionId) {
        alert('Sending receipt for transaction: ' + transactionId);
    }

    // Generate payment link
    function generatePaymentLink() {
        const form = document.getElementById('paymentLinkForm');
        const formData = new FormData(form);
        const data = {
            email: formData.get('email'),
            amount: formData.get('amount'),
            expiry: formData.get('expiry'),
            share_via: formData.getAll('share_via'),
            csrf_token: formData.get('csrf_token')
        };

        if (data.email && data.amount) {
            fetch('/admin/payments/generate_link', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': csrfToken
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Payment link generated successfully for ' + data.email + ' with amount ₹' + data.amount);
                    bootstrap.Modal.getInstance(document.getElementById('generateLinkModal')).hide();
                    form.reset();
                } else {
                    throw new Error(data.error || 'Failed to generate payment link');
                }
            })
            .catch(error => alert('Error generating payment link: ' + error.message));
        } else {
            alert('Please fill in all required fields.');
        }
    }

    // Chart initialization with error handling
    function initializeChartsWithErrorHandling() {
        console.log('Initializing payment analytics charts...');

        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded');
            showChartError('Chart.js library failed to load. Please refresh the page.');
            return;
        }

        // Check if all required canvas elements exist
        const requiredCanvases = ['trendsChart', 'statusChart', 'methodsChart', 'revenueChart', 'successChart'];
        const missingCanvases = requiredCanvases.filter(id => !document.getElementById(id));

        if (missingCanvases.length > 0) {
            console.error('Missing canvas elements:', missingCanvases);
            showChartError(`Missing chart containers: ${missingCanvases.join(', ')}`);
            return;
        }

        // Initialize charts with error handling
        try {
            initializeAllCharts();
            console.log('Charts initialized successfully');
        } catch (error) {
            console.error('Error initializing charts:', error);
            showChartError('Failed to initialize charts: ' + error.message);
        }
    }

    // Show chart error message
    function showChartError(message) {
        const errorHtml = `
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Chart Loading Issue:</strong> ${message}
                <button class="btn btn-sm btn-outline-primary ms-2" onclick="location.reload()">
                    <i class="fas fa-refresh me-1"></i>Refresh Page
                </button>
            </div>
        `;

        // Show error in each chart container
        const chartContainers = document.querySelectorAll('.chart-container');
        chartContainers.forEach(container => {
            const canvas = container.querySelector('canvas');
            if (canvas) {
                canvas.style.display = 'none';
                const existingError = container.querySelector('.alert');
                if (!existingError) {
                    canvas.insertAdjacentHTML('afterend', errorHtml);
                }
            }
        });
    }

    // Enhanced chart initialization with retry mechanism and fallback
    function initializeChartsWithRetry(retryCount = 0) {
        const maxRetries = 3;

        if (typeof Chart === 'undefined' && retryCount < maxRetries) {
            console.log(`Chart.js not ready, retrying... (${retryCount + 1}/${maxRetries})`);

            // Try loading Chart.js from alternative CDN if first attempt fails
            if (retryCount === 1) {
                console.log('Trying alternative Chart.js CDN...');
                loadChartJSFallback();
            }

            setTimeout(() => initializeChartsWithRetry(retryCount + 1), 1500);
            return;
        }

        if (typeof Chart === 'undefined') {
            console.error('Chart.js failed to load after multiple attempts');
            showChartError('Chart.js library failed to load. Please check your internet connection and refresh the page.');
            return;
        }

        initializeChartsWithErrorHandling();
    }

    // Fallback Chart.js loader
    function loadChartJSFallback() {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js';
        script.onload = function() {
            console.log('Fallback Chart.js loaded successfully');
        };
        script.onerror = function() {
            console.error('Fallback Chart.js also failed to load');
        };
        document.head.appendChild(script);
    }

    // Debug function to test authentication
    async function testAuthentication() {
        try {
            const response = await fetch('/admin/dashboard', {
                method: 'GET',
                credentials: 'same-origin'
            });
            console.log(`Authentication test - Status: ${response.status}`);
            if (response.status === 200) {
                console.log('✅ User is authenticated');
                return true;
            } else if (response.status === 302 || response.status === 401) {
                console.log('❌ User is not authenticated');
                return false;
            }
        } catch (error) {
            console.error('Authentication test failed:', error);
            return false;
        }
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Payment management page loaded');

        // Test authentication first
        testAuthentication().then(isAuthenticated => {
            if (!isAuthenticated) {
                console.warn('User may not be authenticated - this could cause API failures');
            }
        });

        // Initialize payments table
        updatePaymentsTable(payments);

        // Add chart export button
        addChartExportButtons();

        // Initialize charts with retry mechanism
        initializeChartsWithRetry();

        // Add event listeners for filters
        const dateRange = document.getElementById('dateRange');
        const paymentStatus = document.getElementById('paymentStatus');
        const paymentMethod = document.getElementById('paymentMethod');

        if (dateRange) dateRange.addEventListener('change', filterPayments);
        if (paymentStatus) paymentStatus.addEventListener('change', filterPayments);
        if (paymentMethod) paymentMethod.addEventListener('change', filterPayments);
    });
</script>
{% endblock %}