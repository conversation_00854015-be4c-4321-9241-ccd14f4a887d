-- CVBioLabs Database Table Creation Script
-- Run this script to create all required tables

USE cvbiolabs;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status TINYINT(1) DEFAULT 1,
    otp_verified TINYINT(1) DEFAULT 0
);

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    first_name <PERSON><PERSON><PERSON><PERSON>(60),
    last_name VARCHAR(60),
    phone VARCHAR(20),
    date_of_birth DATE,
    gender VARCHAR(10),
    address TEXT,
    FOREI<PERSON><PERSON>EY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    professional_id VARCHAR(10) UNIQUE,
    name VARCHAR(100),
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255),
    role ENUM('Admin', 'Receptionist', 'Doctor') NOT NULL DEFAULT 'Admin',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    phone VARCHAR(20),
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active'
);

-- Create doctors table
CREATE TABLE IF NOT EXISTS doctors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    professional_id VARCHAR(10) UNIQUE,
    name VARCHAR(100) NOT NULL,
    specialization VARCHAR(100),
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255),
    licence_number VARCHAR(50),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create testdetails table
CREATE TABLE IF NOT EXISTS testdetails (
    SrNo INT PRIMARY KEY,
    TestName VARCHAR(255),
    TestID BIGINT,
    TestCode VARCHAR(255),
    TestAmount DECIMAL(10, 2),
    OutsourceAmount DECIMAL(10, 2),
    OutsourceCenter VARCHAR(255),
    SampleType VARCHAR(255),
    TestCategory VARCHAR(255),
    DepartmentName VARCHAR(255),
    Accreditation VARCHAR(255),
    IntegrationCode VARCHAR(255),
    ShortText VARCHAR(255),
    CAPTest VARCHAR(5),
    TargetTAT VARCHAR(255),
    VerificationStatus VARCHAR(255),
    TargetTATHHMM VARCHAR(255),
    active BOOLEAN DEFAULT TRUE
);

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    test_id INT NOT NULL,
    booking_date DATE NOT NULL,
    appointment_time TIME NOT NULL DEFAULT '00:00:00',
    booking_status ENUM('pending', 'confirmed', 'cancelled', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    barcode VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    address_line1 VARCHAR(255) NOT NULL,
    address_line2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) DEFAULT 'India',
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    payment_method ENUM('RazorPay', 'UPI', 'Card', 'Net Banking', 'Cash') NOT NULL,
    transaction_id VARCHAR(100) UNIQUE,
    payment_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    payment_status VARCHAR(50) NOT NULL,
    refund_amount DECIMAL(10, 2) DEFAULT 0,
    refund_date TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

-- Create pickup_agents table
CREATE TABLE IF NOT EXISTS pickup_agents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    professional_id VARCHAR(10) UNIQUE,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(15) NOT NULL UNIQUE,
    status ENUM('Available', 'Busy', 'Inactive') NOT NULL DEFAULT 'Available',
    vehicle_number VARCHAR(20),
    service_area VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255)
);

-- Create reports table
CREATE TABLE IF NOT EXISTS reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    patient_id INT NOT NULL,
    test_id INT NOT NULL,
    doctor_id INT,
    assigned_doctor_id INT,
    assign_status ENUM('Not Assigned', 'Assigned') NOT NULL DEFAULT 'Not Assigned',
    report_url VARCHAR(255) NOT NULL,
    report_status ENUM('Pending', 'Verified', 'Completed') NOT NULL DEFAULT 'Pending',
    doctor_review TEXT,
    comments TEXT,
    report_type ENUM('standard', 'detailed', 'summary') DEFAULT 'standard',
    report_format ENUM('pdf', 'excel', 'csv') DEFAULT 'pdf',
    file_path VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_doctor_id) REFERENCES doctors(id),
    FOREIGN KEY (patient_id) REFERENCES users(id),
    FOREIGN KEY (doctor_id) REFERENCES doctors(id)
);

-- Create patient_report table
CREATE TABLE IF NOT EXISTS patient_report (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    booking_id INT NOT NULL,
    patient_id INT NOT NULL,
    test_name VARCHAR(255) NOT NULL,
    barcode VARCHAR(255) NOT NULL,
    report_url VARCHAR(255) NOT NULL,
    report_status ENUM('Pending', 'Verified', 'Completed') DEFAULT 'Pending',
    comment TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_by_receptionist_id INT,
    verified_by_admin_id INT,
    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sent_by_receptionist_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (verified_by_admin_id) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Create sample_collections table
CREATE TABLE IF NOT EXISTS sample_collections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    agent_id INT NOT NULL,
    collection_status ENUM('Pending', 'Collected', 'Delivered') NOT NULL DEFAULT 'Pending',
    collection_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES pickup_agents(id)
);

-- Create coupons table
CREATE TABLE IF NOT EXISTS coupons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) NOT NULL UNIQUE,
    discount_amount DECIMAL(10, 2) NOT NULL,
    expiry_date DATE NOT NULL,
    status ENUM('Active', 'Expired', 'Used') NOT NULL DEFAULT 'Active',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create coupon_usage table
CREATE TABLE IF NOT EXISTS coupon_usage (
    id INT PRIMARY KEY AUTO_INCREMENT,
    coupon_id INT NOT NULL,
    user_id INT NOT NULL,
    booking_id INT,
    used_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE SET NULL,
    UNIQUE (coupon_id, user_id)
);

-- Create otp_verification table
CREATE TABLE IF NOT EXISTS otp_verification (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(15) NOT NULL,
    email VARCHAR(100) NOT NULL,
    otp VARCHAR(6) NOT NULL,
    verified TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL
);

-- Create referrals table
CREATE TABLE IF NOT EXISTS referrals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    referral_code VARCHAR(50) NOT NULL,
    referral_status ENUM('Pending', 'Rewarded') NOT NULL DEFAULT 'Pending',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create report_history table
CREATE TABLE IF NOT EXISTS report_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    doctor_id INT,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id)
);

-- Show all tables created
SHOW TABLES;
