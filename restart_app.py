#!/usr/bin/env python3
"""
<PERSON>ript to restart the Flask application and clear rate limits
"""

import os
import sys
import time
import psutil
import signal
from dotenv import load_dotenv

load_dotenv()

def find_flask_processes():
    """Find running Flask processes"""
    flask_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('app.py' in arg or 'flask' in arg.lower() for arg in cmdline):
                if any('python' in arg.lower() for arg in cmdline):
                    flask_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return flask_processes

def stop_flask_processes():
    """Stop running Flask processes"""
    processes = find_flask_processes()
    
    if not processes:
        print("ℹ️  No Flask processes found running")
        return True
    
    print(f"🔄 Found {len(processes)} Flask process(es) running")
    
    for proc in processes:
        try:
            print(f"   Stopping process {proc.pid}: {' '.join(proc.cmdline())}")
            proc.terminate()
            
            # Wait for graceful shutdown
            try:
                proc.wait(timeout=5)
                print(f"   ✅ Process {proc.pid} stopped gracefully")
            except psutil.TimeoutExpired:
                print(f"   ⚠️  Process {proc.pid} didn't stop gracefully, forcing...")
                proc.kill()
                proc.wait()
                print(f"   ✅ Process {proc.pid} force stopped")
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"   ⚠️  Could not stop process {proc.pid}: {e}")
    
    return True

def start_flask_app():
    """Start the Flask application"""
    print("🚀 Starting Flask application...")
    
    # Change to the app directory
    app_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(app_dir)
    
    # Start the Flask app in the background
    if os.name == 'nt':  # Windows
        import subprocess
        subprocess.Popen([sys.executable, 'app.py'], 
                        creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:  # Unix/Linux/Mac
        os.system(f'{sys.executable} app.py &')
    
    print("✅ Flask application started")
    print("💡 Check http://localhost:7000/health to verify it's running")

def clear_session_files():
    """Clear Flask session files if using filesystem sessions"""
    session_dir = './flask_session'
    if os.path.exists(session_dir):
        try:
            import shutil
            shutil.rmtree(session_dir)
            print("🗑️  Cleared Flask session files")
        except Exception as e:
            print(f"⚠️  Could not clear session files: {e}")

def main():
    """Main restart function"""
    print("🔄 CVBioLabs Flask App Restarter")
    print("=" * 40)
    
    # Check if we should force restart
    force = len(sys.argv) > 1 and sys.argv[1] == '--force'
    
    if force:
        print("⚠️  Force restart mode enabled")
    
    # Stop existing processes
    print("\n1️⃣  Stopping existing Flask processes...")
    stop_flask_processes()
    
    # Clear session files to reset rate limits
    print("\n2️⃣  Clearing session data...")
    clear_session_files()
    
    # Wait a moment
    print("\n3️⃣  Waiting for cleanup...")
    time.sleep(2)
    
    # Start the app
    print("\n4️⃣  Starting Flask application...")
    start_flask_app()
    
    print("\n✅ Restart complete!")
    print("🔗 Application should be available at: http://localhost:7000")
    print("🏥 Health check: http://localhost:7000/health")
    print("📊 Rate limits have been cleared")
    
    # Wait a moment and check if it started
    print("\n⏳ Waiting for application to start...")
    time.sleep(3)
    
    try:
        import requests
        response = requests.get('http://localhost:7000/health', timeout=5)
        if response.status_code == 200:
            print("✅ Application is running and healthy!")
        else:
            print(f"⚠️  Application responded with status {response.status_code}")
    except Exception as e:
        print(f"⚠️  Could not verify application status: {e}")
        print("💡 Please check manually at http://localhost:7000")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Restart cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during restart: {e}")
        sys.exit(1)
