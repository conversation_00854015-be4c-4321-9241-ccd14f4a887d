"""
Secure error handling for CVBioLabs application
Provides consistent error responses without information disclosure
"""

import logging
import traceback
from datetime import datetime
from flask import jsonify, render_template, request, current_app
from werkzeug.exceptions import HTTPException
import os

logger = logging.getLogger(__name__)

class SecureErrorHandler:
    """Secure error handling utilities"""
    
    # Generic error messages to prevent information disclosure
    GENERIC_MESSAGES = {
        400: "Bad request. Please check your input and try again.",
        401: "Authentication required. Please log in.",
        403: "Access denied. You don't have permission to access this resource.",
        404: "The requested resource was not found.",
        405: "Method not allowed for this endpoint.",
        413: "File too large. Please upload a smaller file.",
        429: "Too many requests. Please try again later.",
        500: "An internal error occurred. Please try again later.",
        502: "Service temporarily unavailable. Please try again later.",
        503: "Service temporarily unavailable. Please try again later."
    }
    
    @staticmethod
    def log_error(error, request_info=None):
        """Log error details securely"""
        try:
            error_id = f"ERR_{hash(str(error))}"
            
            # Prepare request information
            if not request_info and request:
                request_info = {
                    'method': request.method,
                    'url': request.url,
                    'remote_addr': request.remote_addr,
                    'user_agent': request.headers.get('User-Agent', 'Unknown'),
                    'endpoint': request.endpoint
                }
            
            # Log error with context
            logger.error(
                f"Error ID: {error_id} | "
                f"Type: {type(error).__name__} | "
                f"Message: {str(error)} | "
                f"Request: {request_info}",
                exc_info=True
            )
            
            return error_id
        except Exception as e:
            logger.critical(f"Failed to log error: {e}")
            return "ERR_UNKNOWN"
    
    @staticmethod
    def create_error_response(status_code, message=None, error_id=None, is_api=False):
        """Create standardized error response"""
        
        # Use generic message if none provided
        if not message:
            message = SecureErrorHandler.GENERIC_MESSAGES.get(
                status_code, 
                "An error occurred. Please try again."
            )
        
        if is_api or request.is_json or request.path.startswith('/api/'):
            # API response
            response_data = {
                'status': 'error',
                'message': message,
                'error_code': status_code
            }
            
            # Include error ID only in development
            if error_id and os.getenv('FLASK_ENV') == 'development':
                response_data['error_id'] = error_id
            
            return jsonify(response_data), status_code
        else:
            # Web page response
            return render_template(
                'error.html',
                error_code=status_code,
                error_message=message,
                error_id=error_id if os.getenv('FLASK_ENV') == 'development' else None
            ), status_code

def register_error_handlers(app):
    """Register secure error handlers with Flask app"""
    
    @app.errorhandler(400)
    def bad_request(error):
        # Check if this is an SSL handshake attempt or malformed request
        error_msg = str(error)
        if 'Bad request version' in error_msg or 'SSL' in error_msg or 'h2' in error_msg:
            # Don't log these as errors - they're likely SSL handshake attempts
            logger.info(f"SSL handshake or malformed request from {request.remote_addr if request else 'unknown'}: {error_msg}")
            return '', 400  # Return empty response for these

        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(400, error_id=error_id)
    
    @app.errorhandler(401)
    def unauthorized(error):
        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(401, error_id=error_id)
    
    @app.errorhandler(403)
    def forbidden(error):
        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(403, error_id=error_id)
    
    @app.errorhandler(404)
    def not_found(error):
        # Don't log 404s as errors (too noisy)
        return SecureErrorHandler.create_error_response(404)
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(405, error_id=error_id)
    
    @app.errorhandler(413)
    def request_entity_too_large(error):
        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(
            413, 
            "File too large. Maximum file size is 16MB.",
            error_id=error_id
        )
    
    @app.errorhandler(429)
    def ratelimit_handler(error):
        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(
            429,
            f"Rate limit exceeded. {error.description}",
            error_id=error_id
        )
    
    @app.errorhandler(500)
    def internal_server_error(error):
        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(500, error_id=error_id)
    
    @app.errorhandler(502)
    def bad_gateway(error):
        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(502, error_id=error_id)
    
    @app.errorhandler(503)
    def service_unavailable(error):
        error_id = SecureErrorHandler.log_error(error)
        return SecureErrorHandler.create_error_response(503, error_id=error_id)
    
    @app.errorhandler(Exception)
    def handle_unexpected_error(error):
        """Handle any unexpected errors"""
        # Don't handle HTTP exceptions here
        if isinstance(error, HTTPException):
            return error
        
        error_id = SecureErrorHandler.log_error(error)
        
        # In development, show more details
        if os.getenv('FLASK_ENV') == 'development':
            message = f"Unexpected error: {str(error)}"
        else:
            message = "An unexpected error occurred. Please try again later."
        
        return SecureErrorHandler.create_error_response(500, message, error_id=error_id)

class SecurityLogger:
    """Security-focused logging utilities"""
    
    @staticmethod
    def log_security_event(event_type, details, severity='WARNING'):
        """Log security-related events"""
        try:
            security_logger = logging.getLogger('security')
            
            log_entry = {
                'event_type': event_type,
                'details': details,
                'timestamp': str(datetime.utcnow()),
                'remote_addr': request.remote_addr if request else 'Unknown',
                'user_agent': request.headers.get('User-Agent', 'Unknown') if request else 'Unknown',
                'endpoint': request.endpoint if request else 'Unknown'
            }
            
            if severity == 'CRITICAL':
                security_logger.critical(f"SECURITY EVENT: {log_entry}")
            elif severity == 'ERROR':
                security_logger.error(f"SECURITY EVENT: {log_entry}")
            elif severity == 'WARNING':
                security_logger.warning(f"SECURITY EVENT: {log_entry}")
            else:
                security_logger.info(f"SECURITY EVENT: {log_entry}")
                
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")
    
    @staticmethod
    def log_failed_login(email, reason):
        """Log failed login attempts"""
        SecurityLogger.log_security_event(
            'FAILED_LOGIN',
            {
                'email': email,
                'reason': reason,
                'ip': request.remote_addr if request else 'Unknown'
            },
            'WARNING'
        )
    
    @staticmethod
    def log_suspicious_activity(activity_type, details):
        """Log suspicious activities"""
        SecurityLogger.log_security_event(
            'SUSPICIOUS_ACTIVITY',
            {
                'activity_type': activity_type,
                'details': details
            },
            'ERROR'
        )
    
    @staticmethod
    def log_access_denied(resource, reason):
        """Log access denied events"""
        SecurityLogger.log_security_event(
            'ACCESS_DENIED',
            {
                'resource': resource,
                'reason': reason
            },
            'WARNING'
        )

# Global instances
secure_error_handler = SecureErrorHandler()
security_logger = SecurityLogger()
