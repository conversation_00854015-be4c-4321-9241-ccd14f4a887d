#!/usr/bin/env python3
"""
Database connection pooling for CVBioLabs application
This module provides connection pooling to improve database performance
"""

import mysql.connector.pooling
import os
from dotenv import load_dotenv
import logging
from contextlib import contextmanager

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class DatabasePool:
    """Database connection pool manager"""
    
    def __init__(self):
        self.pool = None
        self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize the connection pool"""
        try:
            pool_config = {
                'pool_name': 'cvbiolabs_pool',
                'pool_size': 10,  # Adjust based on your needs
                'pool_reset_session': True,
                'host': os.getenv('DB_HOST'),
                'user': os.getenv('DB_USER'),
                'password': os.getenv('DB_PASSWORD'),
                'database': os.getenv('DB_NAME'),
                'charset': os.getenv('DB_CHARSET', 'utf8mb4'),
                'autocommit': False,
                'time_zone': '+00:00',
                'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO',
                'connect_timeout': 10,
                'buffered': True
            }
            
            self.pool = mysql.connector.pooling.MySQLConnectionPool(**pool_config)
            logger.info(f"Database connection pool initialized with {pool_config['pool_size']} connections")
            
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise
    
    def get_connection(self):
        """Get a connection from the pool"""
        try:
            return self.pool.get_connection()
        except Exception as e:
            logger.error(f"Failed to get connection from pool: {e}")
            raise
    
    @contextmanager
    def get_connection_context(self):
        """Context manager for database connections"""
        conn = None
        try:
            conn = self.get_connection()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_pool_status(self):
        """Get pool status information"""
        if self.pool:
            return {
                'pool_name': self.pool.pool_name,
                'pool_size': self.pool.pool_size,
                'connections_in_use': len([conn for conn in self.pool._cnx_queue._queue if conn.is_connected()]),
                'available_connections': self.pool._cnx_queue.qsize()
            }
        return None

# Global pool instance
db_pool = DatabasePool()

def get_db_connection():
    """Get a database connection from the pool"""
    return db_pool.get_connection()

def get_db_connection_context():
    """Get a database connection context manager"""
    return db_pool.get_connection_context()

def get_pool_status():
    """Get database pool status"""
    return db_pool.get_pool_status()

# Example usage:
# 
# # Method 1: Manual connection management
# conn = get_db_connection()
# try:
#     cursor = conn.cursor()
#     cursor.execute("SELECT * FROM testdetails LIMIT 1")
#     result = cursor.fetchone()
# finally:
#     conn.close()
#
# # Method 2: Context manager (recommended)
# with get_db_connection_context() as conn:
#     cursor = conn.cursor()
#     cursor.execute("SELECT * FROM testdetails LIMIT 1")
#     result = cursor.fetchone()
