<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Dashboard - CVBioLabs</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-orange: #f58220;
            --deep-blue: #003865;
            --bright-blue: #007dc5;
            --light-bg: #f0f7fb;
            --white: #ffffff;
            --text-dark: #1a1a1a;
            --text-light: #6b7280;
            --success: #10b981;
            --gradient-primary: linear-gradient(135deg, var(--deep-blue) 0%, var(--bright-blue) 100%);
            --gradient-accent: linear-gradient(135deg, var(--primary-orange) 0%, #ff6b35 100%);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
            background: linear-gradient(135deg, var(--light-bg) 0%, rgba(240, 247, 251, 0.5) 50%, white 100%);
        }
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-orange);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #e07020;
        }

        /* Header with Glass Effect */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo img {
            width: 70px;
            height: 70px;
            border-radius: 8px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .logo-text {
            font-family: 'Poppins', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 500;
            position: relative;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: rgba(245, 130, 32, 0.1);
            color: var(--primary-orange);
        }

        .auth-buttons {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-accent);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-dark);
            border: 2px solid rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            background: var(--light-bg);
            border-color: var(--primary-orange);
        }

        /* Dropdown Styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background: #fff;
            min-width: 180px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
            border-radius: 12px;
            z-index: 1001;
            padding: 0.5rem 0;
        }
        .dropdown-content a {
            color: #1a1a1a;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            display: block;
            font-weight: 500;
            border-radius: 0;
            transition: background 0.2s;
        }
        .dropdown-content a:hover {
            background: #f0f7fb;
            color: #f58220;
        }
        .dropdown:hover .dropdown-content,
        .dropdown:focus-within .dropdown-content {
            display: block;
        }
        .dropdown-btn {
            background: #fff;
            border: 2px solid #eee;
            border-radius: 50px;
            padding: 0.5rem 1.25rem;
            font-weight: 600;
            color: #1a1a1a;
            cursor: pointer;
            transition: border 0.2s;
        }
        .dropdown-btn:hover,
        .dropdown-btn:focus {
            border-color: #f58220;
            color: #f58220;
        }
        /* Main Content */
        .main-content {
            padding-top: 120px;
            min-height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        /* Dashboard Welcome Section */
        .dashboard-welcome {
            background: linear-gradient(135deg, var(--light-bg) 0%, rgba(240, 247, 251, 0.5) 50%, white 100%);
            border-radius: 20px;
            padding: 3rem 2rem;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }

        .dashboard-welcome::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -10%;
            width: 80%;
            height: 150%;
            background: linear-gradient(45deg, transparent 0%, rgba(0, 125, 197, 0.05) 50%, transparent 100%);
            transform: rotate(15deg);
            z-index: 1;
        }

        .welcome-content {
            position: relative;
            z-index: 2;
        }

        .welcome-text {
            font-family: 'Poppins', sans-serif;
            font-size: clamp(1.8rem, 4vw, 2.5rem);
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: var(--text-light);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* Section Styles */
        .section {
            display: none;
            margin-bottom: 3rem;
        }

        .section.active {
            display: block;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-header h2 {
            font-family: 'Poppins', sans-serif;
            font-size: clamp(2rem, 4vw, 2.5rem);
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .section-header p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Card Grid */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            font-family: 'Poppins', sans-serif;
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        /* Report Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 999px;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-block;
            margin-top: 0.5rem;
        }

        .status-pending {
            background: rgba(244,124,32,0.1);
            color: #f47c20;
        }

        .status-verified {
            background: rgba(59,130,246,0.1);
            color: #3b82f6;
        }

        .status-completed {
            background: rgba(16,185,129,0.1);
            color: #10b981;
        }

        /* Report and Content Styles */
        .report-item {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }

        .report-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
        }

        .report-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .test-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .test-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
        }

        .test-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Report List */
        .report-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .report-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 1.5rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .report-info {
            flex: 1;
            padding-right: 1rem;
        }

        .report-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-info {
            color: #64748b;
            font-size: 0.875rem;
            line-height: 1.5;
            margin-top: 0.5rem;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: var(--white);
            padding: 2rem;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            box-shadow: var(--shadow-xl);
            position: relative;
            animation: fadeInUp 0.4s cubic-bezier(0.4,0,0.2,1);
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 1.5rem;
        }

        /* Test Cards Specific Styles */
        .search-container {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .test-title {
            color: var(--dark-blue);
            font-size: 1.125rem;
            font-weight: 600;
        }

        .department-badge {
            background: var(--light-blue);
            color: var(--dark-blue);
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-block;
        }

        .tat-badge {
            background: rgba(244,124,32,0.1);
            color: var(--orange);
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-block;
        }

        .price {
            color: var(--orange);
            font-size: 1.25rem;
            font-weight: 600;
        }

        /* Enhanced Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--deep-blue);
            font-size: 0.9rem;
            letter-spacing: 0.3px;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: var(--white);
            color: var(--text-dark);
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-orange);
            box-shadow: 0 0 0 3px rgba(245, 130, 32, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
            background: var(--white);
            transform: translateY(-1px);
        }

        .form-input:hover {
            border-color: #cbd5e1;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .form-input::placeholder {
            color: #94a3b8;
            font-size: 0.95rem;
        }

        .profile-form {
            max-width: 700px;
            margin: 0 auto;
            background: var(--white);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .profile-picture-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 2.5rem;
        }

        .profile-picture {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 1rem;
            cursor: pointer;
            background: linear-gradient(135deg, var(--light-bg) 0%, #e2e8f0 100%);
            border: 4px solid var(--white);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .profile-picture:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
        }

        .profile-picture img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .picture-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .picture-overlay i {
            color: white;
            font-size: 1.5rem;
        }

        .profile-picture:hover .picture-overlay {
            opacity: 1;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2.5rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
        }

        .form-actions .btn {
            min-width: 140px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            border-radius: 50px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-actions .btn:hover {
            transform: translateY(-2px);
        }

        /* Enhanced Edit Profile Section */
        #edit-profile {
            background: var(--light-bg);
            padding: 2rem 0;
            border-radius: 20px;
            margin: 2rem 0;
        }

        #edit-profile .section-header {
            margin-bottom: 2rem;
        }

        #edit-profile .section-header h2 {
            color: var(--deep-blue);
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        #edit-profile .section-header p {
            color: var(--text-light);
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .profile-form {
                padding: 1.5rem;
                margin: 1rem;
            }

            .form-actions {
                flex-direction: column;
                gap: 0.75rem;
            }

            .form-actions .btn {
                min-width: auto;
                width: 100%;
            }

            .profile-picture {
                width: 100px;
                height: 100px;
            }
        }

        @media (max-width: 640px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            #edit-profile {
                margin: 1rem 0;
                padding: 1rem 0;
            }

            .profile-form {
                padding: 1rem;
                border-radius: 15px;
            }
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        #logoutBtn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        #logoutBtn i {
            font-size: 1rem;
        }

        .cart-icon {
            position: relative;
            cursor: pointer;
            font-size: 1.2rem;
            color: var(--dark-blue);
            padding: 0.5rem;
            transition: all 0.3s ease;
            margin-left: 0;  /* Remove left margin */
        }

        .cart-icon:hover {
            color: var(--orange);
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--orange);
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        #cartItems {
            max-height: 400px;
            overflow-y: auto;
            margin: 1rem 0;
        }

        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--gray-200);
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-right: 1rem;
        }

        .quantity-btn {
            background: var(--gray-200);
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .quantity-btn:hover {
            background: var(--gray-100);
        }

        .quantity-value {
            min-width: 20px;
            text-align: center;
        }

        .cart-total {
            margin: 1rem 0;
            text-align: right;
            padding-top: 1rem;
            border-top: 2px solid var(--gray-200);
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        /* Professional Cart Modal Styling */
        .cart-modal-content {
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            padding: 0;
            font-family: 'Inter', sans-serif;
            max-width: 1000px;
            width: 90%;
            max-height: 85vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Cart Header */
        .cart-header {
            background: linear-gradient(135deg, #f58220 0%, #ff6b35 100%);
            padding: 2.25rem 2rem 2rem 2rem;
            border-radius: 20px 20px 0 0;
            position: relative;
            overflow: hidden;
            min-height: 120px;
        }
        .cart-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        .cart-header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            position: relative;
            z-index: 2;
            height: 100%;
        }
        .cart-title {
            display: flex;
            align-items: flex-start;
            gap: 1.25rem;
            flex: 1;
            padding-top: 0.5rem;
        }
        .cart-icon-wrapper {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .cart-icon-wrapper i {
            font-size: 1.5rem;
            color: white;
        }
        .cart-title-text {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding-top: 0.25rem;
        }
        .cart-title-text h3 {
            font-family: 'Poppins', sans-serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            line-height: 1.2;
        }
        .cart-subtitle {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.98);
            margin: 0;
            font-weight: 500;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.3px;
            background: rgba(255, 255, 255, 0.12);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            display: inline-block;
            max-width: fit-content;
        }
        .cart-close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 12px;
            padding: 0.75rem;
            color: white;
            font-size: 1.25rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .cart-close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        /* Cart Content */
        .cart-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background: #fafbfc;
        }
        .cart-main {
            flex: 1;
            display: flex;
            gap: 2.5rem;
            padding: 2rem;
            overflow: hidden;
            min-height: 0;
        }
        .cart-items-container {
            flex: 1.8;
            min-width: 0;
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
        }
        .cart-items-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f1f5f9;
        }
        .cart-items-header h4 {
            font-family: 'Poppins', sans-serif;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--deep-blue);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .cart-items-count {
            background: var(--light-bg);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-light);
        }
        .cart-items-list {
            flex: 1;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        /* Cart Sidebar */
        .cart-sidebar {
            flex: 1;
            min-width: 280px;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            overflow-y: auto;
        }

        /* Order Summary */
        .order-summary {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .summary-header h4 {
            font-family: 'Poppins', sans-serif;
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--deep-blue);
            margin: 0 0 1.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .summary-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1rem;
        }
        .summary-row span:first-child {
            color: var(--text-light);
            font-weight: 500;
        }
        .summary-row span:last-child {
            color: var(--text-dark);
            font-weight: 600;
        }
        .total-row {
            font-size: 1.25rem;
            font-weight: 700;
            padding-top: 0.5rem;
        }
        .total-row span:first-child {
            color: var(--deep-blue);
        }
        .total-row span:last-child {
            color: var(--orange);
            font-size: 1.5rem;
        }

        /* Cart Footer */
        .cart-footer {
            background: #f8fafc;
            padding: 1.5rem 2rem;
            border-top: 1px solid #e2e8f0;
            border-radius: 0 0 24px 24px;
        }
        .cart-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            align-items: center;
        }
        .btn-secondary-cart {
            background: white;
            color: var(--text-light);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .btn-secondary-cart:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            transform: translateY(-1px);
        }
        .btn-primary-cart {
            background: var(--gradient-accent);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            justify-content: center;
        }
        .btn-primary-cart:hover {
            background: linear-gradient(135deg, #ff6b35 0%, var(--orange) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2);
        }
        .btn-primary-cart:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Cart Item Styling */
        .cart-item {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
        }
        .cart-item:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border-color: #cbd5e1;
        }
        .cart-item-info {
            flex: 1;
            min-width: 0;
        }
        .cart-item-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--deep-blue);
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }
        .cart-item-details {
            font-size: 0.875rem;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .cart-item-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: #f1f5f9;
            border-radius: 8px;
            padding: 0.25rem;
        }
        .quantity-btn {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-dark);
        }
        .quantity-btn:hover {
            background: var(--orange);
            color: white;
            border-color: var(--orange);
        }
        .quantity-value {
            min-width: 32px;
            text-align: center;
            font-weight: 600;
            color: var(--text-dark);
        }
        .cart-item-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #f58220;
            min-width: 85px;
            text-align: right;
        }

        .remove-item-btn {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #ef4444;
            font-size: 0.85rem;
            cursor: pointer;
            padding: 0.4rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.5rem;
        }
        .remove-item-btn:hover {
            background: #fee2e2;
            color: #dc2626;
            border-color: #fca5a5;
            transform: scale(1.05);
        }

        /* Empty Cart State */
        .empty-cart {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 16px;
            padding: 3rem 2rem;
            text-align: center;
            border: 2px dashed #cbd5e1;
        }
        .empty-cart i {
            font-size: 3rem;
            color: #cbd5e1;
            margin-bottom: 1rem;
        }
        .empty-cart p {
            font-size: 1.125rem;
            color: var(--text-light);
            margin-bottom: 1.5rem;
        }
        .continue-shopping-btn {
            background: var(--gradient-accent);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .continue-shopping-btn:hover {
            background: linear-gradient(135deg, #ff6b35 0%, var(--orange) 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);
        }

        /* Responsive Design for Cart */
        @media (max-width: 1024px) {
            .cart-modal-content {
                max-width: 95%;
                margin: 1rem;
            }
            .cart-main {
                flex-direction: column;
                gap: 1.5rem;
            }
            .cart-sidebar {
                min-width: auto;
            }
        }
        @media (max-width: 768px) {
            .cart-modal-content {
                margin: 0.5rem;
                border-radius: 16px;
                max-height: 95vh;
            }
            .cart-header {
                padding: 1.5rem;
                border-radius: 16px 16px 0 0;
            }
            .cart-title-text h3 {
                font-size: 1.5rem;
            }
            .cart-main {
                padding: 1.5rem;
            }
            .cart-footer {
                padding: 1rem 1.5rem;
            }
            .cart-actions {
                flex-direction: column;
                gap: 0.75rem;
            }
            .btn-secondary-cart,
            .btn-primary-cart {
                width: 100%;
                justify-content: center;
            }
            .cart-item {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }
            .cart-item-controls {
                justify-content: space-between;
            }
            .cart-item-price {
                text-align: center;
                font-size: 1.25rem;
            }
        }

        .status-paid {
    background-color: green;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
}

        .nav-wrapper {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
    list-style: none;
    margin-right: 1rem;
    padding: 0;
}

.nav-menu li a, .home-btn {
    text-decoration: none;
    color: var(--deep-blue);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    position: relative;
    background: none;
}

.nav-menu li a:hover, .home-btn:hover {
    background: rgba(245,130,32,0.1);
    color: var(--primary-orange);
}

.auth-buttons .auth-btn, .dropdown-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
    background: var(--gradient-accent);
    color: white;
    box-shadow: var(--shadow-md);
}

.auth-buttons .auth-btn:hover, .dropdown-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    opacity: 0.95;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-btn {
    background-color: var(--orange);
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background-color: white;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
    z-index: 1000;
}

.dropdown-content a {
    color: var(--deep-blue);
    padding: 12px 20px;
    text-decoration: none;
    display: block;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.dropdown-content a:hover {
    background-color: var(--light-bg);
    color: var(--primary-orange);
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown:hover .dropdown-btn {
    background: #e06b15;
    color: white;
}

.home-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--dark-blue);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border-radius: 25px;
}

.home-btn:hover {
    color: var(--orange);
    background-color: var(--light-blue);
}

.home-btn i {
    font-size: 1.2rem;
}

.menu-items {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
    list-style: none;
    margin-right: 1rem;
}

/* Add or update these styles in your existing <style> section */

@media screen and (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }
    .dashboard-header {
            position: sticky;
            top: 0;
            z-index: 101;
        }

    .sidebar {
            position: fixed;
            bottom: 0;
            top: auto;
            left: 0;
            width: 100%;
            height: auto;
            min-height: 110px;
            z-index: 1000;
        }

    .sidebar-header {
        display: none; /* Hide the header on mobile */
    }

    .nav-menu {
        flex-direction: row;
        justify-content: space-evenly;
        flex-wrap: wrap;
        padding: 0.5rem;
        background: white;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        width: 100%;
    }

    .nav-item {
        flex: 0 1 20%;  /* Changed from flex: 1 to allow wrapping */
        min-width: 60px;
        padding: 0.5rem;
        text-align: center;
        border-radius: 0;
        color: var(--dark-blue);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        margin: 2px;
    }

    .nav-item::before {
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        font-size: 1.2rem;
        display: block;
        margin-bottom: 4px;
        color: var(--orange);
    }

    .nav-item span {
        display: block;
        font-size: 0.65rem;  /* Reduced font size */
        color: var(--dark-blue);
        font-weight: 500;
        white-space: normal;  /* Changed from nowrap to allow text wrapping */
        text-align: center;
        line-height: 1.2;
        margin-top: 2px;
    }

    /* Adjust main content margin for fixed bottom nav */
    .main-content {
        margin-bottom: 120px;  /* Increased to accommodate two rows */
        padding: 1rem;
    }

    /* Ensure bottom navigation has enough height */
    .sidebar {
        min-height: 110px;  /* Increased height to fit two rows */
    }
}

/* Add styles for extra small screens */
@media screen and (max-width: 320px) {
    .nav-item {
        flex: 0 1 33%;  /* Show 3 items per row on very small screens */
        min-width: 50px;
    }

    .nav-item span {
        font-size: 0.6rem;
    }

    .main-content {
        margin-bottom: 150px;  /* More space for three rows */
    }

    .sidebar {
        min-height: 140px;
    }
}

/* Add these styles to your existing CSS */
.reports-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
}

.reports-table table {
    width: 100%;
    border-collapse: collapse;
}

.reports-table th,
.reports-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.reports-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #1e293b;
}

.reports-table tr:last-child td {
    border-bottom: none;
}

.reports-table tr:hover {
    background: #f8fafc;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 999px;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-block;
}

.status-pending {
    background: rgba(244,124,32,0.1);
    color: #f47c20;
}

.status-verified {
    background: rgba(59,130,246,0.1);
    color: #3b82f6;
}

.status-completed {
    background: rgba(16,185,129,0.1);
    color: #10b981;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-accent);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff6b35 0%, var(--primary-orange) 100%);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--light-bg);
    color: var(--deep-blue);
    border: 2px solid var(--primary-orange);
}

.btn-secondary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

@media (max-width: 768px) {
    .reports-table {
        overflow-x: auto;
    }
    
    .reports-table table {
        min-width: 600px;
    }
}

/* Add these styles to your existing CSS */
.download-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: #f47c20;
    color: white;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s;
}

.download-btn:hover {
    background-color: #e06b1c;
}

.download-btn i {
    font-size: 16px;
}

.status-text {
    color: #64748b;
    font-weight: 500;
}

.reports-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-top: 20px;
}

.reports-table table {
    width: 100%;
    border-collapse: collapse;
}

.reports-table th,
.reports-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.reports-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #1e293b;
}

.reports-table tr:last-child td {
    border-bottom: none;
}

.reports-table tr:hover {
    background: #f8fafc;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 999px;
    font-size: 14px;
    font-weight: 500;
    display: inline-block;
}

.status-pending {
    background: rgba(244,124,32,0.1);
    color: #f47c20;
}

.status-verified {
    background: rgba(59,130,246,0.1);
    color: #3b82f6;
}

.status-completed {
    background: rgba(16,185,129,0.1);
    color: #10b981;
}

.alert {
    padding: 12px 16px;
    margin-bottom: 16px;
    border-radius: 6px;
    font-weight: 500;
}

.alert-error {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-success {
    background: #dcfce7;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

@media (max-width: 768px) {
    .reports-table {
        overflow-x: auto;
    }
    
    .reports-table table {
        min-width: 600px;
    }
    
    .download-btn {
        padding: 6px 12px;
        font-size: 14px;
    }
}

/* Profile Section Styles */
.profile-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.profile-header {
    margin-bottom: 1.5rem;
}

.profile-header h3 {
    color: var(--dark-blue);
    margin-bottom: 0.5rem;
}

.profile-header p {
    color: #64748b;
    font-size: 0.875rem;
}

.profile-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.profile-details .form-group {
    margin-bottom: 0;
}

.profile-details .form-label {
    margin-bottom: 0.25rem;
}

.profile-details .form-input {
    background: #f7f7f7;
    padding: 0.75rem;
    border: 1px solid var(--gray-200);
    border-radius: 6px;
    color: #333;
}

.profile-details .form-input[readonly] {
    background: #e9ecef;
    cursor: not-allowed;
}

.profile-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

.profile-actions .btn {
    flex: 1;
}

        /* WhatsApp Button */
        .whatsapp-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            text-decoration: none;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .whatsapp-btn:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-xl);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Enhanced Form Validation Styles */
        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-error {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .form-success {
            color: #10b981;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        .section {
            animation: fadeInUp 0.6s ease-out;
        }

        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        .welcome-content {
            animation: slideInLeft 0.8s ease-out;
        }

        /* Mobile Navigation */
        .mobile-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.5rem;
            z-index: 1000;
        }

        .mobile-nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.5rem;
            color: var(--text-dark);
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        .mobile-nav-item.active,
        .mobile-nav-item:hover {
            color: var(--primary-orange);
            background: rgba(245, 130, 32, 0.1);
        }

        .mobile-nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .mobile-nav {
                display: block;
            }

            .main-content {
                padding-left: 1rem;
                padding-right: 1rem;
                padding-top: 100px;
                padding-bottom: 100px; /* Space for mobile nav */
            }

            .dashboard-welcome {
                padding: 2rem 1rem;
            }

            .welcome-text {
                font-size: 1.8rem;
            }

            .card-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .card {
                padding: 1.5rem;
            }

            .section-header h2 {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 640px) {
            .profile-details {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .welcome-text {
                font-size: 1.5rem;
            }

            .whatsapp-btn {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                bottom: 5rem;
                right: 1rem;
            }
        }
    </style>
    <!-- Add Razorpay Script -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <!-- Add CSRF token meta tag -->
    <meta name="csrf-token" content="{{ csrf_token }}">
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="nav">
            <div class="logo">
                <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBIOLABS Logo">
                <span class="logo-text">CVBIOLABS</span>
            </div>
            <ul class="nav-links">
                <li><a href="#" data-section="bookings" class="nav-link">My Bookings</a></li>
                <li><a href="#" data-section="reports" class="nav-link">Test Reports</a></li>
                <li><a href="#" data-section="payments" class="nav-link">Payments</a></li>
                <li><a href="#" data-section="profile" class="nav-link">Profile</a></li>
            </ul>
            <div class="auth-buttons">
                <a href="{{ url_for('home') }}" class="btn btn-secondary">
                    <i class="fas fa-home"></i> Home
                </a>
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-btn">
                        <i class="fas fa-user"></i> {{ current_user.username }}
                    </button>
                    <div class="dropdown-content">
                        <a href="{{ url_for('test') }}">Test Booking</a>
                        <a href="{{ url_for('logout') }}">Logout</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Welcome Section -->
        <div class="dashboard-welcome">
            <div class="welcome-content">
                <h1 class="welcome-text">
                    Welcome back, {% if profile %}{{ profile.first_name }} {{ profile.last_name }}{% else %}User{% endif %}!
                </h1>
                <p class="welcome-subtitle">
                    Manage your health tests, view reports, and track your wellness journey with CVBioLabs.
                </p>
            </div>
        </div>

        <!-- My Bookings -->
        <div class="section active" id="bookings">
            <div class="section-header">
                <h2>My Bookings</h2>
                <p>View and manage your test bookings</p>
            </div>
                <div class="report-list">
                    {% for booking in bookings %}
                        <div class="report-item">
                            <div class="report-info">
                                <h4 class="font-semibold">{{ booking.TestName }}</h4>
                                <p class="test-info">
                                    Test Code: {{ booking.TestCode }}<br>
                                    Department: {{ booking.DepartmentName }}<br>
                                    Sample Type: {{ booking.SampleType }}<br>
                                    {% if booking.TargetTAT %}
                                        TAT: {{ booking.TargetTAT }}<br>
                                    {% endif %}
                                    Price: ₹{{ booking.TestAmount }}<br>
                                    Booking Date: {{ booking.booking_date }}<br>
                                    Appointment Time: {{ booking.appointment_time }}<br>
                                    Barcode: {{ booking.barcode }}
                                </p>
                                <span class="status-badge status-{{ booking.status }}">{{ booking.status | capitalize }}</span>
                            </div>
                            <div class="report-actions">
                                {% if booking.status == 'pending' %}
                                    <button class="btn btn-primary cancel-booking" data-booking-id="{{ booking.id }}">Cancel Booking</button>
                                {% endif %}
                            </div>
                        </div>
                    {% else %}
                        <p>No bookings found.</p>
                    {% endfor %}
                </div>
            </div>

        <!-- Test Reports -->
        <div class="section" id="reports">
            <div class="section-header">
                <h2>Test Reports</h2>
                <p>Download and view your test results</p>
            </div>
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                <div class="reports-table">
                    <table class="w-full">
                        <thead>
                            <tr>
                                <th>Test Name</th>
                                <th>Barcode</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Comment</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in reports %}
                            <tr>
                                <td>{{ report.test_name }}</td>
                                <td>{{ report.barcode }}</td>
                                <td>{{ report.sent_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="status-badge status-{{ report.report_status | lower }}">
                                        {{ report.report_status }}
                                    </span>
                                </td>
                                <td>{{ report.comment or '' }}</td>
                                <td>
                                    {% if report.report_status in ['Completed', 'Verified'] %}
                                        <a href="{{ url_for('download_report', report_id=report.id) }}" class="download-btn">
                                            <i class="fas fa-download"></i> Download Report
                                        </a>
                                    {% else %}
                                        <span class="status-text">
                                            Pending
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center py-4">No reports found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

        <!-- Payments -->
        <div class="section" id="payments">
            <div class="section-header">
                <h2>Payment History</h2>
                <p>Track your payment transactions</p>
            </div>
    <div class="report-list">
        {% for payment in payments %}
            <div class="report-item">
                <div class="report-info">
                    <h4 class="font-semibold">{{ payment.TestName }}</h4>
                    <p class="test-info">
                        Booking ID: {{ payment.booking_id }}<br>
                        Amount: ₹{{ payment.amount }}<br>
                        Payment Method: {{ payment.payment_method }}<br>
                        Transaction ID: {{ payment.transaction_id }}<br>
                        Payment Date: {{ payment.payment_date }}
                    </p>
                    <span class="status-badge status-{{ payment.payment_status | lower }}">
                        {{ payment.payment_status | replace('paid', 'completed') | capitalize }}
                    </span>
                </div>
            </div>
        {% else %}
            <p>No payments found.</p>
        {% endfor %}
    </div>
</div>

            <!-- Cart Modal -->
            <div id="cartModal" class="modal">
                <div class="modal-content cart-modal-content">
                    <div class="cart-header">
                        <div class="cart-header-content">
                            <div class="cart-title">
                                <div class="cart-icon-wrapper">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="cart-title-text">
                                    <h3>Shopping Cart</h3>
                                    <p class="cart-subtitle">Review your selected tests</p>
                                </div>
                            </div>
                            <button id="closeCart" class="cart-close-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <div class="cart-content">
                        <div class="cart-main">
                            <div class="cart-items-container">
                                <div class="cart-items-header">
                                    <h4><i class="fas fa-list-ul"></i> Selected Tests</h4>
                                    <div class="cart-items-count">
                                        <span id="cartItemsCount">0 items</span>
                                    </div>
                                </div>
                                <div id="cartItems" class="cart-items-list"></div>
                            </div>

                            <div class="cart-sidebar">
                                <div class="order-summary">
                                    <div class="summary-header">
                                        <h4><i class="fas fa-receipt"></i> Order Summary</h4>
                                    </div>
                                    <div class="summary-content">
                                        <div class="summary-row total-row">
                                            <span>Total Amount</span>
                                            <span id="cartTotal">₹0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="cart-footer">
                            <div class="cart-actions">
                                <button id="continueShopping" class="btn-secondary-cart">
                                    <i class="fas fa-arrow-left"></i> Continue Shopping
                                </button>
                                <button id="bookTests" class="btn-primary-cart" data-amount="{{ total }}">
                                    <i class="fas fa-credit-card"></i> Proceed to Payment
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Reset Password Modal -->
            <div id="resetPasswordModal" class="modal">
                <div class="modal-content">
                    <h3 class="section-header text-xl font-semibold">Reset Password</h3>
                    <form id="resetPasswordForm">
                        <div class="form-group">
                            <label class="form-label">Old Password</label>
                            <input type="password" name="old_password" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">New Password</label>
                            <input type="password" name="new_password" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Confirm New Password</label>
                            <input type="password" name="confirm_password" class="form-input" required>
                        </div>
                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">Submit</button>
                            <button type="button" class="btn btn-secondary close-reset-modal">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>

        <!-- Profile Section -->
        <div class="section" id="profile">
            <div class="section-header">
                <h2><i class="fas fa-user-circle" style="color: var(--primary-orange); margin-right: 0.5rem;"></i>Profile Details</h2>
                <p>View and manage your personal information</p>
            </div>
            <div class="card" style="max-width: 700px; margin: 0 auto;">
                <!-- Profile Picture Section -->
                <div class="profile-picture-container" style="margin-bottom: 2rem;">
                    <div class="profile-picture" style="width: 100px; height: 100px;">
                        <img src="https://via.placeholder.com/100x100/f0f7fb/003865?text={% if profile %}{{ profile.first_name[0] }}{{ profile.last_name[0] if profile.last_name else '' }}{% else %}U{% endif %}" alt="Profile Picture">
                    </div>
                    <h3 style="color: var(--deep-blue); margin-top: 1rem; font-size: 1.3rem; font-weight: 600;">
                        {% if profile %}{{ profile.first_name }} {{ profile.last_name }}{% else %}User{% endif %}
                    </h3>
                    <p style="color: var(--text-light); font-size: 0.95rem;">CVBioLabs Patient</p>
                </div>

                <!-- Personal Information -->
                <div style="margin-bottom: 2rem;">
                    <h4 style="color: var(--deep-blue); margin-bottom: 1rem; font-size: 1.1rem; font-weight: 600; border-bottom: 2px solid var(--primary-orange); padding-bottom: 0.5rem; display: inline-block;">
                        <i class="fas fa-user" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Personal Information
                    </h4>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-signature" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Full Name
                        </label>
                        <div class="form-input" style="background:#f8fafc; border: 2px solid #e2e8f0; cursor: default;">
                            {% if profile %}{{ profile.first_name }} {{ profile.last_name }}{% else %}N/A{% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-birthday-cake" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Age
                            </label>
                            <div class="form-input" style="background:#f8fafc; border: 2px solid #e2e8f0; cursor: default;">
                                {% if profile.date_of_birth %}{{ profile.date_of_birth | calculate_age }} years{% else %}N/A{% endif %}
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-venus-mars" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Gender
                            </label>
                            <div class="form-input" style="background:#f8fafc; border: 2px solid #e2e8f0; cursor: default;">
                                {% if profile and profile.gender %}{{ profile.gender }}{% else %}N/A{% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div style="margin-bottom: 2rem;">
                    <h4 style="color: var(--deep-blue); margin-bottom: 1rem; font-size: 1.1rem; font-weight: 600; border-bottom: 2px solid var(--primary-orange); padding-bottom: 0.5rem; display: inline-block;">
                        <i class="fas fa-address-book" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Contact Information
                    </h4>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-envelope" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Email Address
                        </label>
                        <div class="form-input" style="background:#f8fafc; border: 2px solid #e2e8f0; cursor: default;">
                            {{ email or 'N/A' }}
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-phone" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Phone Number
                        </label>
                        <div class="form-input" style="background:#f8fafc; border: 2px solid #e2e8f0; cursor: default;">
                            {% if profile and profile.phone %}{{ profile.phone }}{% else %}N/A{% endif %}
                        </div>
                    </div>
                </div>

                <div class="profile-actions" style="text-align: center; padding-top: 1.5rem; border-top: 1px solid #e2e8f0;">
                    <button class="btn btn-primary" id="editProfileBtn" style="min-width: 160px;">
                        <i class="fas fa-edit" style="margin-right: 0.5rem;"></i>Edit Profile
                    </button>
                </div>
            </div>
        </div>

        <!-- Edit Profile Section (hidden by default) -->
        <div class="section" id="edit-profile" style="display:none;">
            <div class="section-header">
                <h2><i class="fas fa-user-edit" style="color: var(--primary-orange); margin-right: 0.5rem;"></i>Edit Profile</h2>
                <p>Update your personal information and keep your profile current</p>
            </div>
            <div class="profile-form">
                <!-- Profile Picture Section -->
                <div class="profile-picture-container">
                    <div class="profile-picture">
                        <img src="https://via.placeholder.com/120x120/f0f7fb/003865?text={% if profile %}{{ profile.first_name[0] }}{{ profile.last_name[0] if profile.last_name else '' }}{% else %}U{% endif %}" alt="Profile Picture">
                        <div class="picture-overlay">
                            <i class="fas fa-camera"></i>
                        </div>
                    </div>
                    <p style="color: var(--text-light); font-size: 0.9rem; text-align: center;">Click to change profile picture</p>
                </div>

                <form id="editProfileForm">
                    <!-- Personal Information -->
                    <div style="margin-bottom: 2rem;">
                        <h3 style="color: var(--deep-blue); margin-bottom: 1rem; font-size: 1.2rem; font-weight: 600; border-bottom: 2px solid var(--primary-orange); padding-bottom: 0.5rem; display: inline-block;">
                            <i class="fas fa-user" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Personal Information
                        </h3>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-signature" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Full Name *
                            </label>
                            <input type="text" name="name" value="{% if profile %}{{ profile.first_name }} {{ profile.last_name }}{% endif %}" class="form-input" placeholder="Enter your full name" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-birthday-cake" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Age
                                </label>
                                <input type="number" name="age" value="{% if profile.date_of_birth %}{{ profile.date_of_birth | calculate_age }}{% endif %}" class="form-input" placeholder="Enter your age" min="1" max="120">
                            </div>
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-venus-mars" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Gender
                                </label>
                                <select name="gender" class="form-input">
                                    <option value="">Select Gender</option>
                                    <option value="Male" {% if profile and profile.gender == 'Male' %}selected{% endif %}>Male</option>
                                    <option value="Female" {% if profile and profile.gender == 'Female' %}selected{% endif %}>Female</option>
                                    <option value="Other" {% if profile and profile.gender == 'Other' %}selected{% endif %}>Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div style="margin-bottom: 2rem;">
                        <h3 style="color: var(--deep-blue); margin-bottom: 1rem; font-size: 1.2rem; font-weight: 600; border-bottom: 2px solid var(--primary-orange); padding-bottom: 0.5rem; display: inline-block;">
                            <i class="fas fa-address-book" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Contact Information
                        </h3>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-envelope" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Email Address *
                            </label>
                            <input type="email" name="email" value="{{ email }}" class="form-input" placeholder="Enter your email address" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-phone" style="margin-right: 0.5rem; color: var(--primary-orange);"></i>Phone Number *
                            </label>
                            <input type="tel" name="phone" value="{% if profile %}{{ profile.phone }}{% endif %}" class="form-input" placeholder="Enter your phone number" required>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save" style="margin-right: 0.5rem;"></i>Save Changes
                        </button>
                        <button type="button" class="btn btn-secondary cancel-edit">
                            <i class="fas fa-times" style="margin-right: 0.5rem;"></i>Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <div class="mobile-nav">
        <div class="mobile-nav-items">
            <a href="#" data-section="bookings" class="mobile-nav-item nav-link">
                <i class="fas fa-calendar-check"></i>
                <span>Bookings</span>
            </a>
            <a href="#" data-section="reports" class="mobile-nav-item nav-link">
                <i class="fas fa-file-medical"></i>
                <span>Reports</span>
            </a>
            <a href="#" data-section="payments" class="mobile-nav-item nav-link">
                <i class="fas fa-credit-card"></i>
                <span>Payments</span>
            </a>
            <a href="#" data-section="profile" class="mobile-nav-item nav-link">
                <i class="fas fa-user"></i>
                <span>Profile</span>
            </a>
        </div>
    </div>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/917893620683?text=Hello! I would like to know more about your health check-up packages." class="whatsapp-btn" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        $(document).ready(function() {
            // Set up CSRF token for all AJAX requests
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", $('meta[name="csrf-token"]').attr('content'));
                    }
                }
            });

            // Header scroll effect
            window.addEventListener('scroll', function() {
                const header = document.getElementById('header');
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });

            // Tab Navigation
            $('.nav-link').click(function(e) {
                e.preventDefault();
                $('.nav-link').removeClass('active');
                $(this).addClass('active');
                $('.section').removeClass('active');
                const section = $(this).data('section');
                $('#' + section).addClass('active');
                $('.modal').removeClass('active');
            });

            // Show the first section by default
            $('.nav-link').first().click();

            // Department Filter
            $('#departmentFilter').change(function() {
                const department = $(this).val();
                $('.test-card').each(function() {
                    if (!department || $(this).data('department') === department) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });

            // Add to Cart
            $('.add-to-cart').click(function() {
                const testId = $(this).data('test-id');
                const button = $(this);
                button.prop('disabled', true);

                $.ajax({
                    url: '/cart/add',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ test_id: testId }),
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#cart-count').text(response.count);
                            const originalText = button.text();
                            button.text('Added!');
                            setTimeout(() => button.text(originalText), 2000);
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function(xhr) {
                        console.error("Cart error:", xhr.responseText);
                        alert('Error adding to cart. Please try again.');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            });

            // Load Cart
            function loadCart() {
                $('#cartItems').html('<div class="loading-spinner"></div>');

                $.ajax({
                    url: '/cart',
                    method: 'GET',
                    success: function(response) {
                        if (response.status === 'success') {
                            // Update cart items count
                            $('#cartItemsCount').text(`${response.count} ${response.count === 1 ? 'item' : 'items'}`);

                            if (response.cart.length === 0) {
                                $('#cartItems').html(`
                                    <div class="empty-cart">
                                        <i class="fas fa-shopping-cart"></i>
                                        <p>Your cart is empty</p>
                                        <button class="continue-shopping-btn" onclick="$('#cartModal').removeClass('active')">
                                            <i class="fas fa-arrow-left"></i> Continue Shopping
                                        </button>
                                    </div>
                                `);
                                $('#bookTests').prop('disabled', true);
                                $('#cartTotal').text('₹0.00');
                                $('.cart-footer').css('opacity', '0.5');
                            } else {
                                $('.cart-footer').css('opacity', '1');
                                $('#cartItems').empty();

                                response.cart.forEach(item => {
                                    const itemTotal = item.price * item.quantity;
                                    $('#cartItems').append(`
                                        <div class="cart-item" data-test-id="${item.test_id}">
                                            <div class="cart-item-info">
                                                <div class="cart-item-name">${item.name}</div>
                                                <div class="cart-item-details">
                                                    <span>₹${item.price.toFixed(2)} each</span>
                                                    <span>•</span>
                                                    <span>Qty: ${item.quantity}</span>
                                                </div>
                                            </div>
                                            <div class="cart-item-controls">
                                                <div class="quantity-controls">
                                                    <button class="quantity-btn minus-btn" data-test-id="${item.test_id}">-</button>
                                                    <span class="quantity-value">${item.quantity}</span>
                                                    <button class="quantity-btn plus-btn" data-test-id="${item.test_id}">+</button>
                                                </div>
                                                <button class="remove-item-btn" data-test-id="${item.test_id}" title="Remove item">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <div class="cart-item-price">₹${itemTotal.toFixed(2)}</div>
                                        </div>
                                    `);
                                });
                                $('#bookTests').prop('disabled', false);
                            }
                            $('#cartTotal').text(`₹${response.total.toFixed(2)}`);
                            $('#cart-count').text(response.count);
                        } else {
                            $('#cartItems').html(`
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <p>Error loading cart</p>
                                </div>
                            `);
                        }
                    },
                    error: function() {
                        $('#cartItems').html(`
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                <p>Error loading cart</p>
                            </div>
                        `);
                    }
                });
            }

            // Open Cart Modal
            $('#cartIcon').click(function() {
                $('#cartModal').addClass('active');
                loadCart();
            });

            // Book Tests
           $('#bookTests').click(function() {
        $.get('/cart', function(response) {
            if (response.status === 'success' && response.cart.length > 0) {
            $.ajax({
                url: '/book_tests',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    tests: response.cart,
                    total_amount: response.total
                }),
                success: function(orderData) {
                    if (orderData.status === 'error') {
                        alert(orderData.message);
                        return;
                    }

                    var options = {
                        key: orderData.key,
                        amount: orderData.amount,
                        currency: "INR",
                        name: "CVBioLabs",
                        description: "Test Booking Payment",
                        order_id: orderData.order_id,
                        handler: function(response) {
                            $.ajax({
                                url: '/verify_payment',
                                method: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    payment_id: response.razorpay_payment_id,
                                    order_id: response.razorpay_order_id,
                                    signature: response.razorpay_signature
                                }),
                                success: function(result) {
                                    if (result.status === 'success') {
                                        // Send confirmation email after successful payment
                                        $.ajax({
                                            url: '/send_confirmation_email',
                                            method: 'POST',
                                            contentType: 'application/json',
                                            data: JSON.stringify({
                                                email: "{{ email }}",
                                                name: "{{ profile.first_name }} {{ profile.last_name }}",
                                                amount: orderData.amount / 100 // Razorpay amount is in paise
                                            }),
                                            complete: function() {
                                                alert('Payment successful! Your tests have been booked. A confirmation email has been sent.');
                                                $('#cartModal').removeClass('active');
                                                window.location.reload();
                                            }
                                        });
                                    } else {
                                        alert('Payment verification failed: ' + result.message);
                                    }
                                },
                                error: function(xhr) {
                                    console.error("Payment verification error:", xhr.responseText);
                                    alert('Payment verification failed. Please contact support.');
                                }
                            });
                        },
                        prefill: {
                            name: "{{ profile.first_name }} {{ profile.last_name }}",
                            email: "{{ email }}",
                            contact: "{{ profile.phone }}"
                        },
                        theme: {
                            color: "#f47c20"
                        },
                        modal: {
                            ondismiss: function() {
                                alert('Payment cancelled');
                            }
                        }
                    };

                    var rzp = new Razorpay(options);
                    rzp.on('payment.failed', function(response) {
                        alert('Payment failed: ' + response.error.description);
                    });
                    rzp.open();
                },
                error: function(xhr) {
                    alert('Error creating payment order. Please try again.');
                }
            });
        } else {
            alert('Your cart is empty');
        }
    });
});
            // Close Cart Modal
            $('#closeCart').click(function() {
                $('#cartModal').removeClass('active');
            });

            // Continue Shopping Button
            $(document).on('click', '#continueShopping', function() {
                $('#cartModal').removeClass('active');
            });

            // Open Reset Password Modal
            $('.open-reset-modal').click(function() {
                $('#resetPasswordModal').addClass('active');
            });

            // Close Reset Password Modal
            $('.close-reset-modal').click(function() {
                $('#resetPasswordModal').removeClass('active');
            });

            // Reset Password Form Submission
            $('#resetPasswordForm').submit(function(e) {
                e.preventDefault();
                $.ajax({
                    url: '/change_password',
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        alert(response.message);
                        if (response.status === 'success') {
                            $('#resetPasswordModal').removeClass('active');
                        }
                    },
                    error: function() {
                        alert('Error changing password');
                    }
                });
            });

            // Enhanced Edit Profile Form Submission
            $('#editProfileForm').submit(function(e) {
                e.preventDefault();

                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();

                // Show loading state
                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Saving...');

                // Validate form
                const name = $('input[name="name"]').val().trim();
                const email = $('input[name="email"]').val().trim();
                const phone = $('input[name="phone"]').val().trim();

                if (!name || !email || !phone) {
                    showNotification('Please fill in all required fields', 'error');
                    submitBtn.prop('disabled', false).html(originalText);
                    return;
                }

                // Email validation
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    showNotification('Please enter a valid email address', 'error');
                    submitBtn.prop('disabled', false).html(originalText);
                    return;
                }

                // Phone validation
                const phoneRegex = /^[0-9]{10}$/;
                if (!phoneRegex.test(phone.replace(/\D/g, ''))) {
                    showNotification('Please enter a valid 10-digit phone number', 'error');
                    submitBtn.prop('disabled', false).html(originalText);
                    return;
                }

                $.ajax({
                    url: '/edit_profile',
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.status === 'success') {
                            showNotification('Profile updated successfully!', 'success');
                            setTimeout(() => {
                                window.location.reload();
                            }, 1500);
                        } else {
                            showNotification(response.message || 'Error updating profile', 'error');
                            submitBtn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function(xhr) {
                        console.error('Profile update error:', xhr.responseText);
                        showNotification('Error updating profile. Please try again.', 'error');
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Enhanced Cancel Edit Profile
            $('.cancel-edit').click(function() {
                if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                    $('.section').removeClass('active');
                    $('#profile').addClass('active');
                    $('.nav-item').removeClass('active');
                    $('.nav-item[data-section="profile"]').addClass('active');
                }
            });

            // Enhanced notification system
            function showNotification(message, type = 'info') {
                // Remove existing notifications
                $('.notification').remove();

                const notification = $(`
                    <div class="notification notification-${type}" style="
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                        color: white;
                        padding: 1rem 1.5rem;
                        border-radius: 12px;
                        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                        z-index: 9999;
                        font-weight: 500;
                        max-width: 400px;
                        animation: slideInRight 0.3s ease;
                    ">
                        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}" style="margin-right: 0.5rem;"></i>
                        ${message}
                    </div>
                `);

                $('body').append(notification);

                setTimeout(() => {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 4000);
            }

            // Copy Referral Code
            $('.copy-referral').click(function() {
                const code = $('#referralCode').val();
                navigator.clipboard.writeText(code).then(() => {
                    showNotification('Referral code copied to clipboard!', 'success');
                }).catch(() => {
                    showNotification('Failed to copy referral code', 'error');
                });
            });

            // Cancel Booking
            $('.cancel-booking').click(function() {
                const bookingId = $(this).data('booking-id');
                if (confirm('Are you sure you want to cancel this booking?')) {
                    $.ajax({
                        url: `/cancel_booking/${bookingId}`,
                        method: 'POST',
                        success: function(response) {
                            alert(response.message);
                            if (response.status === 'success') {
                                window.location.reload();
                            }
                        },
                        error: function() {
                            alert('Error cancelling booking');
                        }
                    });
                }
            });

            // Update quantity in cart
            $(document).on('click', '.quantity-btn', function() {
                const testId = $(this).data('test-id');
                const action = $(this).hasClass('plus-btn') ? 'increment' : 'decrement';

                $.ajax({
                    url: `/cart/update/${testId}`,
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ action: action }),
                    success: function(response) {
                        if (response.status === 'success') {
                            loadCart(); // Refresh cart display
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function() {
                        alert('Error updating cart');
                    }
                });
            });

            // Remove item from cart
            $(document).on('click', '.remove-item-btn', function() {
                const testId = $(this).data('test-id');

                if (confirm('Are you sure you want to remove this item from your cart?')) {
                    $.ajax({
                        url: `/cart/update/${testId}`,
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ action: 'remove' }),
                        success: function(response) {
                            if (response.status === 'success') {
                                loadCart();
                            } else {
                                alert(response.message);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Remove cart item error:', {xhr, status, error});
                            alert('Error removing item from cart');
                        }
                    });
                }
            });

            // Enhanced Show Edit Profile form
            $('#editProfileBtn').click(function() {
                const button = $(this);
                const originalText = button.html();

                // Show loading state
                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Loading...');

                setTimeout(() => {
                    $('.section').removeClass('active');
                    $('#edit-profile').addClass('active').show();
                    $('.nav-item').removeClass('active');

                    // Scroll to top of edit form
                    $('html, body').animate({
                        scrollTop: $('#edit-profile').offset().top - 100
                    }, 500);

                    // Reset button
                    button.prop('disabled', false).html(originalText);

                    // Focus on first input
                    $('#edit-profile input[name="name"]').focus();
                }, 300);
            });
        });
    </script>
</body>
</html>