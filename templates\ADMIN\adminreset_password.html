{% extends "ADMIN/adminbase.html" %}

{% block title %}Password Management - Admin Dashboard{% endblock %}

{% block page_title %}Password Management{% endblock %}

{% block extra_css %}
<meta name="csrf-token" content="{{ csrf_token }}">
<style>
    .password-strength {
        height: 5px;
        margin-top: 5px;
        transition: all 0.3s;
    }

    .required::after {
        content: " *";
        color: var(--danger);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Password Reset Cards -->
    <div class="row g-4">
        <!-- Staff Password Reset -->
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-tie me-2"></i>Receptionist Password Reset
                    </h5>
                </div>
                <div class="card-body">
                    <form id="staffResetForm" onsubmit="return handlePasswordReset('staff', event)">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <div class="mb-3">
                            <label class="form-label required">Receptionist ID</label>
                            <input type="text" class="form-control" name="staff_id" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">Email</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="new_password" required onkeyup="checkPasswordStrength(this)">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this.previousElementSibling)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength w-100 rounded"></div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">Confirm Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="confirm_password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this.previousElementSibling)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Doctor Password Reset -->
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-md me-2"></i>Doctor Password Reset
                    </h5>
                </div>
                <div class="card-body">
                    <form id="doctorResetForm" onsubmit="return handlePasswordReset('doctor', event)">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <div class="mb-3">
                            <label class="form-label required">Doctor ID</label>
                            <input type="text" class="form-control" name="doctor_id" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">License Number</label>
                            <input type="text" class="form-control" name="license_number" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="new_password" required onkeyup="checkPasswordStrength(this)">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this.previousElementSibling)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength w-100 rounded"></div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">Confirm Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="confirm_password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this.previousElementSibling)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Pickup Agent Password Reset -->
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-truck me-2"></i>Pickup Agent Password Reset
                    </h5>
                </div>
                <div class="card-body">
                    <form id="agentResetForm" onsubmit="return handlePasswordReset('agent', event)">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <div class="mb-3">
                            <label class="form-label required">Agent ID</label>
                            <input type="text" class="form-control" name="agent_id" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">Phone Number</label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="new_password" required onkeyup="checkPasswordStrength(this)">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this.previousElementSibling)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength w-100 rounded"></div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">Confirm Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="confirm_password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this.previousElementSibling)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-info w-100">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Password Requirements Card -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-shield-alt me-2"></i>Password Requirements
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check-circle text-success me-2"></i>Minimum 8 characters</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>At least one uppercase letter</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>At least one lowercase letter</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check-circle text-success me-2"></i>At least one number</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>At least one special character</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>No common dictionary words</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Password strength checker
    function checkPasswordStrength(input) {
        const password = input.value;
        const strengthBar = input.nextElementSibling;
        let strength = 0;

        if (password.length >= 8) strength += 20;
        if (password.match(/[A-Z]/)) strength += 20;
        if (password.match(/[a-z]/)) strength += 20;
        if (password.match(/[0-9]/)) strength += 20;
        if (password.match(/[^A-Za-z0-9]/)) strength += 20;

        strengthBar.style.width = strength + '%';
        
        if (strength <= 40) {
            strengthBar.style.backgroundColor = '#dc3545';
        } else if (strength <= 80) {
            strengthBar.style.backgroundColor = '#8eb5c4';
        } else {
            strengthBar.style.backgroundColor = '#506da7';
        }
    }

    // Toggle password visibility
    function togglePasswordVisibility(input) {
        const icon = input.nextElementSibling.querySelector('i');
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Handle password reset with AJAX
    function handlePasswordReset(userType, event) {
        event.preventDefault();
        const form = event.target;
        const password = form.querySelector('input[name="new_password"]').value;
        const confirmPassword = form.querySelector('input[name="confirm_password"]').value;

        // Client-side validation
        if (password !== confirmPassword) {
            showAlert('Passwords do not match!', 'danger');
            return false;
        }

        if (!isPasswordStrong(password)) {
            showAlert('Password does not meet the requirements!', 'danger');
            return false;
        }

        // Prepare form data
        const formData = new FormData(form);

        // Send AJAX request to Flask
        fetch(`/admin/reset-password/${userType}`, {
            method: 'POST',
            headers: {
                'X-CSRF-Token': csrfToken
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                form.reset();
                form.querySelectorAll('.password-strength').forEach(bar => bar.style.width = '0');
            } else {
                throw new Error(data.error || 'Failed to reset password');
            }
        })
        .catch(error => {
            showAlert('Error resetting password: ' + error.message, 'danger');
        });

        return false;
    }

    // Password strength validator
    function isPasswordStrong(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        return (
            password.length >= minLength &&
            hasUpperCase &&
            hasLowerCase &&
            hasNumbers &&
            hasSpecialChar
        );
    }

    // Show alert message
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3`;
        alertDiv.role = 'alert';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.body.appendChild(alertDiv);
        setTimeout(() => alertDiv.remove(), 3000);
    }
</script>
{% endblock %}