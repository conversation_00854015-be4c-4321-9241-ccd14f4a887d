<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickup Agent Dashboard - CVBiolabs</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-orange: #f58220;
            --deep-blue: #003865;
            --bright-blue: #007dc5;
            --light-bg: #f0f7fb;
            --white: #ffffff;
            --text-dark: #1a1a1a;
            --text-light: #6b7280;
            --success: #10b981;
            --gradient-primary: linear-gradient(135deg, var(--deep-blue) 0%, var(--bright-blue) 100%);
            --gradient-accent: linear-gradient(135deg, var(--primary-orange) 0%, #ff6b35 100%);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
            background: var(--light-bg);
            opacity: 0;
            animation: fadeInBody 0.5s ease-out forwards;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-orange);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #e07020;
        }

        @keyframes fadeInBody {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* Loading Spinner */
        .loading-spinner {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            display: none;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--gray-200);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Header with Glass Effect */
        .top-nav {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .top-nav.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
        }

        .logo img {
            width: 70px;
            height: 70px;
            border-radius: 8px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .logo-text {
            font-family: 'Poppins', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 500;
            position: relative;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            background: rgba(245, 130, 32, 0.1);
            color: var(--primary-orange);
        }

        .nav-menu a.active {
            background: rgba(245, 130, 32, 0.1);
            color: var(--primary-orange);
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            transition: all 0.3s ease;
        }

        .status-badge.status-Available {
            background-color: var(--success);
            color: white;
            animation: pulse-green 2s infinite;
        }

        .status-badge.status-Busy {
            background-color: var(--primary-orange);
            color: white;
            animation: pulse-orange 2s infinite;
        }

        .status-badge.status-Inactive {
            background-color: var(--text-light);
            color: white;
            animation: pulse-gray 2s infinite;
        }

        @keyframes pulse-green {
            0%, 100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
        }

        @keyframes pulse-orange {
            0%, 100% { box-shadow: 0 0 0 0 rgba(245, 130, 32, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(245, 130, 32, 0); }
        }

        @keyframes pulse-gray {
            0%, 100% { box-shadow: 0 0 0 0 rgba(107, 114, 128, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(107, 114, 128, 0); }
        }

        /* Custom Dropdown Container */
        .status-dropdown {
            position: relative;
            display: inline-block;
        }

        .status-select {
            padding: 0.75rem 3rem 0.75rem 1.5rem;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 50px;
            background: var(--white);
            color: var(--text-dark);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.9rem;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            position: relative;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f58220' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 1.2em;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .status-select:hover {
            border-color: var(--primary-orange);
            box-shadow: 0 8px 25px rgba(245, 130, 32, 0.2);
            transform: translateY(-2px);
        }

        .status-select:focus {
            outline: none;
            border-color: var(--primary-orange);
            box-shadow: 0 0 0 3px rgba(245, 130, 32, 0.1), 0 8px 25px rgba(245, 130, 32, 0.2);
            transform: translateY(-2px);
        }

        /* Animated dropdown arrow */
        .status-dropdown::after {
            content: '';
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid var(--primary-orange);
            transition: transform 0.3s ease;
            pointer-events: none;
            z-index: 1;
        }

        .status-dropdown:hover::after {
            transform: translateY(-50%) rotate(180deg);
        }

        /* Status-specific styling */
        .status-select[data-status="Available"] {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
            border-color: rgba(16, 185, 129, 0.3);
            color: var(--success);
            animation: pulse-green-subtle 3s infinite;
        }

        .status-select[data-status="Busy"] {
            background: linear-gradient(135deg, rgba(245, 130, 32, 0.1), rgba(245, 130, 32, 0.05));
            border-color: rgba(245, 130, 32, 0.3);
            color: var(--primary-orange);
            animation: pulse-orange-subtle 3s infinite;
        }

        .status-select[data-status="Inactive"] {
            background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(107, 114, 128, 0.05));
            border-color: rgba(107, 114, 128, 0.3);
            color: var(--text-light);
            animation: pulse-gray-subtle 3s infinite;
        }

        @keyframes pulse-green-subtle {
            0%, 100% { box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1); }
            50% { box-shadow: 0 4px 20px rgba(16, 185, 129, 0.2); }
        }

        @keyframes pulse-orange-subtle {
            0%, 100% { box-shadow: 0 4px 15px rgba(245, 130, 32, 0.1); }
            50% { box-shadow: 0 4px 20px rgba(245, 130, 32, 0.2); }
        }

        @keyframes pulse-gray-subtle {
            0%, 100% { box-shadow: 0 4px 15px rgba(107, 114, 128, 0.1); }
            50% { box-shadow: 0 4px 20px rgba(107, 114, 128, 0.2); }
        }

        /* Enhanced dropdown options styling */
        .status-select option {
            padding: 1rem;
            background: var(--white);
            color: var(--text-dark);
            font-weight: 600;
            border: none;
            transition: all 0.3s ease;
        }

        .status-select option:hover {
            background: var(--light-bg);
        }

        .status-select option[value="Available"] {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
            color: var(--success);
        }

        .status-select option[value="Busy"] {
            background: linear-gradient(135deg, rgba(245, 130, 32, 0.1), rgba(245, 130, 32, 0.05));
            color: var(--primary-orange);
        }

        .status-select option[value="Inactive"] {
            background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(107, 114, 128, 0.05));
            color: var(--text-light);
        }

        /* Dropdown opening animation */
        .status-select:focus {
            animation: dropdownOpen 0.3s ease-out;
        }

        @keyframes dropdownOpen {
            0% {
                transform: scale(1) rotateX(0deg);
            }
            50% {
                transform: scale(1.02) rotateX(-5deg);
            }
            100% {
                transform: scale(1) rotateX(0deg);
            }
        }

        /* Smooth status transition effect */
        .status-transition {
            animation: statusChange 0.5s ease-out;
        }

        @keyframes statusChange {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(0.95);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .logout-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.9rem;
            background: var(--gradient-accent);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Main Content */
        .main-content {
            padding: 120px 2rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
            min-height: 100vh;
        }

        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: clamp(2rem, 4vw, 2.5rem);
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 2rem;
            text-align: center;
        }

        /* Collection Cards */
        .collections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .collection-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
            opacity: 0;
            transform: translateY(20px);
            animation: slideInUp 0.6s ease-out forwards;
        }

        .collection-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
        }

        .collection-card:nth-child(1) { animation-delay: 0.1s; }
        .collection-card:nth-child(2) { animation-delay: 0.2s; }
        .collection-card:nth-child(3) { animation-delay: 0.3s; }
        .collection-card:nth-child(4) { animation-delay: 0.4s; }
        .collection-card:nth-child(5) { animation-delay: 0.5s; }
        .collection-card:nth-child(6) { animation-delay: 0.6s; }

        .collection-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .customer-name {
            font-family: 'Poppins', sans-serif;
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .collection-info {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-light);
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .info-item i {
            color: var(--primary-orange);
            width: 18px;
            font-size: 1rem;
        }

        .card-actions {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-accent);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .status-pending {
            background-color: rgba(245, 130, 32, 0.1);
            color: var(--primary-orange);
        }

        .status-completed {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .status-collected {
            background-color: rgba(0, 125, 197, 0.1);
            color: var(--bright-blue);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, var(--light-bg) 0%, white 100%);
            border-radius: 20px;
            color: var(--text-light);
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: var(--shadow-md);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .empty-state p {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 500;
            color: var(--text-light);
        }

        /* Section visibility with animations */
        .section {
            display: none;
            opacity: 0;
            transform: translateY(20px);
        }

        .section.active {
            display: block;
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Profile styles */
        .profile-form {
            max-width: 600px;
            margin: 0 auto;
            background: var(--white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
        }

        .profile-picture-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 2rem;
        }

        .profile-picture {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 1rem;
            cursor: pointer;
            border: 4px solid var(--primary-color);
        }

        .profile-picture img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--secondary-color);
            font-weight: 600;
            font-size: 0.875rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            background: var(--gray-100);
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        /* Mobile Navigation */
        .mobile-nav-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-600);
            cursor: pointer;
            padding: 0.5rem;
        }

        .mobile-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--white);
            border-top: 1px solid var(--gray-200);
            padding: 0.75rem;
            z-index: 1000;
            box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .mobile-nav-menu {
            display: flex;
            justify-content: space-around;
            align-items: center;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
            padding: 0.5rem;
            text-decoration: none;
            color: var(--gray-600);
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 8px;
            min-width: 60px;
        }

        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            color: var(--primary-color);
            background-color: rgba(255, 107, 53, 0.1);
        }

        .mobile-nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .collections-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .top-nav {
                padding: 1rem;
            }

            .nav-menu {
                display: none;
            }

            .mobile-nav {
                display: block;
            }

            .nav-right {
                gap: 0.5rem;
                flex-direction: column;
                align-items: flex-end;
            }

            .status-select {
                padding: 0.6rem 2.5rem 0.6rem 1rem;
                font-size: 0.8rem;
                background-size: 1em;
                background-position: right 0.75rem center;
            }

            .status-dropdown::after {
                right: 0.75rem;
                border-top: 6px solid var(--primary-orange);
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
            }

            .logout-btn {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }

            .collections-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .main-content {
                padding: 100px 1rem 120px;
            }

            .page-title {
                font-size: 1.75rem;
                margin-bottom: 1.5rem;
            }

            .collection-card {
                padding: 1.5rem;
            }

            .customer-name {
                font-size: 1.1rem;
            }

            .info-item {
                font-size: 0.85rem;
            }

            .btn {
                padding: 0.65rem 1.25rem;
                font-size: 0.85rem;
            }

            .card-actions {
                flex-direction: column;
                gap: 0.75rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .profile-form {
                padding: 1.5rem;
            }

            .profile-picture {
                width: 100px;
                height: 100px;
            }

            .logo-text {
                font-size: 1.25rem;
            }

            .logo img {
                width: 50px;
                height: 50px;
            }
        }

        @media (max-width: 480px) {
            .top-nav {
                padding: 0.75rem;
            }

            .nav-right {
                flex-direction: column;
                gap: 0.25rem;
                align-items: flex-end;
            }

            .status-badge {
                font-size: 0.65rem;
                padding: 0.25rem 0.5rem;
            }

            .collection-card {
                padding: 0.75rem;
            }

            .page-title {
                font-size: 1.25rem;
            }

            .customer-name {
                font-size: 0.95rem;
            }

            .info-item {
                font-size: 0.75rem;
            }

            .btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.75rem;
            }

            .mobile-nav-item {
                font-size: 0.65rem;
                min-width: 50px;
            }

            .mobile-nav-item i {
                font-size: 1rem;
            }
        }

    </style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner"></div>
    </div>

    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-left">
            <a href="#" class="logo">
                <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBiolabs Logo">
                <span class="logo-text">CVBIOLABS</span>
            </a>
            <ul class="nav-menu">
                <li><a href="{{ url_for('pickup_agent', section='dashboard', agent_id=agent.id) }}" class="{% if active_section == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-home"></i> Dashboard
                </a></li>
                <li><a href="{{ url_for('pickup_agent', section='collections', agent_id=agent.id) }}" class="{% if active_section == 'collections' %}active{% endif %}">
                    <i class="fas fa-tasks"></i> Collections
                </a></li>
                <li><a href="{{ url_for('pickup_agent', section='history', agent_id=agent.id) }}" class="{% if active_section == 'history' %}active{% endif %}">
                    <i class="fas fa-history"></i> History
                </a></li>
                <li><a href="{{ url_for('pickup_agent', section='profile', agent_id=agent.id) }}" class="{% if active_section == 'profile' %}active{% endif %}">
                    <i class="fas fa-user"></i> Profile
                </a></li>
            </ul>
        </div>
        <div class="nav-right">
            <form action="{{ url_for('update_pickup_agent_status') }}" method="POST" style="display: inline;">
                <div class="status-dropdown">
                    <select name="status" class="status-select" data-status="{{ agent.status }}" onchange="this.form.submit()">
                        <option value="Available" {% if agent.status == 'Available' %}selected{% endif %}>🟢 Available</option>
                        <option value="Busy" {% if agent.status == 'Busy' %}selected{% endif %}>🟠 Busy</option>
                        <option value="Inactive" {% if agent.status == 'Inactive' %}selected{% endif %}>⚫ Inactive</option>
                    </select>
                </div>
            </form>
            <span class="status-badge status-{{ agent.status }}">{{ agent.status }}</span>
            <a href="{{ url_for('pickup_logout') }}" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav">
        <ul class="mobile-nav-menu">
            <li>
                <a href="{{ url_for('pickup_agent', section='dashboard', agent_id=agent.id) }}"
                   class="mobile-nav-item {% if active_section == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            <li>
                <a href="{{ url_for('pickup_agent', section='collections', agent_id=agent.id) }}"
                   class="mobile-nav-item {% if active_section == 'collections' %}active{% endif %}">
                    <i class="fas fa-tasks"></i>
                    <span>Collections</span>
                </a>
            </li>
            <li>
                <a href="{{ url_for('pickup_agent', section='history', agent_id=agent.id) }}"
                   class="mobile-nav-item {% if active_section == 'history' %}active{% endif %}">
                    <i class="fas fa-history"></i>
                    <span>History</span>
                </a>
            </li>
            <li>
                <a href="{{ url_for('pickup_agent', section='profile', agent_id=agent.id) }}"
                   class="mobile-nav-item {% if active_section == 'profile' %}active{% endif %}">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% if show_today %}
        <!-- Today's Collections Section -->
        <div class="section {% if active_section == 'dashboard' %}active{% endif %}">
            <h1 class="page-title">Today's Collections</h1>
            <div class="collections-grid">
                {% if tasks %}
                    {% for task in tasks %}
                    <div class="collection-card">
                        <div class="card-header">
                            <div>
                                <h3 class="customer-name">{{ task.customer_name }}</h3>
                                <span class="status-badge {% if task.collection_status == 'Pending' %}status-pending{% elif task.collection_status == 'Collected' %}status-collected{% else %}status-completed{% endif %}">
                                    {{ task.collection_status }}
                                </span>
                            </div>
                        </div>
                        <div class="collection-info">
                            <div class="info-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ task.address_line1 }}, {{ task.city }}, {{ task.state }}</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-calendar"></i>
                                <span>{{ task.formatted_date if task.formatted_date else 'Not Available' }}</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>{{ task.formatted_time }}</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-vial"></i>
                                <span>{{ task.TestName }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-calendar-check"></i>
                        <p>No collections scheduled for today</p>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Collections Section -->
        <div class="section {% if active_section == 'collections' %}active{% endif %}">
            <h1 class="page-title">Upcoming Collections</h1>
            <div class="collections-grid">
                {% if tasks %}
                    {% for task in tasks %}
                    <div class="collection-card">
                        <div class="card-header">
                            <div>
                                <h3 class="customer-name">{{ task.customer_name }}</h3>
                                <span class="status-badge {% if task.collection_status == 'Pending' %}status-pending{% elif task.collection_status == 'Collected' %}status-collected{% else %}status-completed{% endif %}">
                                    {{ task.collection_status }}
                                </span>
                            </div>
                        </div>
                        <div class="collection-info">
                            <div class="info-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ task.address_line1 }}, {{ task.city }}, {{ task.state }}</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-calendar"></i>
                                <span>{{ task.formatted_date }}</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>{{ task.formatted_time }}</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-vial"></i>
                                <span>{{ task.TestName }}</span>
                            </div>
                        </div>
                        <div class="card-actions">
                            {% if task.collection_status == 'Pending' %}
                            <form action="{{ url_for('update_pickup_status', task_id=task.id) }}" method="POST">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                                <input type="hidden" name="status" value="Collected">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check"></i> Collect Sample
                                </button>
                            </form>
                            {% elif task.collection_status == 'Collected' %}
                            <form action="{{ url_for('update_pickup_status', task_id=task.id) }}" method="POST">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                                <input type="hidden" name="status" value="Delivered">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-truck"></i> Mark Delivered
                                </button>
                            </form>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-calendar"></i>
                        <p>No upcoming collections</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- History Section -->
        <div class="section {% if active_section == 'history' %}active{% endif %}">
            <h1 class="page-title">Collection History</h1>
            <div class="collections-grid">
                {% for item in history %}
                <div class="collection-card">
                    <div class="card-header">
                        <div>
                            <h3 class="customer-name">{{ item.customer_name }}</h3>
                            <span class="status-badge status-completed">Delivered</span>
                        </div>
                    </div>
                    <div class="collection-info">
                        <div class="info-item">
                            <i class="fas fa-vial"></i>
                            <span>{{ item.TestName }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>{{ item.formatted_date }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ item.formatted_time }}</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Profile Section -->
        <div class="section {% if active_section == 'profile' %}active{% endif %}">
            <h1 class="page-title">Agent Profile</h1>
            <div class="profile-form">
                <div class="profile-picture-container">
                    <div class="profile-picture">
                        <img src="{{ url_for('static', filename='images/default-avatar.png') }}" alt="Profile Picture">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Name</label>
                    <input type="text" class="form-input" value="{{ profile.name }}" readonly>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Total Collections</label>
                        <input type="text" class="form-input" value="{{ profile.total_collections }}" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Completed Collections</label>
                        <input type="text" class="form-input" value="{{ profile.completed_collections }}" readonly>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-input" value="{{ profile.email }}" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">Phone</label>
                    <input type="tel" class="form-input" value="{{ profile.phone }}" readonly>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Page load animations
        document.addEventListener('DOMContentLoaded', function() {
            // Hide loading spinner
            const loadingSpinner = document.getElementById('loadingSpinner');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Animate cards with staggered delay
            const cards = document.querySelectorAll('.collection-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // Highlight active navigation items
            const currentUrl = window.location.href;
            document.querySelectorAll('.nav-menu a, .mobile-nav-item').forEach(link => {
                if (link.href === currentUrl) {
                    link.classList.add('active');
                }
            });

            // Header scroll effect
            const header = document.querySelector('.top-nav');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        });

        // Show loading spinner on navigation
        document.querySelectorAll('a[href], form').forEach(element => {
            element.addEventListener('click', function(e) {
                // Don't show spinner for same page links or logout
                if (this.href && this.href !== window.location.href && !this.href.includes('logout')) {
                    const loadingSpinner = document.getElementById('loadingSpinner');
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'block';
                    }
                }
            });
        });

        // Enhanced status select functionality
        const statusSelect = document.querySelector('.status-select');
        if (statusSelect) {
            // Set initial styling based on current status
            const currentStatus = statusSelect.getAttribute('data-status');
            statusSelect.setAttribute('data-status', currentStatus);

            statusSelect.addEventListener('change', function() {
                const select = this;
                const status = select.value;

                // Update data attribute for styling
                select.setAttribute('data-status', status);

                // Add a smooth transition effect
                select.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    select.style.transform = 'scale(1)';
                }, 150);

                // Show loading spinner with delay for smooth transition
                setTimeout(() => {
                    const loadingSpinner = document.getElementById('loadingSpinner');
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'block';
                    }
                }, 200);
            });

            // Add hover effects for dropdown arrow
            const dropdown = document.querySelector('.status-dropdown');
            if (dropdown) {
                statusSelect.addEventListener('focus', function() {
                    dropdown.style.transform = 'scale(1.02)';
                });

                statusSelect.addEventListener('blur', function() {
                    dropdown.style.transform = 'scale(1)';
                });

                // Add ripple effect on click
                statusSelect.addEventListener('mousedown', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('dropdown-ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            }
        }

        // Add ripple effect styles for dropdown
        const dropdownStyle = document.createElement('style');
        dropdownStyle.textContent = `
            .status-select {
                position: relative;
                overflow: hidden;
            }
            .dropdown-ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(245, 130, 32, 0.3);
                transform: scale(0);
                animation: dropdown-ripple-animation 0.6s linear;
                pointer-events: none;
            }
            @keyframes dropdown-ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(dropdownStyle);

        // Smooth scroll for mobile navigation
        document.querySelectorAll('.mobile-nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                // Add active state animation
                document.querySelectorAll('.mobile-nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Button click animations
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function(e) {
                // Create ripple effect
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Add ripple effect styles
        const style = document.createElement('style');
        style.textContent = `
            .btn {
                position: relative;
                overflow: hidden;
            }
            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }
            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all cards for scroll animations
        document.querySelectorAll('.collection-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>