<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>CV BIOLABS - Doctor Dashboard</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            /* Brand Colors - Consistent with Home Page */
            --primary-orange: #f58220;
            --deep-blue: #003865;
            --bright-blue: #007dc5;
            --light-bg: #f0f7fb;
            --white: #ffffff;
            --text-dark: #1a1a1a;
            --text-light: #6b7280;

            /* UI Colors */
            --background: #f8fafc;
            --surface: #ffffff;
            --surface-hover: #f1f5f9;
            --border: #e2e8f0;
            --border-light: #f1f5f9;
            --text-primary: #1a1a1a;
            --text-secondary: #6b7280;
            --text-muted: #94a3b8;

            /* Status Colors */
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #007dc5;

            /* Gradients - Consistent with Home Page */
            --gradient-primary: linear-gradient(135deg, #003865 0%, #007dc5 100%);
            --gradient-accent: linear-gradient(135deg, #f58220 0%, #ff6b35 100%);

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);

            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
            background: linear-gradient(135deg, var(--light-bg) 0%, rgba(240, 247, 251, 0.5) 50%, white 100%);
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-orange);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #e07020;
        }

        /* Icon Styles - Ensure FontAwesome icons are properly displayed */
        .fas, .far, .fab {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", "FontAwesome" !important;
            font-weight: 900;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Ensure icons have proper spacing and alignment */
        i.fas, i.far, i.fab {
            display: inline-block;
            vertical-align: middle;
        }
        /* Header with Glass Effect */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo img {
            width: 70px;
            height: 70px;
            border-radius: 8px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .logo-text {
            font-family: 'Poppins', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2.5rem;
            align-items: center;
            margin: 0;
            padding: 0;
        }

        .nav-links li {
            margin: 0;
        }

        .nav-links a {
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 500;
            position: relative;
            padding: 0.75rem 1.25rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
        }

        .nav-links a i {
            font-size: 1rem;
            width: 16px;
            text-align: center;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: rgba(245, 130, 32, 0.1);
            color: var(--primary-orange);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-shrink: 0;
        }

        /* Modern Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .btn i {
            font-size: 0.9rem;
            width: 16px;
            text-align: center;
            display: inline-block;
        }

        .btn-primary {
            background: var(--gradient-accent);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 130, 32, 0.3);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 130, 32, 0.4);
        }

        .btn-secondary {
            background: var(--gradient-primary);
            color: var(--white);
            box-shadow: 0 4px 15px rgba(0, 56, 101, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 56, 101, 0.4);
        }

        .btn-outline {
            background: transparent;
            color: var(--text-dark);
            border: 2px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .btn-outline:hover {
            background: rgba(245, 130, 32, 0.1);
            border-color: var(--primary-orange);
            color: var(--primary-orange);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 130, 32, 0.2);
        }

        /* Legacy button classes for compatibility */
        /* Search Button Special Styling */
        .search-btn {
            background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            min-width: 120px;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        /* Filter Button Active States */
        .filter-btn-pending.active {
            background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .filter-btn-verified.active {
            background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .filter-btn-all.active {
            background: linear-gradient(135deg, var(--info) 0%, #0369a1 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        /* Legacy button classes for compatibility */
        .btn.bg-blue-900, .btn.bg-blue-800 {
            background: var(--gradient-primary);
            color: var(--white);
        }

        .btn.bg-orange-500, .btn.bg-orange-600 {
            background: var(--gradient-accent);
            color: var(--white);
        }
        /* Card Styles */
        .card {
            background: var(--surface);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
            border-color: var(--primary-orange);
        }

        .stat-card {
            background: var(--surface);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
            transition: all 0.3s ease;
            padding: var(--spacing-xl);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
        }

        .stat-card:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-4px) scale(1.02);
            border-color: var(--primary-orange);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            font-family: 'Poppins', sans-serif;
            margin-bottom: var(--spacing-sm);
            color: var(--deep-blue);
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        /* Status Badge Styles */
        .status {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: 9999px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-verified {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-completed {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .status-unknown {
            background: rgba(148, 163, 184, 0.1);
            color: var(--text-muted);
            border: 1px solid rgba(148, 163, 184, 0.2);
        }
        /* Animation Styles */
        .notification {
            animation: slideInRight 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%) scale(0.8);
                opacity: 0;
            }
            to {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes buttonPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        .btn:active {
            animation: buttonPulse 0.2s ease-in-out;
        }

        /* Filter button hover animations */
        .filter-btn-pending:hover:not(.active) {
            background: rgba(245, 158, 11, 0.1);
            border-color: var(--warning);
            color: var(--warning);
        }

        .filter-btn-verified:hover:not(.active) {
            background: rgba(16, 185, 129, 0.1);
            border-color: var(--success);
            color: var(--success);
        }

        .filter-btn-all:hover:not(.active) {
            background: rgba(59, 130, 246, 0.1);
            border-color: var(--info);
            color: var(--info);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            z-index: 2000;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        .modal-content {
            position: relative;
            background: var(--surface);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            margin: auto;
            max-width: 600px;
            width: 100%;
            animation: modalSlideIn 0.3s ease-out;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px) scale(0.95);
                opacity: 0;
            }
            to {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        /* Enhanced status badge styling */
        .status-badge {
            display: inline-block;
            font-weight: 700;
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-verified {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .status-pending {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .status-unknown {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
        }

        /* Enhanced button hover effects */
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-outline:hover {
            background: #374151 !important;
            color: white !important;
            border-color: #374151 !important;
            box-shadow: 0 4px 12px rgba(55, 65, 81, 0.3) !important;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5) !important;
            transform: translateY(-2px);
        }

        /* Textarea focus effects */
        textarea:focus {
            outline: none;
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        /* Close button hover effect */
        .modal-header .btn-outline:hover {
            background: #ef4444;
            border-color: #ef4444;
            color: white;
            transform: scale(1.05);
        }

        /* Form Styles */
        .form-input,
        .filter-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border);
            border-radius: 50px;
            background: var(--surface);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .form-input:focus,
        .filter-input:focus {
            outline: none;
            border-color: var(--primary-orange);
            box-shadow: 0 0 0 3px rgba(245, 130, 32, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
            background: var(--white);
            transform: translateY(-1px);
        }

        .filter-input::placeholder {
            color: var(--text-muted);
            font-style: italic;
        }

        .form-label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        /* Section Styles */
        .reports-section,
        .profile-section {
            background: var(--surface);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
            padding: var(--spacing-2xl);
        }
        /* Main Content */
        .main-content {
            padding-top: 120px;
            min-height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
            animation: fadeIn 0.5s ease-out;
        }

        .section-header {
            margin-bottom: var(--spacing-xl);
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--deep-blue);
            margin-bottom: var(--spacing-xs);
            font-family: 'Poppins', sans-serif;
        }

        .section-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        /* Mobile Navigation */
        .mobile-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: var(--surface);
            border-top: 1px solid var(--border);
            z-index: 1100;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }

        .mobile-nav-items {
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: space-around;
            padding: 0 var(--spacing-md);
            margin: 0;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-sm);
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--radius-md);
            min-width: 60px;
            height: 100%;
            gap: 0.25rem;
        }

        .mobile-nav-item.active,
        .mobile-nav-item:hover {
            color: var(--primary-orange);
            background: rgba(245, 130, 32, 0.1);
            transform: translateY(-2px);
        }

        .mobile-nav-item i {
            font-size: 1.25rem;
            width: 20px;
            text-align: center;
            display: block;
        }

        .mobile-nav-item span {
            display: block;
            text-align: center;
        }

        /* Dashboard Welcome Section */
        .dashboard-welcome {
            background: linear-gradient(135deg, var(--light-bg) 0%, rgba(240, 247, 251, 0.5) 50%, white 100%);
            border-radius: 16px;
            padding: 2rem 1.5rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .dashboard-welcome::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -10%;
            width: 80%;
            height: 150%;
            background: linear-gradient(45deg, transparent 0%, rgba(0, 125, 197, 0.05) 50%, transparent 100%);
            transform: rotate(15deg);
            z-index: 1;
        }

        .welcome-content {
            position: relative;
            z-index: 2;
        }

        .welcome-text {
            font-family: 'Poppins', sans-serif;
            font-size: clamp(1.5rem, 3vw, 2rem);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.25rem;
            line-height: 1.3;
        }

        .welcome-subtitle {
            color: var(--text-light);
            font-size: 0.95rem;
            margin-bottom: 0;
            font-weight: 500;
        }
        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .mobile-nav {
                display: block;
            }

            .nav {
                padding: 1rem 1.5rem;
                gap: 1rem;
            }

            .logo {
                gap: 0.5rem;
            }

            .logo img {
                width: 50px;
                height: 50px;
            }

            .logo-text {
                font-size: 1.25rem;
            }

            .main-content {
                padding-left: 1rem;
                padding-right: 1rem;
                padding-top: 100px;
                padding-bottom: 100px; /* Space for mobile nav */
            }

            .dashboard-welcome {
                padding: 1.5rem 1rem;
            }

            .welcome-text {
                font-size: 1.4rem;
            }

            .section-title {
                font-size: 1.5rem;
            }

            .stat-card {
                padding: var(--spacing-lg);
            }

            .reports-section,
            .profile-section {
                padding: var(--spacing-lg);
            }

            .modal-content {
                margin: var(--spacing-md);
                max-width: calc(100vw - 2rem);
            }
        }

        @media (max-width: 640px) {
            .main-content {
                padding: var(--spacing-sm);
                padding-bottom: 90px;
            }

            .section-title {
                font-size: 1.25rem;
            }

            .stat-card {
                padding: var(--spacing-md);
            }

            .stat-number {
                font-size: 2rem;
            }

            .reports-section,
            .profile-section {
                padding: var(--spacing-md);
            }

            .btn {
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: 0.8rem;
            }
        }
        /* Table Styles */
        .reports-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--surface);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .reports-table th {
            background: var(--surface-hover);
            padding: var(--spacing-lg);
            text-align: left;
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border);
        }

        .reports-table td {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-light);
            vertical-align: middle;
        }

        .reports-table tr:hover {
            background: var(--surface-hover);
        }

        .reports-table tr:last-child td {
            border-bottom: none;
        }

        /* Mobile Table Styles */
        @media (max-width: 768px) {
            .reports-table {
                display: block;
                width: 100%;
                border: none;
                box-shadow: none;
                background: transparent;
            }

            .reports-table thead {
                display: none;
            }

            .reports-table tbody {
                display: flex;
                flex-direction: column;
                gap: var(--spacing-md);
            }

            .reports-table tr {
                display: flex;
                flex-direction: column;
                background: var(--surface);
                border-radius: var(--radius-lg);
                box-shadow: var(--shadow-sm);
                border: 1px solid var(--border-light);
                padding: var(--spacing-lg);
                margin-bottom: 0;
                transition: all 0.3s ease;
            }

            .reports-table tr:hover {
                box-shadow: var(--shadow-md);
                transform: translateY(-1px);
            }

            .reports-table td {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: var(--spacing-sm) 0;
                border: none;
                font-size: 0.875rem;
            }

            .reports-table td:before {
                content: attr(data-label);
                font-weight: 600;
                color: var(--deep-blue);
                min-width: 120px;
                display: inline-block;
            }

            .reports-table td:last-child {
                flex-direction: row;
                justify-content: flex-start;
                gap: var(--spacing-sm);
                margin-top: var(--spacing-sm);
                padding-top: var(--spacing-sm);
                border-top: 1px solid var(--border-light);
            }

            .reports-table td:last-child:before {
                display: none;
            }

            /* Filter Controls Styling */
            .filter-controls {
                background: white;
                border-radius: var(--radius-2xl);
                padding: var(--spacing-xl);
                box-shadow: var(--shadow-sm);
                border: 1px solid var(--border-light);
                margin-bottom: var(--spacing-xl);
            }

            .filter-row {
                display: flex;
                gap: var(--spacing-md);
                margin-bottom: var(--spacing-md);
                align-items: center;
                flex-wrap: wrap;
            }

            .filter-row .filter-input {
                flex: 1;
                min-width: 200px;
            }

            .filter-row .search-btn {
                flex-shrink: 0;
            }

            /* Mobile Filter Styles */
            @media (max-width: 768px) {
                .filter-controls {
                    padding: var(--spacing-lg);
                }

                .filter-row {
                    flex-direction: column;
                    gap: var(--spacing-sm);
                }

                .filter-row .filter-input {
                    min-width: auto;
                }
            }

            .filter-input {
                flex: 1;
            }

            .filter-buttons {
                display: flex;
                gap: var(--spacing-lg);
                flex-wrap: wrap;
                justify-content: center;
                margin-top: var(--spacing-xl);
                padding: var(--spacing-md) 0;
            }

            .filter-buttons .btn {
                flex: 1;
                min-width: 140px;
                max-width: 180px;
                font-size: 0.9rem;
                padding: 0.75rem 1.5rem;
                font-weight: 600;
                margin: 0 var(--spacing-xs);
            }
        }
        /* Grid Styles */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }

        @media (max-width: 768px) {
            .card-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: var(--spacing-lg);
            right: var(--spacing-lg);
            background: var(--success);
            color: var(--white);
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: 3000;
            font-weight: 500;
        }

        /* Utility Classes */
        .hidden {
            display: none !important;
        }

        .text-center {
            text-align: center;
        }

        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: var(--spacing-sm);
        }

        .gap-4 {
            gap: var(--spacing-md);
        }

        .mb-4 {
            margin-bottom: var(--spacing-md);
        }

        .mb-6 {
            margin-bottom: var(--spacing-lg);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="nav">
            <div class="logo">
                <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBIOLABS Logo">
                <span class="logo-text">CVBIOLABS</span>
            </div>
            <ul class="nav-links">
                <li><a href="#" class="nav-link active" onclick="showSection('dashboard')" id="nav-dashboard-desktop">
                    <i class="fas fa-home"></i> Dashboard
                </a></li>
                <li><a href="#" class="nav-link" onclick="showSection('reports')" id="nav-reports-desktop">
                    <i class="fas fa-file-medical"></i> Lab Reports
                </a></li>
                <li><a href="#" class="nav-link" onclick="showSection('profile')" id="nav-profile-desktop">
                    <i class="fas fa-user-md"></i> Profile
                </a></li>
            </ul>
            <div class="auth-buttons">
                <button class="btn btn-secondary" onclick="handleLogout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </nav>
    </header>

    <!-- Mobile Navigation -->
    <div class="mobile-nav">
        <div class="mobile-nav-items">
            <a href="#" class="mobile-nav-item active" onclick="showSection('dashboard')" id="nav-dashboard">
                <i class="fas fa-home"></i>
                <span>Dashboard</span>
            </a>
            <a href="#" class="mobile-nav-item" onclick="showSection('reports')" id="nav-reports">
                <i class="fas fa-file-medical"></i>
                <span>Reports</span>
            </a>
            <a href="#" class="mobile-nav-item" onclick="showSection('profile')" id="nav-profile">
                <i class="fas fa-user-md"></i>
                <span>Profile</span>
            </a>
            <a href="#" class="mobile-nav-item" onclick="handleLogout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Welcome Section -->
        <div class="dashboard-welcome">
            <div class="welcome-content">
                <h1 class="welcome-text">
                    Hello, <span id="doctorNameDisplay">Doctor</span>!
                </h1>
                <p class="welcome-subtitle">
                    <span id="doctorSpecializationDisplay">Welcome to your professional dashboard</span>
                </p>
            </div>
        </div>

        <!-- Dashboard Section -->
        <section id="dashboard" class="section active">

            <script>
            // Set doctor name and specialization in dashboard header after loading profile
            async function setDoctorHeaderFromProfile() {
                try {
                    const response = await fetch(`${API_URL}/doctors/profile`);
                    if (response.ok) {
                        const profile = await response.json();
                        document.getElementById('doctorNameDisplay').textContent = profile.name || 'Doctor';
                        const specializationEl = document.getElementById('doctorSpecializationDisplay');
                        if (profile.specialization) {
                            specializationEl.textContent = `Specialization: ${profile.specialization}`;
                            specializationEl.style.display = 'block';
                        }
                    }
                } catch (e) { /* ignore */ }
            }
            document.addEventListener('DOMContentLoaded', setDoctorHeaderFromProfile);
            </script>

            <div class="card-grid">
                <div class="stat-card fade-in">
                    <div class="stat-number" id="totalReports">0</div>
                    <div class="stat-label">Total Reports</div>
                </div>
                <div class="stat-card fade-in">
                    <div class="stat-number" id="pendingReports">0</div>
                    <div class="stat-label">Pending Reports</div>
                </div>
                <div class="stat-card fade-in">
                    <div class="stat-number" id="verifiedReports">0</div>
                    <div class="stat-label">Verified Reports</div>
                </div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="section hidden">
            <div class="section-header">
                <h2 class="section-title">Lab Reports</h2>
                <p class="section-subtitle">Review and manage patient lab reports</p>
            </div>

            <div class="reports-section card">
                <div class="filter-controls mb-6">
                    <div class="filter-row">
                        <input type="text" class="filter-input" id="searchInput" placeholder="Search by patient name, test type, or barcode...">
                        <input type="date" class="filter-input" id="dateFilter">
                        <input type="text" class="filter-input" id="barcodeInput" placeholder="Enter barcode...">
                        <button class="btn search-btn" onclick="searchByBarcode()">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                    </div>
                    <br>

                    <div class="filter-buttons">
                        <button class="btn btn-outline filter-btn-pending active" id="filterPending" onclick="setReportStatusFilter('pending')">
                            <i class="fas fa-hourglass-half"></i>
                            Pending
                        </button>
                        <button class="btn btn-outline filter-btn-verified" id="filterVerified" onclick="setReportStatusFilter('verified')">
                            <i class="fas fa-check-circle"></i>
                            Verified
                        </button>
                        <button class="btn btn-outline filter-btn-all" id="filterAll" onclick="setReportStatusFilter('all')">
                            <i class="fas fa-list"></i>
                            All
                        </button>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="reports-table">
                        <thead>
                            <tr>
                                <th>Barcode</th>
                                <th>Patient Name</th>
                                <th>Test Name</th>
                                <th>Test Code</th>
                                <th>Booking Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="reportsTableBody"></tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Profile Section -->
        <section id="profile" class="section hidden">
            <div class="section-header">
                <h2 class="section-title">Doctor Profile</h2>
                <p class="section-subtitle">Manage your professional information</p>
            </div>

            <div class="profile-section card">
                <form id="profileForm" method="POST" action="/api/doctors/profile">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                    <div class="form-group">
                        <label for="doctorName" class="form-label">Full Name</label>
                        <input type="text" id="doctorName" name="name" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="doctorEmail" class="form-label">Email Address</label>
                        <input type="email" id="doctorEmail" name="email" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="specialization" class="form-label">Specialization</label>
                        <input type="text" id="specialization" name="specialization" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="licenseNumber" class="form-label">License Number</label>
                        <input type="text" id="licenseNumber" name="license_number" class="form-input" required>
                    </div>

                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-save"></i>
                        Update Profile
                    </button>
                </form>
            </div>
        </section>
    </div>

    <!-- Report View Modal -->
    <div id="reportViewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xl); border-bottom: 1px solid var(--border-color); flex-shrink: 0;">
                <h2 class="section-title" style="margin: 0; color: var(--primary-color); font-size: 1.5rem; font-weight: 700;">Report Details</h2>
                <button class="btn btn-outline" onclick="closeReportViewModal()" style="padding: 0.5rem; border-radius: 50%; width: 40px; height: 40px; border: 2px solid var(--border-color); background: white; color: var(--text-secondary); transition: all 0.2s ease;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body" style="padding: var(--spacing-xl); overflow-y: auto; flex: 1;">
                <div class="report-details card" style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); padding: var(--spacing-xl); margin-bottom: var(--spacing-lg); border-radius: 12px; border: 1px solid var(--border-color); box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                    <div class="detail-grid" style="display: grid; gap: var(--spacing-lg);">
                        <div class="detail-row" style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid rgba(0,0,0,0.08);">
                            <span class="form-label" style="font-weight: 600; color: var(--text-secondary); font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px;">Barcode:</span>
                            <span id="viewBarcode" class="detail-value" style="font-weight: 700; color: var(--text-primary); font-size: 1rem; font-family: 'Courier New', monospace; background: var(--primary-light); padding: 0.25rem 0.5rem; border-radius: 4px;"></span>
                        </div>
                        <div class="detail-row" style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid rgba(0,0,0,0.08);">
                            <span class="form-label" style="font-weight: 600; color: var(--text-secondary); font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px;">Patient Name:</span>
                            <span id="viewPatientName" class="detail-value" style="font-weight: 600; color: var(--text-primary); font-size: 1rem;"></span>
                        </div>
                        <div class="detail-row" style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid rgba(0,0,0,0.08);">
                            <span class="form-label" style="font-weight: 600; color: var(--text-secondary); font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px;">Test Name:</span>
                            <span id="viewTestName" class="detail-value" style="font-weight: 600; color: var(--text-primary); font-size: 1rem;"></span>
                        </div>
                        <div class="detail-row" style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid rgba(0,0,0,0.08);">
                            <span class="form-label" style="font-weight: 600; color: var(--text-secondary); font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px;">Test Code:</span>
                            <span id="viewTestCode" class="detail-value" style="font-weight: 600; color: var(--text-primary); font-size: 1rem; font-family: 'Courier New', monospace;"></span>
                        </div>
                        <div class="detail-row" style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid rgba(0,0,0,0.08);">
                            <span class="form-label" style="font-weight: 600; color: var(--text-secondary); font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px;">Booking Date:</span>
                            <span id="viewBookingDate" class="detail-value" style="font-weight: 600; color: var(--text-primary); font-size: 1rem;"></span>
                        </div>
                        <div class="detail-row" style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0;">
                            <span class="form-label" style="font-weight: 600; color: var(--text-secondary); font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px;">Status:</span>
                            <span id="viewStatus" class="detail-value status-badge" style="font-weight: 700; font-size: 0.9rem; padding: 0.4rem 0.8rem; border-radius: 20px; text-transform: uppercase; letter-spacing: 0.5px;"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer" style="padding: var(--spacing-lg) var(--spacing-xl); border-top: 1px solid var(--border-color); flex-shrink: 0; background: white; box-shadow: 0 -2px 10px rgba(0,0,0,0.05);">
                <div class="modal-actions" style="display: flex; gap: var(--spacing-lg); justify-content: center;">
                    <button class="btn btn-outline" onclick="closeReportViewModal()" style="padding: 0.875rem 2rem; font-weight: 600; border-radius: 10px; transition: all 0.2s ease; min-width: 120px; border: 2px solid #6b7280; color: #374151; background: white; font-size: 0.95rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-times" style="font-size: 0.9rem;"></i>Close
                    </button>
                    <button class="btn btn-primary" onclick="viewReportFile()" style="padding: 0.875rem 2rem; font-weight: 600; border-radius: 10px; transition: all 0.2s ease; min-width: 140px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; border: none; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4); font-size: 0.95rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-eye" style="font-size: 0.9rem;"></i>View Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Verify Modal -->
    <div id="verifyModal" class="modal">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-6 p-6 pb-0">
                <h2 class="section-title">Verify Report</h2>
                <button class="btn btn-outline" onclick="closeVerifyModal()" style="padding: 0.5rem; border-radius: 50%; width: 40px; height: 40px;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="p-6 pt-0">
                <div class="form-group">
                    <label for="verifyComment" class="form-label">Verification Comment</label>
                    <textarea id="verifyComment" rows="4" class="form-input" placeholder="Enter your verification comments..." required></textarea>
                </div>

                <div class="action-buttons flex justify-center gap-4">
                    <button class="btn btn-outline" onclick="closeVerifyModal()">Cancel</button>
                    <button class="btn btn-secondary" id="submitVerifyBtn">
                        <i class="fas fa-check"></i>
                        Verify Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API URL for doctor endpoints
        const API_URL = window.location.origin + '/doctor';

        // Navigation function to switch between sections
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.remove('active');
                section.classList.add('hidden');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.remove('hidden');
                targetSection.classList.add('active');
            }

            // Update navigation
            const navLinks = document.querySelectorAll('.nav-links .nav-link');
            navLinks.forEach(link => link.classList.remove('active'));
            const activeLink = document.getElementById('nav-' + sectionId + '-desktop');
            if (activeLink) activeLink.classList.add('active');

            // Update mobile navigation
            const mobileNavLinks = document.querySelectorAll('.mobile-nav .mobile-nav-item');
            mobileNavLinks.forEach(link => link.classList.remove('active'));
            const mobileActiveLink = document.getElementById('nav-' + sectionId);
            if (mobileActiveLink) mobileActiveLink.classList.add('active');

            // Section-specific actions
            if (sectionId === 'reports') {
                setReportStatusFilter('pending'); // Always reset to pending
            }
            if (sectionId === 'profile') {
                loadProfile();
            }


        }

        // Format date for display
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = 'notification';

            // Set notification style based on type
            let bgColor = 'var(--success)';
            let icon = 'fas fa-check-circle';

            if (type === 'error') {
                bgColor = 'var(--error)';
                icon = 'fas fa-exclamation-circle';
            } else if (type === 'warning') {
                bgColor = 'var(--warning)';
                icon = 'fas fa-exclamation-triangle';
            } else if (type === 'info') {
                bgColor = 'var(--info)';
                icon = 'fas fa-info-circle';
            }

            notification.style.background = bgColor;
            notification.innerHTML = `
                <i class="${icon}" style="margin-right: var(--spacing-sm);"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            // Auto remove after 4 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 4000);
        }

        // Add slideOutRight animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOutRight {
                to {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Update dashboard statistics
        async function updateStats() {
            try {
                const response = await fetch(`${API_URL}/stats`);
                if (!response.ok) throw new Error('Failed to fetch stats');
                const data = await response.json();
                
                document.getElementById('totalReports').textContent = data.total_reports || 0;
                document.getElementById('pendingReports').textContent = data.pending_reports || 0;
                document.getElementById('verifiedReports').textContent = data.verified_reports || 0;
            } catch (error) {
                console.error('Error fetching stats:', error);
                document.getElementById('totalReports').textContent = '0';
                document.getElementById('pendingReports').textContent = '0';
                document.getElementById('verifiedReports').textContent = '0';
            }
        }

        // Fetch and render reports
        async function fetchAndRenderReports(searchTerm = '', dateFilter = '', barcode = '', status = '') {
            try {
                let url = `${API_URL}/reports`;
                const params = new URLSearchParams();
                if (searchTerm) params.append('search', searchTerm);
                if (dateFilter) params.append('date', dateFilter);
                if (barcode) params.append('barcode', barcode);
                if (status) params.append('status', status);
                if (params.toString()) url += `?${params.toString()}`;
                
                const response = await fetch(url);
                if (!response.ok) throw new Error('Failed to fetch reports');
                const reports = await response.json();
                console.log('Fetched reports:', reports); // Debug log
                renderReportsTable(reports);
            } catch (error) {
                showNotification('Error fetching reports');
                console.error('Error:', error);
            }
        }

        // Status filter logic
        let currentStatusFilter = 'pending';
        function setReportStatusFilter(status) {
            currentStatusFilter = status;

            // Reset all filter buttons
            const filterButtons = [
                { id: 'filterPending', baseClass: 'btn btn-outline filter-btn-pending' },
                { id: 'filterVerified', baseClass: 'btn btn-outline filter-btn-verified' },
                { id: 'filterAll', baseClass: 'btn btn-outline filter-btn-all' }
            ];

            filterButtons.forEach(buttonInfo => {
                const button = document.getElementById(buttonInfo.id);
                if (button) {
                    button.className = buttonInfo.baseClass;
                }
            });

            // Set active button
            const activeButton = document.getElementById('filter' + status.charAt(0).toUpperCase() + status.slice(1));
            if (activeButton) {
                activeButton.classList.add('active');
            }

            // Fetch reports with new filter
            fetchAndRenderReports(
                document.getElementById('searchInput').value,
                document.getElementById('dateFilter').value,
                document.getElementById('barcodeInput').value,
                status
            );
        }

        // Render reports table
        function renderReportsTable(reports) {
            const tbody = document.getElementById('reportsTableBody');
            if (!reports || reports.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center" style="padding: var(--spacing-2xl); color: var(--text-muted);">
                            <i class="fas fa-file-medical" style="font-size: 3rem; margin-bottom: var(--spacing-md); opacity: 0.3;"></i>
                            <div>No reports assigned.</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = reports.map(report => {
                const status = report.report_status || 'Unknown';
                const patientName = report.first_name ? `${report.first_name} ${report.last_name}` : report.patient_name || 'N/A';
                const bookingDate = formatDate(report.booking_date);
                const appointmentTime = report.appointment_time || '';

                return `
                    <tr>
                        <td data-label="Barcode">${report.barcode || 'N/A'}</td>
                        <td data-label="Patient Name">${patientName}</td>
                        <td data-label="Test Name">${report.test_name || 'N/A'}</td>
                        <td data-label="Test Code">${report.test_code || 'N/A'}</td>
                        <td data-label="Booking Date">${bookingDate} ${appointmentTime}</td>
                        <td data-label="Status">
                            <span class="status status-${status.toLowerCase()}">${status}</span>
                        </td>
                        <td data-label="Actions">
                            <button class="btn btn-primary" onclick="viewReportDetails(${report.id})" style="margin-right: var(--spacing-xs);">
                                <i class="fas fa-eye"></i>
                                View
                            </button>
                            ${status === 'Pending' ?
                                `<button class="btn btn-secondary" onclick="showVerifyModal(${report.id})">
                                    <i class="fas fa-check"></i>
                                    Verify
                                </button>` :
                                status === 'Verified' ?
                                `<button class="btn btn-secondary" onclick="showEditReportModal(${report.id})">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>` : ''
                            }
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Show edit modal for verified reports
        function showEditReportModal(reportId) {
            viewReportDetails(reportId, true);
        }

        // View report details (with edit option for verified)
        async function viewReportDetails(id, editable = false) {
            try {
                const response = await fetch(`${API_URL}/reports/${id}/view`);
                if (response.ok) {
                    const report = await response.json();
                    const modal = document.getElementById('reportViewModal');
                    document.getElementById('viewBarcode').textContent = report.barcode || 'N/A';
                    document.getElementById('viewPatientName').textContent = 
                        report.first_name ? `${report.first_name} ${report.last_name}` : report.patient_name || 'N/A';
                    document.getElementById('viewTestName').textContent = report.test_name || 'N/A';
                    document.getElementById('viewTestCode').textContent = report.test_code || 'N/A';
                    document.getElementById('viewBookingDate').textContent = 
                        `${formatDate(report.booking_date)} ${report.appointment_time || ''}`;
                    const statusElement = document.getElementById('viewStatus');
                    const status = report.report_status || 'Unknown';
                    statusElement.textContent = status;
                    statusElement.className = `detail-value status-badge status-${status.toLowerCase()}`;
                    modal.dataset.reportId = id;

                    // Remove reference to verifyReportBtn (button was removed from modal)
                    // Add edit fields for verified reports
                    let editFields = document.getElementById('editFields');
                    if (!editFields) {
                        editFields = document.createElement('div');
                        editFields.id = 'editFields';
                        editFields.className = 'mt-4';
                        modal.querySelector('.report-details').after(editFields);
                    }
                    if (report.report_status === 'Verified' && editable) {
                        editFields.innerHTML = `
                            <div class="edit-section" style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: var(--spacing-lg); border-radius: 12px; border: 1px solid #f59e0b; margin-bottom: var(--spacing-lg);">
                                <h3 style="color: #92400e; font-weight: 700; font-size: 1.1rem; margin-bottom: var(--spacing-md); display: flex; align-items: center;">
                                    <i class="fas fa-edit mr-2"></i>Edit Report Details
                                </h3>
                                <div class="form-group" style="margin-bottom: var(--spacing-md);">
                                    <label for="editDoctorReview" class="form-label" style="font-weight: 600; color: #92400e; font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: var(--spacing-sm); display: block;">Doctor Review</label>
                                    <textarea id="editDoctorReview" rows="3" style="width: 100%; padding: var(--spacing-md); border: 2px solid #f59e0b; border-radius: 8px; font-size: 0.95rem; line-height: 1.5; resize: vertical; transition: all 0.2s ease; background: white;" placeholder="Enter your professional review...">${report.doctor_review || ''}</textarea>
                                </div>
                                <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                                    <label for="editComments" class="form-label" style="font-weight: 600; color: #92400e; font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: var(--spacing-sm); display: block;">Additional Comments</label>
                                    <textarea id="editComments" rows="2" style="width: 100%; padding: var(--spacing-md); border: 2px solid #f59e0b; border-radius: 8px; font-size: 0.95rem; line-height: 1.5; resize: vertical; transition: all 0.2s ease; background: white;" placeholder="Add any additional comments...">${report.comments || ''}</textarea>
                                </div>
                                <div class="edit-actions" style="display: flex; gap: var(--spacing-md); justify-content: flex-end;">
                                    <button class="btn btn-outline" onclick="closeReportViewModal()" style="padding: 0.6rem 1.2rem; font-weight: 600; border-radius: 8px; transition: all 0.2s ease; min-width: 90px; border-color: #f59e0b; color: #92400e; font-size: 0.9rem;">
                                        <i class="fas fa-times mr-1"></i>Cancel
                                    </button>
                                    <button class="btn" onclick="submitEditReport(${id})" style="padding: 0.6rem 1.2rem; font-weight: 600; border-radius: 8px; transition: all 0.2s ease; min-width: 100px; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; border: none; box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3); font-size: 0.9rem;">
                                        <i class="fas fa-save mr-1"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        `;
                    } else {
                        editFields.innerHTML = '';
                    }
                    modal.style.display = 'block';
                } else {
                    throw new Error('Failed to load report details');
                }
            } catch (error) {
                showNotification('Error loading report details');
                console.error('Error:', error);
            }
        }

        // Submit edit for verified report
        async function submitEditReport(reportId) {
            const doctorReview = document.getElementById('editDoctorReview').value;
            const comments = document.getElementById('editComments').value;
            try {
                const response = await fetch(`${API_URL}/reports/${reportId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ doctor_review: doctorReview, comments })
                });
                if (response.ok) {
                    showNotification('Report updated successfully');
                    closeReportViewModal();
                    await fetchAndRenderReports(
                        document.getElementById('searchInput').value,
                        document.getElementById('dateFilter').value,
                        document.getElementById('barcodeInput').value,
                        currentStatusFilter
                    );
                } else {
                    const error = await response.json();
                    showNotification(error.error || 'Failed to update report');
                }
            } catch (error) {
                showNotification('Failed to update report');
                console.error('Error:', error);
            }
        }

        // View report file
        function viewReportFile() {
            const reportId = document.getElementById('reportViewModal').dataset.reportId;
            window.open(`${API_URL}/reports/${reportId}/file`, '_blank');
        }

        // Close report view modal
        function closeReportViewModal() {
            document.getElementById('reportViewModal').style.display = 'none';
        }

        // Show verify modal
        async function showVerifyModal(id) {
            try {
                const modal = document.getElementById('verifyModal');
                const commentInput = document.getElementById('verifyComment');
                
                commentInput.value = '';
                modal.dataset.reportId = id;
                
                const verifyBtn = document.getElementById('submitVerifyBtn');
                verifyBtn.onclick = async () => {
                    const comment = commentInput.value.trim();
                    if (!comment) {
                        showNotification('Please enter a verification comment');
                        return;
                    }
                    
                    try {
                        // Get CSRF token from response headers
                        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                        
                        // Log the request data
                        console.log('Sending verification request:', {
                            reportId: id,
                            comment: comment,
                            csrfToken: csrfToken
                        });

                        const response = await fetch(`${API_URL}/reports/${id}/verify`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRF-Token': csrfToken
                            },
                            credentials: 'include',
                            body: JSON.stringify({
                                comment: comment
                            })
                        });
                        
                        // Log the raw response
                        console.log('Response status:', response.status);
                        const responseText = await response.text();
                        console.log('Raw response:', responseText);
                        
                        let data;
                        try {
                            data = JSON.parse(responseText);
                        } catch (e) {
                            console.error('Failed to parse response as JSON:', e);
                            showNotification('Invalid response from server');
                            return;
                        }
                        
                        if (response.ok) {
                            showNotification('Report verified successfully');
                            closeVerifyModal();
                            await fetchAndRenderReports(
                                document.getElementById('searchInput').value,
                                document.getElementById('dateFilter').value,
                                document.getElementById('barcodeInput').value,
                                currentStatusFilter
                            );
                            await updateStats();
                        } else {
                            showNotification(data.error || 'Error verifying report');
                            console.error('Verification error:', data);
                        }
                    } catch (error) {
                        showNotification('Error verifying report');
                        console.error('Error:', error);
                    }
                };
                
                modal.style.display = 'block';
            } catch (error) {
                showNotification('Error loading verification modal');
                console.error('Error:', error);
            }
        }

        // Close verify modal
        function closeVerifyModal() {
            document.getElementById('verifyModal').style.display = 'none';
        }

        // Profile management functions
        async function loadProfile() {
            try {
                const response = await fetch(`${API_URL}/doctors/profile`);
                if (response.status === 401) {
                    window.location.href = '/login';
                    return;
                }
                if (response.ok) {
                    const profile = await response.json();
                    document.getElementById('doctorName').value = profile.name || '';
                    document.getElementById('doctorEmail').value = profile.email || '';
                    document.getElementById('specialization').value = profile.specialization || '';
                    document.getElementById('licenseNumber').value = profile.license_number || '';
                } else {
                    throw new Error('Failed to load profile');
                }
            } catch (error) {
                showNotification('Error loading profile');
                console.error('Error:', error);
            }
        }

        // Update profile form submission
        document.getElementById('profileForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const profileData = {
                name: document.getElementById('doctorName').value,
                email: document.getElementById('doctorEmail').value,
                specialization: document.getElementById('specialization').value,
                license_number: document.getElementById('licenseNumber').value
            };

            try {
                const response = await fetch(`${API_URL}/doctors/profile`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(profileData)
                });
                
                if (response.ok) {
                    showNotification('Profile updated successfully');
                } else {
                    const error = await response.json();
                    showNotification(`Error: ${error.error}`);
                }
            } catch (error) {
                showNotification('Error updating profile');
                console.error('Error:', error);
            }
        });

        // Search by barcode
        async function searchByBarcode() {
            const barcode = document.getElementById('barcodeInput').value.trim();
            await fetchAndRenderReports('', '', barcode);
        }

        // Add smooth scrolling for mobile
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling behavior
            document.documentElement.style.scrollBehavior = 'smooth';

            // Add touch feedback for mobile buttons
            const buttons = document.querySelectorAll('.btn, .mobile-nav-item, .nav-link');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });
                button.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });
        });

        // Handle logout
        async function handleLogout() {
            try {
                const response = await fetch('/doctor/logout');
                if (response.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                console.error('Error during logout:', error);
                window.location.href = '/login';
            }
        }

        // Add function to get CSRF token from response headers
        async function getCsrfToken() {
            try {
                const response = await fetch('/');
                return response.headers.get('X-CSRF-Token');
            } catch (error) {
                console.error('Error getting CSRF token:', error);
                return null;
            }
        }

        // Update the fetch override to include CSRF token
        const _fetch = window.fetch;
        window.fetch = async function(url, options = {}) {
            // Don't add auth header for login/logout routes
            if (!url.includes('/login') && !url.includes('/logout')) {
                options.credentials = 'include';  // Include cookies in requests
                
                // Add CSRF token for non-GET requests
                if (options.method && options.method !== 'GET') {
                    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                    if (csrfToken) {
                        options.headers = {
                            ...options.headers,
                            'X-CSRF-Token': csrfToken
                        };
                    }
                }
            }
            return _fetch(url, options);
        };

        // Initialize dashboard and set pending filter on page load
        async function initializeDashboard() {
            try {
                // Load initial data
                await updateStats();
                await loadProfile();

                // Set up event listeners
                const searchInput = document.getElementById('searchInput');
                const dateFilter = document.getElementById('dateFilter');

                if (searchInput) {
                    searchInput.addEventListener('input', function(e) {
                        fetchAndRenderReports(e.target.value, dateFilter?.value || '', '', currentStatusFilter);
                    });
                }

                if (dateFilter) {
                    dateFilter.addEventListener('change', function(e) {
                        fetchAndRenderReports(searchInput?.value || '', e.target.value, '', currentStatusFilter);
                    });
                }

                // Initialize responsive behavior
                handleResize();
                window.addEventListener('resize', handleResize);

                // Show welcome message
                showNotification('Welcome to your dashboard!', 'info');

            } catch (error) {
                console.error('Error initializing dashboard:', error);
                if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                    window.location.href = '/login';
                } else {
                    showNotification('Error loading dashboard data', 'error');
                }
            }
        }

        // Handle responsive behavior
        function handleResize() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth < 1024) {
                if (sidebar) sidebar.classList.add('sidebar-hidden');
            } else {
                if (sidebar) sidebar.classList.remove('sidebar-hidden');
            }
        }

        document.addEventListener('DOMContentLoaded', initializeDashboard);
    </script>
</body>
</html>